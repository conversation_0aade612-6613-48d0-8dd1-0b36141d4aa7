/* Faroese initialisation for the jQuery UI date picker plugin */
/* Written by <PERSON><PERSON><PERSON>, <EMAIL> */
jQuery(function($){
	$.datepicker.regional['fo'] = {
		closeText: 'Lat aftur',
		prevText: '&#x3C;Fyrra',
		nextText: 'Næsta&#x3E;',
		currentText: 'Í dag',
		monthNames: ['<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON>','<PERSON><PERSON><PERSON>','<PERSON>','Juni',
		'Juli','August','September','Okto<PERSON>','November','Desember'],
		monthNamesShort: ['Jan','Feb','Mar','Apr','<PERSON>','Jun',
		'Jul','Aug','Sep','Okt','Nov','<PERSON>'],
		dayNames: ['<PERSON>nudagur','<PERSON><PERSON><PERSON>gur','Týsdagur','<PERSON><PERSON><PERSON>gu<PERSON>','<PERSON><PERSON><PERSON>gu<PERSON>','Fr<PERSON>ggjadagu<PERSON>','<PERSON><PERSON>agu<PERSON>'],
		dayNamesShort: ['<PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>'],
		dayNamesMin: ['<PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON>','Hó','Fr','Le'],
		weekHeader: 'Vk',
		dateFormat: 'dd-mm-yy',
		firstDay: 1,
		isRTL: false,
		showMonthAfterYear: false,
		yearSuffix: ''};
	$.datepicker.setDefaults($.datepicker.regional['fo']);
});
