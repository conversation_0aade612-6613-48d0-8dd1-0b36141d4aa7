@echo off
echo ========================================
echo SSL Certificate Generation for Local Development
echo ========================================
echo.

echo This script will generate a self-signed SSL certificate for local development.
echo.

set OPENSSL_PATH=E:\tools\xampp\apache\bin\openssl.exe
set CERT_DIR=E:\tools\xampp\apache\conf\ssl.crt
set KEY_DIR=E:\tools\xampp\apache\conf\ssl.key

echo Checking OpenSSL...
if not exist "%OPENSSL_PATH%" (
    echo ERROR: OpenSSL not found at %OPENSSL_PATH%
    echo Please check your XAMPP installation.
    pause
    exit /b 1
)

echo Creating certificate directories...
if not exist "%CERT_DIR%" mkdir "%CERT_DIR%"
if not exist "%KEY_DIR%" mkdir "%KEY_DIR%"

echo.
echo Generating SSL certificate for localhost.cadservices...
echo.

cd /d "E:\tools\xampp\apache\bin"

REM Set OpenSSL configuration file path
set OPENSSL_CONF=E:\tools\xampp\apache\conf\openssl.cnf

REM Check if OpenSSL config exists, if not use a basic one
if not exist "%OPENSSL_CONF%" (
    echo OpenSSL config not found, using minimal configuration...
    set OPENSSL_CONF=
)

REM Generate certificate with proper configuration
if defined OPENSSL_CONF (
    openssl req -x509 -nodes -days 365 -newkey rsa:2048 ^
        -keyout "%KEY_DIR%\localhost.key" ^
        -out "%CERT_DIR%\localhost.crt" ^
        -config "%OPENSSL_CONF%" ^
        -subj "/C=GB/ST=Local/L=Local/O=Local Development/OU=IT Department/CN=localhost.cadservices"
) else (
    openssl req -x509 -nodes -days 365 -newkey rsa:2048 ^
        -keyout "%KEY_DIR%\localhost.key" ^
        -out "%CERT_DIR%\localhost.crt" ^
        -subj "/C=GB/ST=Local/L=Local/O=Local Development/OU=IT Department/CN=localhost.cadservices"
)

if %ERRORLEVEL% EQU 0 (
    echo.
    echo SSL Certificate generated successfully!
    echo Certificate: %CERT_DIR%\localhost.crt
    echo Private Key: %KEY_DIR%\localhost.key
    echo.
    echo You can now restart Apache to use the SSL certificate.
) else (
    echo.
    echo ERROR: Failed to generate SSL certificate.
    echo Please check the OpenSSL installation and try again.
)

echo.
pause
