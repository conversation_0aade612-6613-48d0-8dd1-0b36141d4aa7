/**
 * Alpine.js Components for HTMX Transitions
 * This file contains Alpine.js components and utilities for smooth transitions
 * when HTMX performs DOM updates (inserts, updates, deletes)
 */

// Alpine.js component for table rows with transition support
function tableRow() {
    return {
        show: true,
        
        // Initialize the row (called when row is created)
        init() {
            // Start hidden for enter transitions
            this.show = false;
            this.$nextTick(() => {
                this.show = true;
            });
        },
        
        // Remove the row with transition
        remove() {
            this.show = false;
            // Wait for transition to complete before removing from DOM
            setTimeout(() => {
                if (this.$el && this.$el.parentNode) {
                    this.$el.parentNode.removeChild(this.$el);
                }
            }, 300); // Match transition duration
        },
        
        // Update the row with transition
        update() {
            // Add a brief highlight effect
            this.$el.classList.add('table-row-updated');
            setTimeout(() => {
                this.$el.classList.remove('table-row-updated');
            }, 1000);
        }
    }
}

// Alpine.js component for alert messages
function alertMessage() {
    return {
        show: true,
        
        init() {
            this.show = false;
            this.$nextTick(() => {
                this.show = true;
            });
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
                this.hide();
            }, 5000);
        },
        
        hide() {
            this.show = false;
            setTimeout(() => {
                if (this.$el && this.$el.parentNode) {
                    this.$el.parentNode.removeChild(this.$el);
                }
            }, 300);
        }
    }
}

// Alpine.js component for form elements
function formElement() {
    return {
        show: true,
        
        init() {
            this.show = false;
            this.$nextTick(() => {
                this.show = true;
            });
        },
        
        fadeOut() {
            this.show = false;
        },
        
        fadeIn() {
            this.show = true;
        }
    }
}

// Global Alpine.js data - wait for Alpine.js to be available
function initializeAlpineComponents() {
    if (typeof Alpine !== 'undefined') {
        Alpine.data('tableRow', tableRow);
        Alpine.data('alertMessage', alertMessage);
        Alpine.data('formElement', formElement);
        console.log('✅ Alpine.js transitions components registered');
    } else {
        // Alpine.js not ready yet, wait a bit and try again
        setTimeout(initializeAlpineComponents, 100);
    }
}

// Try to initialize immediately, or wait for Alpine.js
if (typeof Alpine !== 'undefined') {
    initializeAlpineComponents();
} else {
    // Wait for Alpine.js to be available
    document.addEventListener('alpine:init', () => {
        initializeAlpineComponents();
    });

    // Fallback: check periodically if Alpine.js becomes available
    setTimeout(initializeAlpineComponents, 100);
}

// HTMX event listeners for enhanced transitions
document.addEventListener('htmx:beforeSwap', function(evt) {
    // Add loading state to target element
    if (evt.detail.target) {
        evt.detail.target.classList.add('htmx-loading');
    }
});

document.addEventListener('htmx:afterSwap', function(evt) {
    // Remove loading state
    if (evt.detail.target) {
        evt.detail.target.classList.remove('htmx-loading');
    }

    // Initialize Alpine.js on new elements (only if Alpine.js is available)
    if (typeof Alpine !== 'undefined') {
        if (evt.detail.target.hasAttribute('x-data')) {
            Alpine.initTree(evt.detail.target);
        }

        // Initialize Alpine.js on child elements with x-data
        const alpineElements = evt.detail.target.querySelectorAll('[x-data]');
        alpineElements.forEach(el => {
            if (!el._x_dataStack) {
                Alpine.initTree(el);
            }
        });
    }
});

document.addEventListener('htmx:beforeRequest', function(evt) {
    // Add request indicator
    const indicator = document.querySelector('#indicatorLines');
    if (indicator) {
        indicator.style.display = 'block';
    }
});

document.addEventListener('htmx:afterRequest', function(evt) {
    // Hide request indicator
    const indicator = document.querySelector('#indicatorLines');
    if (indicator) {
        indicator.style.display = 'none';
    }
});

// Utility function to trigger Alpine.js transitions manually
window.triggerAlpineTransition = function(selector, action = 'remove') {
    const element = document.querySelector(selector);
    if (element && element.__x) {
        if (typeof element.__x.$data[action] === 'function') {
            element.__x.$data[action]();
        }
    }
};

// Enhanced HTMX swap for better Alpine.js integration
document.addEventListener('htmx:beforeSwap', function(evt) {
    // If swapping out an Alpine.js component, trigger its removal transition (only if Alpine.js is available)
    if (typeof Alpine !== 'undefined' && evt.detail.target.hasAttribute('x-data') && evt.detail.target.__x) {
        const component = evt.detail.target.__x.$data;
        if (typeof component.remove === 'function') {
            component.remove();
            // Delay the actual swap to allow transition
            evt.detail.shouldSwap = false;
            setTimeout(() => {
                evt.detail.target.outerHTML = evt.detail.serverResponse;
                // Initialize Alpine.js on the new content
                const newElement = document.querySelector(`#${evt.detail.target.id}`);
                if (newElement && newElement.hasAttribute('x-data')) {
                    Alpine.initTree(newElement);
                }
            }, 300);
        }
    }
});

// Debug logging (can be removed in production)
if (typeof console !== 'undefined') {
    document.addEventListener('alpine:init', () => {
        console.log('✅ Alpine.js initialized');
    });

    document.addEventListener('htmx:afterSwap', (e) => {
        console.log('🔄 HTMX swap completed, Alpine.js re-initialized');
    });
}
