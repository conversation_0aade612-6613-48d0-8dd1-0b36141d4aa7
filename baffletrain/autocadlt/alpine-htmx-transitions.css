/**
 * Alpine.js + HTMX Transition Styles
 * CSS classes for smooth transitions when HTMX performs DOM updates
 */

/* Table Row Enter Transitions */
.table-row-enter-active {
    transition: all 0.3s ease-out;
}

.table-row-enter {
    opacity: 0;
    transform: translateY(-10px);
    background-color: #e8f5e8;
}

.table-row-enter-to {
    opacity: 1;
    transform: translateY(0);
    background-color: transparent;
}

/* Table Row Leave Transitions */
.table-row-leave-active {
    transition: all 0.3s ease-in;
}

.table-row-leave {
    opacity: 1;
    transform: translateY(0);
    background-color: transparent;
}

.table-row-leave-to {
    opacity: 0;
    transform: translateY(-10px);
    background-color: #ffe6e6;
}

/* Table Row Update Effect */
.table-row-updated {
    background-color: #fff3cd !important;
    transition: background-color 1s ease;
}

/* Alert Message Enter Transitions */
.alert-enter-active {
    transition: all 0.3s ease-out;
}

.alert-enter {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
}

.alert-enter-to {
    opacity: 1;
    transform: translateY(0) scale(1);
}

/* Alert Message Leave Transitions */
.alert-leave-active {
    transition: all 0.3s ease-in;
}

.alert-leave {
    opacity: 1;
    transform: translateY(0) scale(1);
}

.alert-leave-to {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
}

/* Form Element Enter Transitions */
.form-enter-active {
    transition: all 0.3s ease-out;
}

.form-enter {
    opacity: 0;
    transform: translateX(-20px);
}

.form-enter-to {
    opacity: 1;
    transform: translateX(0);
}

.form-leave-active {
    transition: all 0.3s ease-in;
}

.form-leave {
    opacity: 1;
    transform: translateX(0);
}

.form-leave-to {
    opacity: 0;
    transform: translateX(20px);
}

/* HTMX Loading States */
.htmx-loading {
    position: relative;
    pointer-events: none;
}

.htmx-loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.7);
    z-index: 1000;
}

.htmx-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #007bff;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: htmx-spin 1s linear infinite;
    z-index: 1001;
}

@keyframes htmx-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Table Styles for Better Transitions */
.table-striped tbody tr {
    transition: background-color 0.2s ease;
}

.table-striped tbody tr:hover {
    background-color: #f8f9fa !important;
}

/* Smooth transitions for table cells */
.table td, .table th {
    transition: all 0.2s ease;
}

/* Success/Error State Transitions */
.table-row-success {
    background-color: #d4edda !important;
    border-color: #c3e6cb !important;
    animation: success-pulse 0.6s ease-out;
}

.table-row-error {
    background-color: #f8d7da !important;
    border-color: #f5c6cb !important;
    animation: error-pulse 0.6s ease-out;
}

@keyframes success-pulse {
    0% { background-color: #28a745; }
    100% { background-color: #d4edda; }
}

@keyframes error-pulse {
    0% { background-color: #dc3545; }
    100% { background-color: #f8d7da; }
}

/* Fade transitions for general elements */
.fade-enter-active, .fade-leave-active {
    transition: opacity 0.3s ease;
}

.fade-enter, .fade-leave-to {
    opacity: 0;
}

/* Slide transitions */
.slide-enter-active {
    transition: all 0.3s ease-out;
}

.slide-leave-active {
    transition: all 0.3s ease-in;
}

.slide-enter {
    transform: translateX(-100%);
    opacity: 0;
}

.slide-leave-to {
    transform: translateX(100%);
    opacity: 0;
}

/* Scale transitions for modals/popups */
.scale-enter-active, .scale-leave-active {
    transition: all 0.3s ease;
}

.scale-enter, .scale-leave-to {
    opacity: 0;
    transform: scale(0.9);
}

/* Bounce effect for important updates */
.bounce-enter-active {
    animation: bounce-in 0.5s ease-out;
}

@keyframes bounce-in {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
    }
    70% {
        transform: scale(0.9);
        opacity: 0.9;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Utility classes for manual transitions */
.transition-all {
    transition: all 0.3s ease;
}

.transition-opacity {
    transition: opacity 0.3s ease;
}

.transition-transform {
    transition: transform 0.3s ease;
}

/* Hide elements during Alpine.js initialization */
[x-cloak] {
    display: none !important;
}
