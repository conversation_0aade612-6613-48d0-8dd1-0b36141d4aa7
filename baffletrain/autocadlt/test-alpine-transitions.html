<!DOCTYPE html>
<html>
<head>
    <title>Alpine.js Transitions Test</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- Bootstrap for styling -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@2.0.2"></script>
    
    <!-- Alpine.js Transitions CSS -->
    <link rel="stylesheet" href="alpine-htmx-transitions.css">
    
    <!-- Alpine.js - MUST be loaded before our transitions script -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
</head>
<body>
    <div class="container mt-4">
        <h1>Alpine.js Transitions Test</h1>
        <p>This page tests if Alpine.js transitions are working correctly.</p>
        
        <!-- Test 1: Basic Alpine.js functionality -->
        <div class="card mb-4">
            <div class="card-header">
                <h3>Test 1: Basic Alpine.js</h3>
            </div>
            <div class="card-body">
                <div x-data="{ count: 0 }">
                    <button @click="count++" class="btn btn-primary">Count: <span x-text="count"></span></button>
                    <p x-show="count > 0" x-transition class="mt-2 alert alert-info">
                        You clicked <span x-text="count"></span> times!
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Test 2: Table Row Component -->
        <div class="card mb-4">
            <div class="card-header">
                <h3>Test 2: Table Row Transitions</h3>
            </div>
            <div class="card-body">
                <button onclick="addTestRow()" class="btn btn-success mb-3">Add Row</button>
                
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="test-table-body">
                        <tr x-data="tableRow()" x-show="show" 
                            x-transition:enter="table-row-enter-active"
                            x-transition:enter-start="table-row-enter"
                            x-transition:enter-end="table-row-enter-to"
                            x-transition:leave="table-row-leave-active"
                            x-transition:leave-start="table-row-leave"
                            x-transition:leave-end="table-row-leave-to">
                            <td>1</td>
                            <td>Test Item</td>
                            <td>Active</td>
                            <td>
                                <button @click="remove()" class="btn btn-sm btn-danger">
                                    Remove
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Test 3: Alert Message Component -->
        <div class="card mb-4">
            <div class="card-header">
                <h3>Test 3: Alert Message Transitions</h3>
            </div>
            <div class="card-body">
                <button onclick="showAlert()" class="btn btn-warning">Show Alert</button>
                <div id="alert-container" class="mt-3"></div>
            </div>
        </div>
        
        <!-- Test 4: Form Element Component -->
        <div class="card mb-4">
            <div class="card-header">
                <h3>Test 4: Form Element Transitions</h3>
            </div>
            <div class="card-body">
                <div x-data="{ showForm: true }">
                    <button @click="showForm = !showForm" class="btn btn-info mb-3">Toggle Form</button>
                    
                    <div x-show="showForm" x-data="formElement()" x-show="show"
                         x-transition:enter="form-enter-active"
                         x-transition:enter-start="form-enter"
                         x-transition:enter-end="form-enter-to"
                         x-transition:leave="form-leave-active"
                         x-transition:leave-start="form-leave"
                         x-transition:leave-end="form-leave-to">
                        <form class="border p-3 rounded">
                            <div class="mb-3">
                                <label class="form-label">Name:</label>
                                <input type="text" class="form-control" placeholder="Enter name">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Email:</label>
                                <input type="email" class="form-control" placeholder="Enter email">
                            </div>
                            <button type="submit" class="btn btn-primary">Submit</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Alpine.js Transitions JavaScript - loaded after Alpine.js is initialized -->
    <script src="alpine-htmx-transitions.js"></script>
    
    <script>
        let rowCounter = 2;
        
        function addTestRow() {
            const tbody = document.getElementById('test-table-body');
            const newRow = document.createElement('tr');
            newRow.setAttribute('x-data', 'tableRow()');
            newRow.setAttribute('x-show', 'show');
            newRow.setAttribute('x-transition:enter', 'table-row-enter-active');
            newRow.setAttribute('x-transition:enter-start', 'table-row-enter');
            newRow.setAttribute('x-transition:enter-end', 'table-row-enter-to');
            newRow.setAttribute('x-transition:leave', 'table-row-leave-active');
            newRow.setAttribute('x-transition:leave-start', 'table-row-leave');
            newRow.setAttribute('x-transition:leave-end', 'table-row-leave-to');
            
            newRow.innerHTML = `
                <td>${rowCounter}</td>
                <td>Test Item ${rowCounter}</td>
                <td>Active</td>
                <td>
                    <button @click="remove()" class="btn btn-sm btn-danger">
                        Remove
                    </button>
                </td>
            `;
            
            tbody.appendChild(newRow);
            
            // Initialize Alpine.js on the new row
            if (typeof Alpine !== 'undefined') {
                Alpine.initTree(newRow);
            }
            
            rowCounter++;
        }
        
        function showAlert() {
            const container = document.getElementById('alert-container');
            const alert = document.createElement('div');
            alert.className = 'alert alert-warning';
            alert.setAttribute('x-data', 'alertMessage()');
            alert.setAttribute('x-show', 'show');
            alert.setAttribute('x-transition:enter', 'alert-enter-active');
            alert.setAttribute('x-transition:enter-start', 'alert-enter');
            alert.setAttribute('x-transition:enter-end', 'alert-enter-to');
            alert.setAttribute('x-transition:leave', 'alert-leave-active');
            alert.setAttribute('x-transition:leave-start', 'alert-leave');
            alert.setAttribute('x-transition:leave-end', 'alert-leave-to');
            
            alert.innerHTML = `
                <strong>Test Alert!</strong> This alert will auto-hide after 5 seconds.
                <button @click="hide()" class="btn-close float-end" aria-label="Close"></button>
            `;
            
            container.appendChild(alert);
            
            // Initialize Alpine.js on the new alert
            if (typeof Alpine !== 'undefined') {
                Alpine.initTree(alert);
            }
        }
    </script>
</body>
</html>
