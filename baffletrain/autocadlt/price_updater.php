<?php
/*
  $Id: price_updater.php,v 1.1 2004/04/18 jck Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2004 osCommerce


  Released under the GNU General Public License
*/


require('includes/application_top.php');

$languages = tep_get_languages();
$languages_array = array();
$languages_selected = DEFAULT_LANGUAGE;
for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
	$languages_array[] = array(
		'id' => $languages[$i]['code'],
		'text' => $languages[$i]['name']
	);
	if ($languages[$i]['directory'] == $language) {
		$languages_selected = $languages[$i]['code'];
	}
}


////
// Recursively go through the categories and retreive all sub-categories IDs
// TABLES: categories

function pud_get_sub_categories(&$categories, $categories_id)
{
	$sub_categories_query = tep_db_query("select categories_id from categories where parent_id = '" . (int)$categories_id . "'");
	while ($sub_categories = tep_db_fetch_array($sub_categories_query)) {
		if ($sub_categories['categories_id'] == 0) return true;
		$categories[sizeof($categories)] = $sub_categories['categories_id'];
		if ($sub_categories['categories_id'] != $categories_id) {
			pud_get_sub_categories($categories, $sub_categories['categories_id']);
		}
	}
}


// Functions to fill the dropdown boxes
function tep_get_manufacturers($manufacturers_array = '')
{ // Function borrowed from the Catalog side
	if (!is_array($manufacturers_array)) $manufacturers_array = array();
	$manufacturers_query = tep_db_query("select manufacturers_id, manufacturers_name from manufacturers order by manufacturers_name");
	while ($manufacturers = tep_db_fetch_array($manufacturers_query)) {
		$manufacturers_array[] = array('id' => $manufacturers['manufacturers_id'], 'text' => $manufacturers['manufacturers_name']);
	}
	return $manufacturers_array;
}

/*function tep_get_categories($categories_array = '') { // Function modified from tep_get_manufacturers()
    global $language;
    if (!is_array($categories_array)) $categories_array = array();
    $categories_query = tep_db_query("SELECT categories_id, 
	                                         categories_name
                                      FROM categories_description cd, 
                                           languages l 
									  WHERE l.languages_id = cd.language_id
									    AND l.name = '" . $language . "' 
									  ORDER BY categories_name"
									);
    while ($categories = tep_db_fetch_array($categories_query)) {
      $categories_array[] = array('id' => $categories['categories_id'], 'text' => $categories['categories_name']);
    }
    return $categories_array;
  }*/


function tep_get_models($models_array = '')
{ // Function modified from tep_get_manufacturers()
	global $language, $first, $last;
	if (!is_array($models_array)) $models_array = array();
	$models_query = tep_db_query(
		"SELECT products_id,
	                                     products_model 
                                  FROM products 
                                  ORDER BY products_model"
	);
	$count = 0;
	while ($models = tep_db_fetch_array($models_query)) {
		if ($count == 0) {
			$first = $models['products_model'];
		}
		$models_array[] = array('id' => $models['products_model'], 'text' => $models['products_model']);
		$count++;
		$last = $models['products_model'];
	}
	return $models_array;
}


$models_array = tep_get_models();
$from = $first;
$to = $last;


// Process the request data
if (isset($_GET['action']) && $_GET['action'] == 'update') {

	// Get the data from the form and sanitize it
	if (isset($_POST['manufacturers_id'])) {
		$mfr = (int)$_POST['manufacturers_id'];
	} else {
		$mfr = 0;
	}

	if (isset($_POST['categories_id'])) {
		$cat = (int)$_POST['categories_id'];
	} else {
		$cat = 0;
	}

	if (isset($_POST['from_id'])) {
		$from = $_POST['from_id'];
	}

	if (isset($_POST['to_id'])) {
		$to = $_POST['to_id'];
	}

	if (isset($_POST['like'])) {
		$like = $_POST['like'];
	} else {
		$like = '';
	}

	if (isset($_POST['add'])) {
		$add = (int)$_POST['add'];
	} else {
		$add = 1;
	}

	if (isset($_POST['fixed'])) {
		$percent = (int)$_POST['fixed'];
	} else {
		$percent = 1;
	}

	if (isset($_POST['value'])) {
		$value = preg_replace('/[^0-9.]/', '', $_POST['value']);
	} else {
		$value = 0;
	}
	if (isset($_POST['forReal'])) {
		$forReal = $_POST['forReal'];
	} else {
		if (isset($_GET['forReal'])) {
			$forReal = $_GET['forReal'];
		} else {
			$forReal = 0;
		}
	}

	if (isset($_GET['restoreBackup'])) {
		$restoreBackup = $_GET['restoreBackup'];
	} else {
		$restoreBackup = 0;
	}


	if ($restoreBackup == 1) {
		$read_from = $_GET['backup_file'];
		if (file_exists(DIR_FS_BACKUP . $_GET['backup_file'])) {
			$restore_file = DIR_FS_BACKUP . $_GET['backup_file'];
			$extension = substr($_GET['backup_file'], -3);

			if (($extension == 'sql') || ($extension == '.gz') || ($extension == 'zip')) {
				switch ($extension) {
					case 'sql':
						$restore_from = $restore_file;
						$remove_raw = false;
						break;
					case '.gz':
						$restore_from = substr($restore_file, 0, -3);
						exec(LOCAL_EXE_GUNZIP . ' ' . $restore_file . ' -c > ' . $restore_from);
						$remove_raw = true;
						break;
					case 'zip':
						$restore_from = substr($restore_file, 0, -4);
						exec(LOCAL_EXE_UNZIP . ' ' . $restore_file . ' -d ' . DIR_FS_BACKUP);
						$remove_raw = true;
				}

				//echo " filesize: " . filesize($restore_from);

				if (isset($restore_from) && file_exists($restore_from) && (filesize($restore_from) > 60)) {
					$fd = fopen($restore_from, 'rb');
					$restore_query = fread($fd, filesize($restore_from));
					fclose($fd);
				}
			}
		}
		//echo " dataGot: " . isset($restore_query);
		if (isset($restore_query)) {
			//  echo " true: " . isset($restore_query);
			$sql_array = array();
			$restoreOldPrices_array = array();
			$restoreIds_array = array();
			$sql_length = strlen($restore_query);
			$pos = strpos($restore_query, ';');
			$sql_string = "SELECT
							CASE
								WHEN pv.products_id IS NOT NULL THEN 'variation'
								ELSE 'product'
							END AS source,
							p.products_id,
							pv.products_variations_id,
							pdsc.products_name,
							CASE 
								WHEN pv.price > 0 THEN pv.price
								ELSE p.products_price
							END AS products_price,
							CASE
								WHEN pv.model IS NOT NULL THEN pv.model
								ELSE p.products_model
							END AS products_model,
							pv.attributes
							FROM products p
							JOIN products_to_categories ptoc ON p.products_id = ptoc.products_id
							JOIN products_description pdsc ON p.products_id = pdsc.products_id
							LEFT JOIN products_variations pv ON p.products_id = pv.products_id
							WHERE ( ";
			echo " sql_string: " . $sql_string . "<br>";
		
			for ($i = $pos; $i < $sql_length; $i++) {
				if ($restore_query[0] == '#') {
					$restore_query = ltrim(substr($restore_query, strpos($restore_query, "\n")));
					$sql_length = strlen($restore_query);
					$i = strpos($restore_query, ';') - 1;
					continue;
				}
				if ($restore_query[($i + 1)] == "\n") {
					for ($j = ($i + 2); $j < $sql_length; $j++) {
						if (trim($restore_query[$j]) != '') {
							$next = substr($restore_query, $j, 6);
							if ($next[0] == '#') {
								// find out where the break position is so we can remove this line (#comment line)
								for ($k = $j; $k < $sql_length; $k++) {
									if ($restore_query[$k] == "\n") break;
								}
								$query = substr($restore_query, 0, $i + 1);
								$restore_query = substr($restore_query, $k);
								// join the query before the comment appeared, with the rest of the dump
								$restore_query = $query . $restore_query;
								$sql_length = strlen($restore_query);
								$i = strpos($restore_query, ';') - 1;
								continue 2;
							}
							break;
						}
					}
					if ($next == '') { // get the last insert query
						$next = 'update';
					}
					if ((preg_match('/update/i', $next))) {
						$query = substr($restore_query, 0, $i);
						$sql_array[] = $query;
						$next = '';
						$restore_query = ltrim(substr($restore_query, $i + 1));
						$sql_length = strlen($restore_query);
						$i = strpos($restore_query, ';') - 1;
					}
				}
			}

			for ($i = 0, $n = sizeof($sql_array); $i < $n; $i++) {
				if ($i > 0) $sql_string .= " OR ";
				//tep_db_query($sql_array[$i]);
				preg_match('/[0-9.]*/', ltrim(substr($sql_array[$i], 41)), $price);
				preg_match('/p\.products_id=[0-9]*/', $sql_array[$i], $id);
				$restoreProductID = substr($id[0], 14);
				$restore_array[$restoreProductID] = $price[0];
				$sql_string .= "p.products_id = " . $restoreProductID;
			}
			print_r($restore_array);
			$sql_string .= ');';
		}
	} else {

		// Set the SQL where function 

		if ($cat == 0) {
			$where_string = '';
		} else {
			$where_string = '(ptoc.categories_id = ' . $cat;
			$sub_categories = array();
			pud_get_sub_categories($sub_categories, $cat);
			$categories_query_addition = '';
			foreach ($sub_categories as $ckey => $category) {
				$categories_query_addition .= ' or ptoc.categories_id = ' . (int)$category . '';
			}
			$where_string .= ' ' . $categories_query_addition . ')';
		}
		if ($mfr != 0) {
			$where_string .= ' AND manufacturers_id=' . $mfr;
		}

		if ($like == '') {
			if ($from != $first) {
				$where_string .= " AND p.products_model >= '" . $from . "'";
			}
			if ($to != $last) {
				$where_string .= " AND p.products_model <= '" . $to . "'";
			}
		} else {
			$where_string .= " AND (pdsc.products_name LIKE '" . $like . "' OR " . "p.products_model LIKE '" . $like . "')";
		}

		// Query to get the selected products and make the changes

		$sql_string =
		"SELECT
				CASE
					WHEN pv.products_id IS NOT NULL THEN 'variation'
					ELSE 'product'
				END AS source,
				p.products_id,
				pv.products_variations_id,
				pdsc.products_name,
				CASE 
					WHEN pv.price > 0 THEN pv.price
					ELSE p.products_price
				END AS products_price,
				CASE
					WHEN pv.model IS NOT NULL THEN pv.model
					ELSE p.products_model
				END AS products_model,
				pv.attributes
			FROM
				products p
			JOIN products_to_categories ptoc ON
				p.products_id = ptoc.products_id
			JOIN products_description pdsc ON
				p.products_id = pdsc.products_id
			LEFT JOIN products_variations pv ON
				p.products_id = pv.products_id
		WHERE " . $where_string;
	}

	echo 'update string: ' . $sql_string;
	$products_update_query = tep_db_query($sql_string);
	$count = 0;
	$sql_output = '<table border=0 width=100% cellspacing=0 cellpadding=5><tr class="dataTableTitles"><td>src</td><td>Model</td><td>Old&nbsp;Price</td><td>New&nbsp;Price</td><td>Products&nbsp;Name</td></tr>';
	$dbBackup = '# Price Updater' . "\n" .
		'# Backup Date: ' . date(PHP_DATE_TIME_FORMAT) . "\n\n";
	while ($products_update = tep_db_fetch_array($products_update_query)) {
		if ($restoreBackup == 1) {
			$new_price = $restore_array[$products_update['products_id']];
		} else {
			if ($percent == 0) {  // Fixed price change
				if ($add == 0) {  // Subtract
					$new_price = $products_update['products_price'] - $value;
					$mMode = 'addS';
				} else {  // Add
					$new_price = $products_update['products_price'] + $value;
					$mMode = 'addA';
				}
			} else {  // Percent change
				if ($add == 0) {  // Subtract
					$new_price = $products_update['products_price'] * (1 - ($value / 100));
					$mMode = 'pcA';
				} else {  // Add
					$new_price = $products_update['products_price'] * (1 + ($value / 100));
					$mMode = 'pcS';
				}
			}
		}
		$new_price = round($new_price, 2);
		$old_price = round($products_update['products_price'], 2);
		$sql_output .= '<tr class="dataTableContent"><td>' . $products_update['source'] . '</td><td>' . $products_update['products_model'] . '</td><td>&pound;' .  $old_price . '</td><td>&pound;' . $new_price . '</td><td>' . $products_update['products_name'] . '</td></tr>';
		/*<tr><td colspan=5>debug: value: ' . $value . ' mode: ' . $mMode . ' $new_price = ' . $products_update['products_price'] . ' * (1 + (' . $value . ' / 100)) = ' . $new_price . '*/
		if ($forReal > 1) {
			if ($products_update['source'] == 'variation'){
				$query = "UPDATE products_variations as pv SET price='" . $old_price . "' WHERE pv.products_variations_id=" . $products_update['products_variations_id'] . ";\n";
				$dbBackup .= $query;
				tep_db_query($query);
			} else {
				$dbBackup .= "UPDATE products as p SET products_price='" . $old_price . "' WHERE p.products_id=" . $products_update['products_id'] . ";\n";
				tep_db_query("UPDATE products as p SET products_price='" . $new_price . "' WHERE p.products_id=" . $products_update['products_id'] . ";");	
			}
		}
		$product_attributes_query = tep_db_query("select products_attributes_id, options_values_price from products_attributes where products_id = '" . $products_update['id'] . "'");
		while ($product_attributes = tep_db_fetch_array($product_attributes_query)) {
			if ($percent == 0) {  // Fixed price change
				if ($add == 0) {  // Subtract
					$new_price = $product_attributes['options_values_price'] - $value;
				} else {  // Add
					$new_price = $product_attributes['options_values_price'] + $value;
				}
			} else {  // Percent change
				if ($add == 0) {  // Subtract
					$new_price = $product_attributes['options_values_price'] * (1 - ($value / 100));
				} else {  // Add
					$new_price = $product_attributes['options_values_price'] * (1 + ($value / 100));
				}
			}
			if ($forReal > 1) {
				$dbBackup .= "update products_attributes set options_values_price = '" . $old_price . "' where products_attributes_id = '" . $product_attributes['products_attributes_id'] . "'\n";
				tep_db_query("update products_attributes set options_values_price = '" . $new_price . "' where products_attributes_id = '" . $product_attributes['products_attributes_id'] . "'");
			}
		} // end insert product attributes
		$count++;
	}
	$sql_output .= '</table>';

	// If a manufacturer was selected, get the name
	if ($mfr != 0) {
		$manufacturers_query = tep_db_query("SELECT manufacturers_name FROM manufacturers WHERE manufacturers_id=" . $mfr);
		$manufacturers = tep_db_fetch_array($manufacturers_query);
		$manufacturer = $manufacturers['manufacturers_name'];
	} else {
		$manufacturer = "All manufacturers";
	}


	// If a category was selected, get the name
	if ($cat != 0) {
		$categories_query = tep_db_query("SELECT cd.categories_name
										FROM categories_description cd, 
											languages l 
										WHERE l.languages_id = cd.language_id
										AND l.name = '" . $language . "' 
										AND categories_id = " . $cat);
		$categories = tep_db_fetch_array($categories_query);
		$category = $categories['categories_name'];
	} else {
		$category = "ALL CATEGORIES";
	}


	// Finish the rest of the update text information

	$percent_string = '';
	if ($restoreBackup == 1) {
		$add_string = "restored";
	} else {
		if ($percent == 1) {
			$percent_string = "Percent";
		} else {
			$percent_string = "Exactly";
		}
		// Products while loop


		$add_string = "Decreased By";
		if ($add == 1) {
			$add_string = "Increased By";
		}
		if ($forReal > 1) {
			$backup_file = 'pup_' . $manufacturer . '_' . $category . '_' . $add_string . '_' . $value . '_' . $percent_string . '_' . date('YmdHis') . '.sql';
			$backup_file = str_replace(' ', '-', $backup_file);
			$fp = fopen(DIR_FS_BACKUP . $backup_file, 'w');

			fputs($fp, $dbBackup);
			fclose($fp);
		}
		if ($forReal > 1) {
			$update_string = $manufacturer . ' prices in ' .  $category . ' were ' .  $add_string . ' ' . $value . ' ' . $percent_string;
		} else {
			$update_string = $manufacturer . ' prices in ' .  $category . ' will be ' . $add_string  . ' ' .  $value . ' ' . $percent_string;
		}
	}
} // End action=update 

// check if the backup directory exists
$dir_ok = false;
if (is_dir(DIR_FS_BACKUP)) {
	if (tep_is_writable(DIR_FS_BACKUP)) {
		$dir_ok = true;
	} else {
		$messageStack->add(ERROR_BACKUP_DIRECTORY_NOT_WRITEABLE, 'error');
	}
} else {
	$messageStack->add(ERROR_BACKUP_DIRECTORY_DOES_NOT_EXIST, 'error');
}
require('includes/template_top.php');
?>

<table border=0 width="100%" cellspacing=0 cellpadding="5">
	<tr class="dataTableHeadingRow">
		<td class="dataTableHeadingContent">&nbsp;Updates&nbsp;</td>
	</tr>
	<tr class="dataTableRow">
		<td class="dataTableContent">
			<?php
			if (isset($count) && $count != 0) {
				echo $update_string;
			}
			?>
		</td>
	</tr>
	<tr class="dataTableRow">
		<td class="dataTableContent">
			<?php
			if (isset($count) && $count != 0) {
				echo "Products affected: " . $count;
			} else {
				$forReal = 0;
				echo "No products ";
			}
			echo '<br>Mode: ';
			if ($forReal != 1)
				echo 'Preview';
			else
				echo 'Completed';
			echo $sql_output;
			?>
		</td>
	</tr>
</table>

<?php if ($restoreBackup != 1) { ?>
	<form action="price_updater.php?action=update<?php if (defined('SID') && @tep_not_null(SID)) {
														echo '&' . tep_session_name() . '=' . tep_session_id();
													} ?>" method=post>
		<table border=0 width=700 cellspacing=0 cellpadding=2>
			<?php if ($forReal < 2) {
			?>
				<tr>
					<td colspan=7><?php echo tep_black_line(); ?></td>
				</tr>
				<tr class="dataTableHeadingRow">
					<td class="dataTableHeadingContent">&nbsp;Manufacturer&nbsp;</td>
					<td colspan=2 class="dataTableHeadingContent">&nbsp;Category&nbsp;</td>
					<td class="dataTableHeadingContent">&nbsp;Plus_minus&nbsp;</td>
					<td class="dataTableHeadingContent">&nbsp;Fixed&nbsp;</td>
					<td class="dataTableHeadingContent">&nbsp;Value&nbsp;</td>
					<td class="dataTableHeadingContent">&nbsp;Action&nbsp;</td>
				</tr>
				<tr>
					<td colspan=7><?php echo tep_black_line(); ?></td>
				</tr>
				<?php
				$ro = '';
				if ($forReal > 0) {
					$ro = 'readonly';
				}
				?>
				<tr class=attributes-odd><?php // tep_draw_pull_down_menu('categories_id', tep_get_categories(array(array('id' => '0', 'text' => 'All Categories '))),,$ro);
											?>
					<td class="smallText">&nbsp;<?php echo tep_draw_pull_down_menu('manufacturers_id', tep_get_manufacturers(array(array('id' => '0', 'text' => 'All Manufacturers'))), '', $ro); ?>&nbsp;</td>
					<td colspan=2 class="smallText">&nbsp;<?php echo tep_draw_pull_down_menu('categories_id', array_merge(array(0 => array("id" => '0', 'text' => 'All Categories')), tep_get_category_tree()), '', $ro); ?>&nbsp;</td>
					<td class="smallText">&nbsp;<?php echo tep_draw_pull_down_menu('add', array(array('id' => '1', 'text' => '+'), array('id' => '0', 'text' => '-')), '', $ro); ?>&nbsp;</td>
					<td class="smallText">&nbsp;<?php echo tep_draw_pull_down_menu('fixed', array(array('id' => '1', 'text' => '%'), array('id' => '0', 'text' => 'Fixed')), '', $ro); ?>&nbsp;</td>
					<td class="smallText">&nbsp;<?php echo tep_draw_input_field('value', "0", $ro); ?>&nbsp;</td>
					<?php  ?>
				</tr>
				<tr>
					<td colspan=7> <?php echo tep_black_line(); ?></td>
				</tr>
				<tr>
					<td colspan=7><?php echo tep_draw_separator('pixel_trans.gif', '100%', '5'); ?></td>
				</tr>
				<tr>
					<td colspan=7><?php echo tep_black_line(); ?></td>
				</tr>
				<tr class="dataTableHeadingRow">
					<td colspan=7 class="dataTableHeadingContent">&nbsp;Model&nbsp;</td>
				</tr>
				<tr>
					<td colspan=7><?php echo tep_black_line(); ?></td>
				</tr>
				<tr class=attributes-odd>
					<td class="smallText">&nbsp;<?php echo tep_draw_pull_down_menu('from_id', $models_array, $from, $ro); ?>&nbsp;&nbsp;To&nbsp;</td>
					<td class="smallText">&nbsp;<?php echo tep_draw_pull_down_menu('to_id', $models_array, $to, $ro); ?>&nbsp;</td>
					<td class="smallText">&nbsp;Like:&nbsp;</td>
					<td colspan=4 class="smallText">&nbsp;<?php echo tep_draw_input_field('like', "", $ro); ?>&nbsp;&nbsp;&nbsp;Notes&nbsp;</td>
				</tr>
				<tr>
					<?php /*} else {?><tr class=attributes-odd>
				<td class="smallText">&nbsp;<?php echo tep_image_submit('button_update.gif', IMAGE_UPDATE); ?>&nbsp;</td>
				</td></tr><tr>
				<?php }>*/ ?>
					<td colspan=7><?php echo tep_black_line(); ?></td>
					<td class="smallText">&nbsp;
						<?php
						if ($forReal == 0) {
							echo tep_draw_hidden_field('forReal', 1) ?> &nbsp;</td>
					<td class="smallText">&nbsp;<?php echo tep_image_submit('button_preview.gif', IMAGE_PREVIEW); ?>&nbsp;</td>
					<?php // echo 'blerA: ' . $forReal;
					?>
				<?php } ?>
				</tr>
			<?php }
			if ($forReal == 1) { ?>
				<td class="smallText">&nbsp;<?php echo tep_draw_hidden_field('forReal', 2) ?>&nbsp;</td>
				<td class="smallText">&nbsp;<?php echo tep_image_submit('button_update.gif', IMAGE_UPDATE); ?>&nbsp;</td>
			<?php } ?>

		</table>
	</form>
	<?php } else {
	if ($forReal == 1) { ?>
		<td class="smallText"></td>
		<td class="smallText"><a href="<?php echo tep_href_link('price_updater.php', 'action=update&restoreBackup=1&forReal=2&backup_file=' . $_GET['backup_file']) ?>">Restore</a>&nbsp;</td>
		</td>
<?php }
} ?>
</td>
</tr>
</table>
<?php if ($forReal != 0) { ?>
	<table border="0" width="1024" cellspacing="0" cellpadding="2">
		<tr class="dataTableHeadingRow">
			<td class="dataTableHeadingContent">Title</td>
			<td class="dataTableHeadingContent" align="center">Date</td>
			<td class="dataTableHeadingContent" align="right">Size</td>
			<td class="dataTableHeadingContent" align="right">Action&nbsp;</td>
		</tr>
		<?php
		if ($dir_ok == true) {
			$dir = dir(DIR_FS_BACKUP);
			$contents = array();
			while ($file = $dir->read()) {
				if (!is_dir(DIR_FS_BACKUP . $file) && (substr($file, -3) == 'sql') && (substr($file, 0, 4) == 'pup_')) {
					$contents[] = $file;
				}
			}
			sort($contents);

			for ($i = 0, $n = sizeof($contents); $i < $n; $i++) {
				$entry = $contents[$i];

				$check = 0;

				if ((!isset($_GET['file']) || (isset($_GET['file']) && ($_GET['file'] == $entry))) && !isset($buInfo) && ($action != 'backup') && ($action != 'restorelocal')) {
					$file_array['file'] = $entry;
					$file_array['date'] = date(PHP_DATE_TIME_FORMAT, filemtime(DIR_FS_BACKUP . $entry));
					$file_array['size'] = number_format(filesize(DIR_FS_BACKUP . $entry)) . ' bytes';
					switch (substr($entry, -3)) {
						case 'zip':
							$file_array['compression'] = 'ZIP';
							break;
						case '.gz':
							$file_array['compression'] = 'GZIP';
							break;
						default:
							$file_array['compression'] = "No Extension";
							break;
					}

					$buInfo = new objectInfo($file_array);
				}


				$e = explode("_", $entry);
				$entryParsed = str_replace("-", " ", 'Products: <span style="color: green;font-weight:bold">' . $e[1] . '</span> In Category: <span style="color: green;font-weight:bold">' . $e[2] . '</span> Action: <span style="color: green;font-weight:bold">' . $e[3] . ' ' . $e[4] . str_replace("Percent", "%", str_replace("Exactly", " Exactly", $e[5])) . '</span>');
		?>
				<td class="dataTableContent"><?php echo $entryParsed; ?></td>
				<td class="dataTableContent" align="center"><?php echo date(PHP_DATE_TIME_FORMAT, filemtime(DIR_FS_BACKUP . $entry)); ?></td>
				<td class="dataTableContent" align="right"><?php echo number_format(filesize(DIR_FS_BACKUP . $entry)); ?> bytes</td>
				<td class="dataTableContent" align="right"><a href="<?php echo tep_href_link('price_updater.php', 'action=update&restoreBackup=1&forReal=1&backup_file=' . $contents[$i]) ?>">Restore</a>&nbsp;<a href="<?php tep_href_link('price_updater.php', 'action=deleteBackup&file=' . $file) ?>">Delete</a></td>
				</tr>
		<?php
			}
		}
		$dir->close();
		?>
	</table>
<?php } ?>


<?php
require('includes/template_bottom.php');
require('includes/application_bottom.php');
?>