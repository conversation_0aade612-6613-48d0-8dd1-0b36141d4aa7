<?php
/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2013 osCommerce

  Released under the GNU General Public License
*/

define('MODULE_SECURITY_CHECK_EXTENDED_MYSQL_UTF8_TITLE', 'MySQL UTF-8');
define('MODULE_SECURITY_CHECK_EXTENDED_MYSQL_UTF8_ERROR', 'Some database tables need to be converted to UTF-8 (utf8_unicode_ci). Please review the tables under Tools -&gt; Database Tables.');
?>
