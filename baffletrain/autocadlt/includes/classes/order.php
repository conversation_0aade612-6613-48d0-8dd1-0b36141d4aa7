<?php
/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2014 osCommerce

  Released under the GNU General Public License
*/

  class order {
    var $info, $totals, $products, $customer, $delivery;

    function __construct($order_id) {
      $this->info = array();
      $this->totals = array();
      $this->products = array();
      $this->customer = array();
      $this->delivery = array();

      $this->query($order_id);
    }

    function query($order_id) {
      global $languages_id;
      print_rr($order_id);
      $order_query = tep_db_query("select o.*, s.orders_status_name from " . TABLE_ORDERS . " o, " . TABLE_ORDERS_STATUS . " s where o.orders_id = '" . (int)$order_id . "' and o.orders_status = s.orders_status_id and s.language_id = '" . (int)$languages_id . "'");
      $order = tep_db_fetch_array($order_query);
      print_rr($order);
	    $order_extra_query = tep_db_query("SELECT customers_fax,customers_id, customers_firstname, customers_lastname FROM `customers` WHERE customers_id = " . $order['customers_id'] . " LIMIT 1");
      $order_extra = tep_db_fetch_array($order_extra_query);

      $totals_query = tep_db_query("select title, text, class from " . TABLE_ORDERS_TOTAL . " where orders_id = '" . (int)$order_id . "' order by sort_order");
      while ($totals = tep_db_fetch_array($totals_query)) {
        $this->totals[] = array('title' => $totals['title'],
                                'text' => $totals['text'],
                                'class' => $totals['class']);
      }

      $this->info = array('id' => $order['orders_id'],
                          'total' => null,
                          'currency' => $order['currency'],
                          'currency_value' => $order['currency_value'],
                          'payment_method' => $order['payment_method'],
                          'cc_type' => $order['cc_type'],
                          'cc_owner' => $order['cc_owner'],
                          'cc_number' => $order['cc_number'],
                          'cc_expires' => $order['cc_expires'],
                          
                          //MAZ BOF SWITCH
                          // BMC CC Mod Start
                            'cc_start' => $order['cc_start'],
                            'cc_issue' => $order['cc_issue'],
                            'cc_cvv' => $order['cc_cvv'],
                            // BMC CC Mod End
                              //MAZ EOF SWITCH DEBIT CARDS
                            // MAZ purchaseorder start
                          'account_name' => $order['account_name'],
                          'account_number' => $order['account_number'],
                          'po_number' => $order['po_number'],
                        // MAZ purchaseorder end
                            'cc_po_no' => $order['cc_po_no'],
                            'cc_po_contact_name' => $order['cc_po_contact_name'],
                            'cc_po_department' => $order['cc_po_department'],
                          'date_purchased' => $order['date_purchased'],
                          'orders_status' => $order_status['orders_status_name'],
                          'last_modified' => $order['last_modified'],
                          
                          'date_purchased' => $order['date_purchased'],
                          'status' => $order['orders_status_name'],
                          'orders_status' => $order['orders_status'],
                          'last_modified' => $order['last_modified']);

      foreach ( $this->totals as $t ) {
        if ( $t['class'] == 'ot_total' ) {
          $this->info['total'] = $t['text'];
          break;
        }
      }

      $this->customer = array( 'id' => $order_extra['customers_id'],
                              'name' => $order['customers_name'],
                              'firstname' => $order_extra['customers_firstname'],
                              'lastname' => $order_extra['customers_lastname'],
                              'company' => $order['customers_company'],
                              'street_address' => $order['customers_street_address'],
                              'suburb' => $order['customers_suburb'],
                              'city' => $order['customers_city'],
                              'postcode' => $order['customers_postcode'],
                              'state' => $order['customers_state'],
                              'country' => $order['customers_country'],
                              'format_id' => $order['customers_address_format_id'],
                              'telephone' => $order['customers_telephone'],
							                'mobile' => $order_extra['customers_fax'],
                              'email_address' => $order['customers_email_address']);

      $this->delivery = array('name' => $order['delivery_name'],
                              'company' => $order['delivery_company'],
                              'street_address' => $order['delivery_street_address'],
                              'suburb' => $order['delivery_suburb'],
                              'city' => $order['delivery_city'],
                              'postcode' => $order['delivery_postcode'],
                              'state' => $order['delivery_state'],
                              'country' => $order['delivery_country'],
                              'format_id' => $order['delivery_address_format_id']);

      $this->billing = array('name' => $order['billing_name'],
                             'company' => $order['billing_company'],
                             'street_address' => $order['billing_street_address'],
                             'suburb' => $order['billing_suburb'],
                             'city' => $order['billing_city'],
                             'postcode' => $order['billing_postcode'],
                             'state' => $order['billing_state'],
                             'country' => $order['billing_country'],
                             'format_id' => $order['billing_address_format_id']
        );

      $index = 0;
      $orders_products_query = tep_db_query("select orders_products_id, products_id, products_name, products_model, products_price, products_tax, products_quantity, final_price from " . TABLE_ORDERS_PRODUCTS . " where orders_id = '" . (int)$order_id . "'");
      while ($orders_products = tep_db_fetch_array($orders_products_query)) {
        $this->products[$index] = array(
                                        'id' => $orders_products['products_id'],
                                        'qty' => $orders_products['products_quantity'],
                                        'name' => $orders_products['products_name'],
                                        'model' => $orders_products['products_model'],
                                        'tax' => $orders_products['products_tax'],
                                        'price' => $orders_products['products_price'],
                                        'final_price' => $orders_products['final_price']
                                      );

        $subindex = 0;
        $attributes_query = tep_db_query("select products_options, products_options_values, options_values_price, price_prefix from " . TABLE_ORDERS_PRODUCTS_ATTRIBUTES . " where orders_id = '" . (int)$order_id . "' and orders_products_id = '" . (int)$orders_products['orders_products_id'] . "'");
        if (tep_db_num_rows($attributes_query)) {
          while ($attributes = tep_db_fetch_array($attributes_query)) {
            $this->products[$index]['attributes'][$subindex] = array('option' => $attributes['products_options'],
                                                                     'value' => $attributes['products_options_values'],
                                                                     'prefix' => $attributes['price_prefix'],
                                                                     'price' => $attributes['options_values_price']);

            $subindex++;
          }
        }
        $index++;
      }
    }
  }
?>
