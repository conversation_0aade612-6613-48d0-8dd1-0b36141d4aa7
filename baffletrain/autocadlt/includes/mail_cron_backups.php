<?php
   /*
   mail_cron_backups.php V1 (c) Sam 22/03/2012  

   Released under the GNU General Public License

   */
   	@include('includes/languages/' . $language . '/backup.php');
   	
   	function showsize($bytes) {
  			$sz = ' KMGTP';
  			$factor = floor((strlen($bytes) - 1) / 3);
  			return round($bytes / pow(1024, $factor ), ($factor > 1 ? 2 : 0) ) . ' ' . ($factor ? @$sz[$factor] . 'B' : 'Bytes');
		}  
		     	
      require('includes/mail_backups.php');
      if (send_backup ($at_backup_file)) at_message(sprintf(SUCCESS_EMAIL, $at_backup_file), 'success');
      else at_message(sprintf(ERROR_EMAIL_FAILED, $at_backup_file), 'error');
?>