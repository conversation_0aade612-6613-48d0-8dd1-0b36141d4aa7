<?php
   /*
   mail_backups.php V1.2.1 (c) Sam 30/03/2012 

   Released under the GNU General Public License

   Complete host, username, password and port (smtp) for an email account on your server

   */
   function mail_message($message, $class='error')
   {
      global $cron, $messageStack;
      if ($cron) echo $message . "\n";
      else $messageStack->add($message, $class);
   }

   function send_backup ($filename)
   {
		global $cron;      
      $host = '';  // your mailbox send host, ie mail.mysite.com
      $uname = '';  // SMTP <NAME_EMAIL>
      $password = ''; // SMTP password
      $port = 587; // change if your SMTP uses a different port.
      $message_size_limit = 50; // enter the maximum size of email attachments set by your mail server in MegaBytes (message_size_limit)
      ini_set('memory_limit', '128M'); // set a suitable value if your default limit is too low and host allows some increase
      if (function_exists('apache_setenv')) apache_setenv('no-gzip', '1');
      $pear = false;
      $lf = "\n";
      $to = STORE_OWNER_EMAIL_ADDRESS;
      $from = EMAIL_FROM;
      $name = STORE_NAME;
      $subject = 'DataBase ' . ($cron ? 'Cron Job ' : '') . 'Backup from ' . $name;
      $textmessage = sprintf(EMAIL_MESSAGE, $filename . $lf.$lf, $lf.$lf);
      $memory = (intval(ini_get('memory_limit'))*1048576) - memory_get_usage(true);
      $fmem = @filesize(DIR_FS_BACKUP . $filename);
      $ext = strtolower(array_pop(explode('.', $filename )));
      $mimetype = $ext == 'gz' ? 'application/x-gzip' : ($ext == 'zip' ? 'application/zip' : 'application/octet-stream');
      if ($fmem*5.5 < $memory)
      {
         if ($fmem < $message_size_limit*1048576)
         {
            // try pear mail first
            if ( @include_once('Mail.php') )
            {
               if ( @include_once('Mail/mime.php') )
               {
                  if ( @include_once('Net/SMTP.php') )
                  {
                     $pear = true;
                     if ($host && $uname && $password && $to && $from && $port)
                     {
                        $headers['From'] = $from;
                        $headers['To'] = $to;
                        $headers['Subject'] = $subject;
                        $headers['Date'] = date('D, j M Y H:i:s');
                        $smtp['host'] = $host;
                        $smtp['port'] = $port;
                        $smtp['auth'] = true;
                        $smtp['username'] = $uname;
                        $smtp['password'] = $password;
                        $smtp['timeout'] = 20;
                        $mime = new Mail_mime($lf);
                        $mime->setTXTBody($textmessage);
                        $mime->setHTMLBody(nl2br($textmessage));
                        $mime->addAttachment(DIR_FS_BACKUP . $filename, $mimetype);
                        $message = $mime->get();
                        $headers = $mime->headers($headers);
                        $mail =& Mail::factory("smtp", $smtp);
                        $sent = $mail->send($to, $headers, $message);
                        if ($result = (PEAR::isError($sent))) mail_message('Email send error: ' . $sent->getMessage());
                        return !$result;
                     }
                     else
                     {
                        mail_message(ERROR_EMAIL_AUTH_PARAM, 'error');
                     }
                  }
               }
            }
            // revert to php mail  
            if ($to && $from && !$pear)
            {
               mail_message(WARNING_PEAR, 'warning');
               $headers = "X-Mailer: PHP v".phpversion().$lf; 
               $headers .= "From: $name <$from>";
               $boundary = 'b1' . md5(uniqid(time()));
               $boundary2 = 'b2' . md5(uniqid(time()));
               //
               $headers .= $lf . "MIME-Version: 1.0$lf" . "Content-Type: multipart/mixed;$lf" . " boundary=\"{$boundary}\"$lf";
               $headers .= "--{$boundary}$lf" . "Content-Type: multipart/alternative;$lf" . "    boundary=\"{$boundary2}\"$lf";
               $message = "--{$boundary2}$lf" . "Content-Type: text/plain; charset=\"".CHARSET."\"$lf" . "Sensitivity: Company-Confidential" . $lf .
               "Content-Transfer-Encoding: 7bit". $lf.$lf . $textmessage . $lf.$lf;
               $message .= "--{$boundary2}$lf" . "Content-Type: text/html; charset=\"".CHARSET."\"$lf" . "Sensitivity: Company-Confidential" . $lf .
               "Content-Transfer-Encoding: quoted-printable". $lf.$lf . nl2br($textmessage) . $lf.$lf;
               $message .= "--{$boundary2}--$lf";
               $message .= "--{$boundary}$lf";
               $fp = @fopen(DIR_FS_BACKUP . $filename,"rb");
               $data = @fread($fp,filesize(DIR_FS_BACKUP . $filename));
               @fclose($fp);
               $data = chunk_split(base64_encode($data));

               $message .= "Content-Type: $mimetype; name=\"".$filename."\"$lf" . "Content-Description: " . $filename. $lf .
               "Content-Disposition: attachment;$lf" . "filename=\"".$filename."\" size=". strlen($data) .";$lf" .
               "Content-Transfer-Encoding: base64" . $lf.$lf . $data . $lf.$lf;
               $message .= "--{$boundary}--" . $lf.$lf;

               return mail($to, $subject, $message, $headers);
            }
            elseif(!$pear)
            {
               mail_message(ERROR_EMAIL_ADD_PARAM);
            }
         }
         else
         {
            mail_message(sprintf(ERROR_EMAIL_SIZE, showsize($fmem)));
         }
      }
      else
      {
         mail_message(sprintf(ERROR_EMAIL_MEMORY, showsize($fmem * 5.5),showsize($memory)));
      }
      return false;
   }

?>