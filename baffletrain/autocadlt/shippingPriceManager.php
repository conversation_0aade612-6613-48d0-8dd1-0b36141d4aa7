<?php
/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2010 osCommerce

  Released under the GNU General Public License
*/

	require('includes/application_top.php');
	
  require('includes/template_top.php');
  global $$link;
  

  if ( isset($_GET['save'] ) ){
     tep_db_query("UPDATE `products_shipping` SET products_ship_key='" . $_POST["products_ship_key"] . "', products_ship_price='" . $_POST['products_ship_price'] . "', products_ship_qty='" . $_POST['products_ship_qty'] . "' WHERE products_ship_key='" . $_POST['products_ship_key'] . "'");
     echo tep_db_affected_rows() . " Entries updated";
  }
  $shippingdb = tep_db_query("SELECT * FROM `products_shipping` GROUP BY `products_ship_key` ORDER BY `products_ship_key` DESC");
  echo tep_draw_form('updateShipping', 'shippingPriceManager.php', 'save=1', 'post', 'enctype="multipart/form-data"');
?>
<table cellspacing="0" cellpadding="2" width="100%" border="0">      <?php // start indvship 
?> 
          <tr class="indvShip">
            <td class="main">Group</td>
            <td class="main" valign=top><?php
    echo tep_draw_input_field('products_ship_key', $pInfo->products_ship_key);
    if (@tep_not_null($pInfo->products_ship_key))
        echo 'notnull';
    else
        echo 'null';
?></td><td class="main">Current Keywords:</td>
            <td rowspan='4' class="main"> <table cellspacing="0" cellpadding="2" width="100%" border="0">
            <?php
    $i      = 0;
   
    while ($keywords = tep_db_fetch_array($shippingdb)) {
        $i++;
        if ($i == 1) {
            echo "<tr><td>";
        } else {
            echo "<td>&nbsp;</td><td>";
        }
        echo "<a href='javascript:null();' onclick=\"document.forms['updateShipping'].products_ship_key.value='" . $keywords['products_ship_key'] . "';document.forms['updateShipping'].products_ship_price.value='" . $keywords['products_ship_price'] . "';document.forms['updateShipping'].products_ship_qty.value='" . $keywords['products_ship_qty'] . "';\">" . $keywords['products_ship_key'] . "";
        if ($i == 1) {
            echo "</td>";
        } else {
            echo "</td></tr>";
            $i = 0;
        }
    }
    
?></table></td>
    </tr> <!-- end Zipcode --> <!-- Indvship -->
          
    <tr class="indvShip">
            <td class="main" style="width:210px"><?php
    echo 'Indv. Shipping Price:';
?></td>
            <td class="main"><?php
    echo tep_draw_input_field('products_ship_price', $pInfo->products_ship_price);
    if (@tep_not_null($pInfo->products_ship_price))
        echo 'notnull';
    else
        echo 'null';
?></td>
    </tr>
    <tr class="indvShip">
            <td class="main"><?php
    echo 'Qty to qualify for free shipping:';
?></td>
            <td class="main"><?php
    echo tep_draw_input_field('products_ship_qty', $pInfo->products_ship_qty);
    if (@tep_not_null($pInfo->products_ship_qty))
        echo 'notnull';
    else
        echo 'null';
?></td>
          </tr>
    <tr class="indvShip"><td>
    <?php echo tep_draw_button(IMAGE_SAVE, 'disk', null, 'primary');?>
             </td>
            
          
    </tr>
</table>
</form>
        <!-- end Indvship -->


<?

	
 
  require('includes/template_bottom.php');
  require('includes/application_bottom.php');
?>
