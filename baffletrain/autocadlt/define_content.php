<?php
/*
  $Id: define_content.php,v 1.0 2005/11/11 00:00:00 holforty Exp $

  Designed for: osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2005 Todd <PERSON>orty - mtholforty (at) surfalot (at) com

  Released under the GNU General Public License
  
    This program is free software; you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation; either version 2 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program; if not, write to the Free Software
    Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA  02110-1301  USA
*/

  require('includes/application_top.php');
  
  function checkPageTitle($pageURL, $count = 0){
	  $count += 1;
	  $checkQuery = tep_db_query('SELECT id FROM ' . TABLE_DEFINE_CONTENT . ' WHERE page_url = "' . $pageURL . '"'  );
	  if(tep_db_num_rows($checkQuery) > 0){
		  return false;
	  }else{
		  return $pageURL;
	  }
  }
  
  if(isset($_REQUEST['action']) && ($_REQUEST['action'] == 'ajax')){
	  $pageName = isset($_POST['data']) ? $_POST['data'] : NULL;
	  $pageURL  = strtolower(str_replace(' ', '_', trim($pageName)));
	  if(isset($pageName) && isset($pageURL)){
		  echo checkPageTitle($pageURL, 0);
	  }
	  exit;
  }
  $action = isset($_GET['action']) ? $_GET['action'] : "";
  if (isset($action) && ($action!='')) {
     
	if (isset($_POST['page_content'])) {
		
      // strip PHP code
	  $_POST['page_content'] = preg_replace('/\r\n|\r|\n/m', '', stripslashes($_POST['page_content']));//exit;
    }

    switch ($action) {
	
      case 'insert': 
        
		// check for existing content first
        $define_content_query = tep_db_query('SELECT * FROM ' . TABLE_DEFINE_CONTENT . ' WHERE page_url = "' .tep_db_input($_POST['pageURL']) . '"');
	
        if (tep_db_num_rows($define_content_query) < 1) {
			$sql_data_array = array(
									'page_title' 						=> tep_db_input(tep_db_prepare_input($_POST['page_title'])),
									'page_url' 							=> tep_db_input(tep_db_prepare_input($_POST['pageURL'])),
									'page_content'						=> tep_db_input(tep_db_prepare_input($_POST['page_content'])),
									'created_date'						=> 'now()',
									'status'							=> tep_db_prepare_input($_POST['status']),
									'login_view'						=> tep_db_prepare_input($_POST['login_view']),
									'seo_title'							=> tep_db_prepare_input($_POST['seo_title']),
									'seo_description'					=> tep_db_prepare_input($_POST['seo_description']),
									'seo_keywords'						=> tep_db_prepare_input($_POST['seo_keywords'])
									
									);
			tep_db_perform(TABLE_DEFINE_CONTENT, $sql_data_array);			
		  $messageStack->add_session(DEFINE_CONTENT_MESSAGE_CONTENT_INSERTED, 'success');
		  tep_redirect(tep_href_link('define_content.php',(!empty($_POST['dcId'])&&!empty($_POST['language_id'])?'dcId='.$_POST['dcId'].'&language_id='.$_POST['language_id']:'')));
		} else {
		  $messageStack->add_session(sprintf(DEFINE_CONTENT_ERROR_ALREADY_EXISTS, $define_content['id'], $define_content['language_id']), 'error');
          tep_redirect(tep_href_link('define_content.php',(!empty($_POST['dcId'])&&!empty($_POST['language_id'])?'dcId='.$_POST['dcId'].'&language_id='.$_POST['language_id']:'')));
		}
        break;
		
      case 'save':
	  	$sql_data_array = array(
			'page_title' 		=> tep_db_input(tep_db_prepare_input($_POST['page_title'])),
			'page_url' 							=> tep_db_input(tep_db_prepare_input($_POST['pageURL'])),
			'page_content'		=> tep_db_input(tep_db_prepare_input($_POST['page_content'])),
			'status'			=> tep_db_prepare_input($_POST['status']),
			'login_view'		=> tep_db_prepare_input($_POST['login_view']),
			'seo_title'			=> tep_db_prepare_input($_POST['seo_title']),
			'seo_description'	=> tep_db_prepare_input($_POST['seo_description']),
			'seo_keywords'		=> tep_db_prepare_input($_POST['seo_keywords'])
		);
		tep_db_perform(TABLE_DEFINE_CONTENT, $sql_data_array, 'update', "id ='" . tep_db_input(tep_db_prepare_input($_POST['dcId'])) . "'");
		$messageStack->add_session(DEFINE_CONTENT_MESSAGE_CONTENT_UPDATED, 'success');
		tep_redirect(tep_href_link('define_content.php',(!empty($_POST['dcId'])&&!empty($_POST['language_id'])?'dcId='.$_POST['dcId'].'&language_id='.$_POST['language_id']:'')));
        break;
		
      case 'delete_define_content_confirmed': 
	  
        tep_db_query ('DELETE FROM ' . TABLE_DEFINE_CONTENT . ' WHERE id=' .tep_db_input( $_POST['dcId']) . ' AND language_id=' . tep_db_input($_POST['language_id']) );
		$messageStack->add_session(DEFINE_CONTENT_MESSAGE_CONTENT_DELETED, 'success');
		tep_redirect(tep_href_link('define_content.php'));
        break;
     
      case 'delete_define_area_content_confirmed': 
	  
		// check for existing content first
        $define_content_query = tep_db_query('SELECT count(*) as total FROM ' . TABLE_DEFINE_CONTENT . ' WHERE dcId=' . tep_db_input($_POST['dcId']) );
        $define_content = tep_db_fetch_array($define_content_query);

        if ($define_content['total']>0) {
		  $messageStack->add_session(DEFINE_CONTENT_ERROR_CONTENT_STILL_EXISTS, 'error');
          tep_redirect(tep_href_link('define_content.php',(!empty($_POST['dcId'])&&!empty($_POST['language_id'])?'dcId='.$_POST['dcId'].'&language_id='.$_POST['language_id']:'')));
		} else {
         
		}
        break;
		
    }
  }

  // get content areas
  $define_content_areas = array();

  if (!isset($lng) || (isset($lng) && !is_object($lng))) {
	include('includes/classes/language.php');
	$lng = new language;
  }
  
  // get language code for TinyMCE
  $lang_code = 'en';
  foreach ($lng->catalog_languages as $key => $lang) {
    if ($lang['directory'] == $language) {
        $lang_code = $key;
        break;
    }
  }
  reset($lng->catalog_languages);
  
  require('includes/template_top.php');
?>

	<table width="100%" border="0" cellspacing="0" cellpadding="2">
      <tr>
        <td><table border="0" width="100%" cellspacing="0" cellpadding="0">
          <tr>
            <td class="pageHeading"><table border="0" cellspacing="0" cellpadding="0"><tr><td class="pageHeading"><?php echo DEFINE_CONTENT_TITLE ?> &nbsp; &nbsp; &nbsp;</td></tr></table></td>
            <td class="pageHeading" align="right"><?php echo tep_draw_separator('pixel_trans.gif', HEADING_IMAGE_WIDTH, HEADING_IMAGE_HEIGHT); ?></td>
          </tr>
        </table></td>
      </tr>
      <tr>
        <td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
      </tr>
	  
<?php
//
// insert/update edit page
//
  if (isset($action) && ($action=='define_content' || $action=='define_content_insert')) { 
    if ($action=='define_content' && !empty($_GET['language_id']) && !empty($_GET['dcId'])) { 
	  $input_language_id = (int)tep_db_prepare_input($_GET['language_id']);
	  $input_content_id = (int)tep_db_prepare_input($_GET['dcId']);
    
	$define_content_query = tep_db_query('SELECT * FROM ' . TABLE_DEFINE_CONTENT.'  WHERE id='.$input_content_id.' AND language_id='.$input_language_id );
      $define_content = tep_db_fetch_array($define_content_query);
    } else { 
      $define_content = array();
    }
?>
      <tr><?php echo tep_draw_form('define_content', 'define_content.php', ((!empty($_GET['usewysiwyg']))?'usewysiwyg='.$_GET['usewysiwyg'].'&':'') . (($action=='define_content')?'dcId='.$input_content_id.'&action=save':'action=insert'), 'post', 'enctype="multipart/form-data"') . tep_hide_session_id(); ?>
		<td><table border="0" cellspacing="0" cellpadding="2">
          <tr>
            <td class="main"><?php echo DEFINE_CONTENT_NAME_TITLE?></td>
            <td class="main">
				<?php echo tep_draw_hidden_field('dcId', $input_content_id); ?>
            	<?php echo tep_draw_input_field('page_title', stripslashes($define_content['page_title']),'id ="page_title"'); ?>&nbsp;<span>&nbsp;</span></td>
          </tr>
         <tr>  
         	 <td> <?php echo DEFINE_CONTENT_URL_TITLE;?> </td>       
             <td>
                <?php echo tep_draw_input_field('pageURL', $define_content['page_url'], " id='pageURL' readonly='readonly'");?>
             </td>
         </tr>
          <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>              
          <tr>
            <td class="main" valign="top"><?php echo DEFINE_CONTENT_CONTENT_TITLE; ?>:</td>
			<?php
           echo tep_draw_textarea_field_ckeditor('page_content', 'off', '100', '25', stripslashes($define_content['page_content']),'id="page_content"'); ?></td>
          </tr>  <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
		  <tr>
    <tr bgcolor="#eeeeee">
            <td class="main" valign="top" >SEO Description</td>
           
                <td class="main"><?php
        echo tep_draw_textarea_field('seo_description', 'soft', '70', '15', stripslashes($define_content['seo_description']));
?></td>
                </tr>
				 <tr bgcolor="#eeeeee">
            <td class="main" valign="top">SEO Keywords
			</td>
            
                <td class="main" valign="top"><?php
        echo tep_draw_input_field('seo_keywords',  stripslashes($define_content['seo_keywords']), 'placeholder="' . PLACEHOLDER_COMMA_SEPARATION . '" style="width: 300px;"');
?></td>
            
          </tr><tr bgcolor="#eeeeee">
            <td class="main" valign="top">SEO Title
			</td>            
                <td class="main" valign="top"><?php
        echo tep_draw_input_field('seo_title',  stripslashes($define_content['seo_title']), 'placeholder="" style="width: 300px;"');
?></td>
            
          </tr>
		
          <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
          <tr>
          	<td><?php echo DEFINE_CONTENT_STATUS_TITLE;?></td>
            <td><?php echo tep_draw_radio_field('status', 'yes', ($define_content['status']=='yes' ? true : false)) . '&nbsp;&nbsp;YES&nbsp;&nbsp;' . tep_draw_radio_field('status', 'no', ($define_content['status']=='no') ? true : false ) . '&nbsp;&nbsp;NO&nbsp;'; ?></td>
          <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
          <tr>
          	<td><?php echo DEFINE_CONTENT_LOGIN_VIEW_TITLE;?></td>
            <td><?php echo tep_draw_radio_field('login_view', 'yes', ($define_content['login_view']=='yes' ? true : false)) . '&nbsp;&nbsp;YES&nbsp;&nbsp;' . tep_draw_radio_field('login_view', 'no', ($define_content['login_view']=='no' ? true : false)) . '&nbsp;&nbsp;NO&nbsp;'; ?></td>
          <tr>
          <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
          <tr>
            <td class="main"><?php echo DEFINE_CONTENT_DATE_TITLE; ?>:</td>
            <td class="main"><?php echo tep_draw_input_field('define_content_date_added', (empty($define_content['define_content_date_added'])?Date("Y-m-d H:i:s"):$define_content['define_content_date_added']), '', true); ?></td>
          </tr>
<?php 
if (!empty($define_content['define_content_last_modified'])) {
?>
          <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
          <tr>
            <td class="main"><?php echo DEFINE_CONTENT_DATE_UPDATED_TITLE; ?>:</td>
            <td class="main"><?php $define_content['define_content_last_modified'] ; ?></td>
          </tr>
<?php
}
?>
          <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
          <tr>
            <td class="main" valign="top"><?php echo DEFINE_CONTENT_LANGUAGE_TITLE; ?>:</td>
            <td class="main"><table border="0" cellspacing="0" cellpadding="0" width="580"><tr><td class="main" width="24">
<?php 
			$found = false;
            foreach ($lng->catalog_languages as $key => $value) {
              if (!empty($input_language_id) && $value['id']==$input_language_id) {
			    $found = true;
                echo tep_draw_radio_field('language_id', $value['id'], true, "");
                echo tep_image(((!empty($_SERVER['HTTPS'])) ? HTTPS_CATALOG_SERVER : HTTP_CATALOG_SERVER).DIR_WS_CATALOG_LANGUAGES .  $value['directory'] . '/images/'. $value['image'], $value['name']).tep_draw_separator('pixel_trans.gif', '20', '15');
			  } elseif (empty($input_language_id)) {
			    echo tep_draw_radio_field('language_id', $value['id'], (empty($selected_first)?true:false), ""); 
                echo tep_image(((!empty($_SERVER['HTTPS'])) ? HTTPS_CATALOG_SERVER : HTTP_CATALOG_SERVER).DIR_WS_CATALOG_LANGUAGES .  $value['directory'] . '/images/'. $value['image'], $value['name']).tep_draw_separator('pixel_trans.gif', '20', '15');
			    $selected_first = true;
			  }			  
            }
			if (!empty($input_language_id) && !$found) {
			  echo '<span class="errorText">'.DEFINE_CONTENT_LANGUAGE_NOT_FOUND.'</span>';
			  echo tep_draw_hidden_field('language_id', $input_language_id);
			}

            ?></td></tr></table></td>
          </tr>
          <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
          <tr>
            <td colspan="2" class="main" align="right"><?php
            echo tep_draw_button(($action=='define_content'?'Update':IMAGE_INSERT), 'disk', null, 'primary'). 
			tep_draw_button(IMAGE_CANCEL, 'close', tep_href_link('define_content.php', ((!empty($input_content_id)&&!empty($_GET['language_id']))?'dcId='.$input_content_id.'&language_id='.$input_language_id:'')));
          ?></td>
          </tr>
        </table></td>
      </form></tr>
<?php
//
// insert/update area edit page
//
  } elseif ($action == 'define_area_content') { 
    if ( isset($_POST['dcId']) ) { 
     
    } else { 
      $define_content = array();
    }
?>
      <tr><?php echo tep_draw_form('define_area_content', 'define_content.php', (isset($_POST['dcId']) ? 'dcId=' . $_POST['dcId'] . '&action=save_area' : 'action=insert_area'), 'post', 'enctype="multipart/form-data"') . tep_hide_session_id(); ?>
        <td><table border="0" cellspacing="0" cellpadding="2">
          <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
          <tr>
            <td class="main" valign="top"><?php echo DEFINE_CONTENT_NAME_TITLE; ?>:</td>
            <td class="main"><?php echo tep_draw_input_field('define_content_name', stripslashes($define_content['define_content_name'])); ?></td>
          </tr>
          <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
          <tr>
            <td colspan="2" class="main" align="right"><?php
            echo tep_draw_button((isset($_POST['dcId'])?'Update':IMAGE_INSERT), 'disk', null, 'primary'). tep_draw_button(IMAGE_CANCEL, 'close', tep_href_link('define_content.php'));
          ?></td>
          </tr>
        </table></td>
      </form></tr>
<?php
//
// list main menu
//
  } else {
?>
      <tr>
        <td><table border="0" width="100%" cellspacing="0" cellpadding="0">
          <tr>
            <td valign="top"><table border="0" width="100%" cellspacing="0" cellpadding="2">
              <tr class="dataTableHeadingRow">
                <td class="dataTableHeadingContent" width="6%"><?php echo DEFINE_CONTENT_ID_TITLE; ?></td>
                <td class="dataTableHeadingContent"><?php echo DEFINE_CONTENT_NAME_TITLE; ?></td>
                <td class="dataTableHeadingContent" width="20%"><?php echo DEFINE_CONTENT_URL_TITLE; ?>&nbsp;</td>
                <td class="dataTableHeadingContent" width="8%"><?php echo DEFINE_CONTENT_STATUS_TITLE; ?>&nbsp;</td>
                <td class="dataTableHeadingContent" width="11%"><?php echo DEFINE_CONTENT_LOGIN_VIEW_TITLE; ?>&nbsp;</td>
                <td class="dataTableHeadingContent" width="11%"><?php echo DEFINE_CONTENT_LANGUAGE_TITLE; ?>&nbsp;</td>
              
                <td class="dataTableHeadingContent" align="center" width="10%"><?php echo DEFINE_CONTENT_ACTION_TITLE; ?>&nbsp;</td>
              </tr>
<?php
    // get languages
	$languages = array();
	foreach($lng->catalog_languages as $key => $lang) {
	  $languages[$lang['id']] = array( 'name' => $lang['name'], 'image' => $lang['image'], 'directory' => $lang['directory'] );
	}
	  
    // get content
    $define_content_query = tep_db_query('SELECT dc.id as dcId, dc.page_url, dc.page_title, dc.page_content, dc.status, dc.language_id, dc.created_date, dc.login_view  FROM ' . TABLE_DEFINE_CONTENT . ' dc ORDER BY dc.id' );
	
    $rows = 0;
    $define_content_count = 0;
	while ( $define_content = tep_db_fetch_array($define_content_query )) {

        $rows++;
        $define_content_count++;
      
        if ( ((!isset($_GET['dcId'])&&!isset($_GET['language_id'])) || ((@$_GET['dcId']==$define_content['dcId'])&&(@$_GET['language_id']==$define_content['language_id']))) && (!is_array($selected_item)) && ($action != 'define_content') ) {
          $selected_item = $define_content;
        }
        if ( (is_array($selected_item)) && (($define_content['dcId']==$selected_item['dcId'])&&($define_content['language_id']==$selected_item['language_id'])) ) {
          echo '              <tr class="dataTableRowSelected" onmouseover="this.style.cursor=\'hand\'" onclick="document.location.href=\'' . tep_href_link('define_content.php', 'dcId=' . $define_content['dcId'] . '&language_id=' . $define_content['language_id'] . '&action=define_content' ) . '\'">' . "\n";
        } else {
          echo '              <tr class="dataTableRow" onmouseover="this.className=\'dataTableRowOver\';this.style.cursor=\'hand\'" onmouseout="this.className=\'dataTableRow\'" onclick="document.location.href=\'' . tep_href_link('define_content.php', 'dcId=' . $define_content['dcId'] . '&language_id=' . $define_content['language_id']) . '\'">' . "\n";
        }
	  
?>
                <td class="dataTableContent" width="6%"><?php echo '&nbsp;' . $define_content['dcId']; ?></td>
                <td class="dataTableContent"><?php echo '&nbsp;' . $define_content['page_title']; ?></td>
                 <td class="dataTableContent" width="20%"><?php echo '&nbsp;' . $define_content['page_url']; ?></td>
                 <td class="dataTableContent" width="12%">
<?php
      if ($define_content['status'] == 'yes') {
        echo tep_image('images/icon_status_green.gif', 'Active', 10, 10) . '&nbsp;&nbsp;' . tep_image('images/icon_status_red_light.gif', '', 10, 10);
      } else {
        echo tep_image('images/icon_status_green_light.gif', '', 10, 10) . '&nbsp;&nbsp;' . tep_image('images/icon_status_red.gif', 'Inactive', 10, 10);
      }
?></td>
                 <td class="dataTableContent" width="12%">
<?php
      if ($define_content['login_view'] == 'yes') {
        echo tep_image('images/icon_status_green.gif', 'Active', 10, 10) . '&nbsp;&nbsp;' . tep_image('images/icon_status_red_light.gif', '', 10, 10);
      } else {
        echo tep_image('images/icon_status_green_light.gif', '', 10, 10) . '&nbsp;&nbsp;' . tep_image('images/icon_status_red.gif', 'Inactive', 10, 10);
      }
?></td>
                <td class="dataTableContent" width="12%"><?php if (!empty($languages[$define_content['language_id']]['image'])) { echo tep_image(  ((!empty($_SERVER['HTTPS'])) ? HTTPS_CATALOG_SERVER : HTTP_CATALOG_SERVER).DIR_WS_CATALOG_LANGUAGES . $languages[$define_content['language_id']]['directory'] . '/images/'. $languages[$define_content['language_id']]['image'], $languages[$define_content['language_id']]['name']); } else { echo DEFINE_CONTENT_LANGUAGE_NOT_FOUND_SHORT; } ?></td>
                <td class="dataTableContent" align="right"><?php if (($define_content['dcId'] == $_GET['dcId']) && ($define_content['language_id'] == $_GET['language_id'])) { echo tep_image('images/icon_arrow_right.gif', ''); } else { echo '<a href="' . tep_href_link('define_content.php', 'dcId=' . $define_content['dcId'] . '&language_id=' . $define_content['language_id'] . '&action=define_content' ) . '">' . tep_image('images/icon_info.gif', IMAGE_ICON_INFO) . '</a>'; } ?>&nbsp;</td>
              </tr>
<?php 
    }
?>


              <tr>
                <td colspan="7"><table border="0" width="100%" cellspacing="0" cellpadding="2">
                  <tr>
                    <td class="smallText"><?php echo '<br>' . DEFINE_CONTENT_ITEMS . '&nbsp;' . $define_content_count; ?></td>
                    <?php echo tep_draw_form('delete_define_content', 'define_content.php', '', 'GET') . tep_draw_hidden_field('action', 'define_content_insert') . tep_hide_session_id(); ?><td align="right" class="smallText"><?php echo  tep_draw_button(IMAGE_INSERT, 'disk', null, 'primary'); ?>&nbsp;</td></form>
                  </tr>
                </table></td>
              </tr>
            </table></td>
<?php

    // item management
    $heading = array();
    $contents = array();
    if  (($action!='delete_define_area_content' ) && ( !empty($selected_item['dcId'])) ) {
      switch ($action) {
        case 'delete_define_content': //generate box for confirming a content deletion

          $heading[] = array('text'   => '<b>' . DEFINE_CONTENT_DELETE_ITEM_TITLE . '</b>');
        
          $contents = array('form'    => tep_draw_form('delete_define_content', 'define_content.php', 'action=delete_define_content_confirmed') . tep_draw_hidden_field('dcId', $_GET['dcId']) . tep_draw_hidden_field('language_id', $_GET['language_id']) . tep_hide_session_id());
          $contents[] = array('text'  => DEFINE_CONTENT_DELETE_ITEM_INTRO);
          $contents[] = array('text'  => '<br><b>' . $selected_item['define_content_name'] . '</b>' . ' &nbsp; ' . tep_image(  ((!empty($_SERVER['HTTPS'])) ? HTTPS_CATALOG_SERVER : HTTP_CATALOG_SERVER).DIR_WS_CATALOG_LANGUAGES . $languages[$_GET['language_id']]['directory'] . '/images/'. $languages[$_GET['language_id']]['image'], $languages[$_GET['language_id']]['name']) );
        
          $contents[] = array('align' => 'center',
                              'text'  => '<br>' . tep_draw_button(IMAGE_DELETE, 'trash', null, 'primary') . tep_draw_button(IMAGE_CANCEL, 'close', tep_href_link('define_content.php', 'dcId=' . $selected_item['dcId'] . '&language_id=' . $selected_item['language_id'])) . '<br><br>');
          break;

        default:
          if ($rows > 0) {
            if (is_array($selected_item)) { //an item is selected, so make the side box
              $heading[] = array('text'   => '<b>' . $selected_item['define_content_name'] . ' ' . DEFINE_CONTENT_CONTENT_TITLE . '</b>');
              $contents = array('form'    => tep_draw_form('define_area_content_edit', 'define_content.php', '', 'get') . tep_draw_hidden_field('action', 'define_content') . tep_draw_hidden_field('dcId', $selected_item['dcId']) . tep_draw_hidden_field('language_id', $selected_item['language_id']) . tep_hide_session_id());
			  $contents[] = array('align' => 'center', 
                                  'text'  => '<br>' . ($WYSIWYG_on==true||$FCK_WYSIWYG_on==true?str_replace('>',' id="usewysiwyg" style="cursor: pointer;">',tep_draw_checkbox_field('usewysiwyg', 'true', ($_COOKIE['dfusewysiwyg']=='true'?true:false))) . ' <label for="usewysiwyg" title="' . DEFINE_CONTENT_USE_WYSIWYG . '">' . DEFINE_CONTENT_USE_WYSIWYG . '</label><br><br>':'') . tep_draw_button(IMAGE_EDIT, 'document', null, 'primary')
								             . tep_draw_button(IMAGE_DELETE, 'trash', tep_href_link('define_content.php', 'dcId=' . $selected_item['dcId'] . '&language_id=' . $selected_item['language_id'] . '&action=delete_define_content')) . '<br><br>'
											 . '</form>' );
            }
          } else { // create
            $heading[] = array('text' => '<b>' . DEFINE_CONTENT_EMPTY . '</b>');
            $contents[] = array('text' => DEFINE_CONTENT_NO_CHILDREN);
          }
          break;
      }
    } elseif ($action!='delete_define_area_content') {
      $heading[] = array('text'   => '<b>' . DEFINE_CONTENT_NOT_AVAILABLE . '</b>');
      $contents[] = array('text'  => '<br>'.DEFINE_CONTENT_NOT_AVAILABLE.'<br><br>');
	}

    if  ($action!='delete_define_content' ) {
      // area management
      $heading2 = array();
      $contents2 = array();
      switch ($action) {
        case 'delete_define_area_content': //generate box for confirming a content deletion

          $heading2[] = array('text'   => '<b>' . DEFINE_CONTENT_MANAGE_AREAS . '</b>');
        
          $contents2 = array('form'    => tep_draw_form('delete_define_area_content', 'define_content.php', 'action=delete_define_area_content_confirmed') . tep_draw_hidden_field('dcId', $_GET['dcId']) . tep_hide_session_id() );
          $contents2[] = array('text'  => DEFINE_CONTENT_DELETE_ITEM_INTRO);
          $contents2[] = array('text'  => '<br><b>' . DEFINE_CONTENT_DELETE_ITEM_ID_TITLE . ' ' . $_GET['dcId'] . '</b>');
        
          $contents2[] = array('align' => 'center',
                               'text'  => '<br>' . tep_image_submit('button_delete.gif', IMAGE_DELETE) . ' <a href="' . tep_href_link('define_content.php') . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>');
          break;

        
      }
    }
	
    if ( (@tep_not_null($heading)) && (@tep_not_null($contents)) || (@tep_not_null($heading2)) && (@tep_not_null($contents2)) ) {
      echo '            <td width="25%" valign="top">' . "\n";

      if ( (@tep_not_null($heading)) && (@tep_not_null($contents)) ) {
        $box = new box;
        echo $box->infoBox($heading, $contents);
		
        echo '<br>';
      }
      if ( (@tep_not_null($heading2)) && (@tep_not_null($contents2)) ) {
        $box = new box;
        echo $box->infoBox($heading2, $contents2);
      }
	
      echo '            </td>' . "\n";
    }
	
?>
          </tr>
        </table></td>
      </tr>
<?php
  }
?>
    </table>
<?php 
    require('includes/template_bottom.php');
	require('includes/application_bottom.php');
?>
<script type="text/javascript">
var pageUrl = '<?php echo tep_href_link('define_content.php');?>';
$(function(){
		
	$('#page_title').blur(function(){
		if($(this).val() != ""){
			var pageURL = ($(this).val().replace(/[^a-zA-Z0-9]/g, ' ').replace(/\s+/g, " "));
			
			$.ajax({
				data 	   : "action=ajax&data=" + pageURL,
				type 	   : "POST",
				dataType   : "html",
				url 	   : pageUrl,
				beforeSend : function(){},
				success	   : function(data){
					if(data == false){
						$("#page_title").parent().find('span').html('Already Exists');
					}else{
						$("#pageURL").val(data);
					}
				}
			});
		}else{
			$("#pageURL").val('');
		}
	});
});
</script>