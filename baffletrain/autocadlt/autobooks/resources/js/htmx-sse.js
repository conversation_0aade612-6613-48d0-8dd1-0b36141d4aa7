/*
Server Sent Events Extension
============================
This extension adds support for Server Sent Events to htmx.
This allows you to trigger client-side events based on server-side events.
*/

(function(){

    /** @type {import("../htmx").HtmxInternalApi} */
    var api;

    htmx.defineExtension('sse', {

        init: function(apiRef) {
            api = apiRef;
        },

        onEvent: function(name, evt) {
            if (name === "htmx:beforeCleanupElement") {
                var element = evt.target;
                var sseSourceElt = api.getClosestMatch(element, function (parent) {
                    return parent.sseSource != null;
                });
                if (sseSourceElt) {
                    var source = sseSourceElt.sseSource;
                    if (source) {
                        source.close();
                        sseSourceElt.sseSource = null;
                    }
                }
            }
        },

        transformResponse : function(text, xhr, elt) {
            var sseSourceElt = api.getClosestMatch(elt, function (parent) {
                return parent.hasAttribute && parent.hasAttribute("sse-connect");
            });

            if (sseSourceElt) {
                // Connect to the SSE source
                var sourceUrl = sseSourceElt.getAttribute("sse-connect");
                var source = new EventSource(sourceUrl);
                sseSourceElt.sseSource = source;

                // Find all elements that want to respond to SSE events
                var sseSwapElts = api.findAll(sseSourceElt, "[sse-swap]");

                // Set up listeners for each event type
                sseSwapElts.forEach(function(elt) {
                    var eventName = elt.getAttribute("sse-swap");
                    var target = elt.getAttribute("sse-target") || elt;
                    if (target === "this") {
                        target = elt;
                    } else {
                        target = api.findAll(sseSourceElt, target)[0] || elt;
                    }

                    source.addEventListener(eventName, function(event) {
                        // Handle the SSE event
                        if (elt.getAttribute("sse-swap-no-content") === "true") {
                            // Just trigger the event
                            api.triggerEvent(elt, "sse:" + eventName, {
                                data: event.data,
                                details: event
                            });
                        } else {
                            // Swap the content
                            var data = event.data;
                            api.triggerEvent(elt, "htmx:sseBeforeMessage", {
                                data: data,
                                details: event
                            });
                            
                            // Swap the content
                            api.withExtensions(elt, function(extension) {
                                data = extension.transformResponse(data, null, elt) || data;
                            });
                            
                            var swapSpec = api.getSwapSpecification(elt);
                            api.swap(target, data, swapSpec);
                            
                            api.triggerEvent(elt, "htmx:sseAfterMessage", {
                                data: data,
                                details: event
                            });
                            
                            // Trigger the event
                            api.triggerEvent(elt, "sse:" + eventName, {
                                data: event.data,
                                details: event
                            });
                        }
                    });
                });

                // Set up error handling
                source.onerror = function(error) {
                    api.triggerEvent(sseSourceElt, "htmx:sseError", {
                        error: error,
                        source: source
                    });
                    
                    // Try to reconnect after a delay
                    if (source.readyState === EventSource.CLOSED) {
                        setTimeout(function() {
                            if (sseSourceElt.sseSource === source) {
                                source = new EventSource(sourceUrl);
                                sseSourceElt.sseSource = source;
                            }
                        }, 5000); // 5 second delay before reconnecting
                    }
                };

                // Set up open handling
                source.onopen = function() {
                    api.triggerEvent(sseSourceElt, "htmx:sseOpen", {
                        source: source
                    });
                };
            }

            return text;
        }
    });
})();
