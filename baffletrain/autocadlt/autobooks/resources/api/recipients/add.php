<?php
// API endpoint to add a recipient
// This will be called via HTMX when the user clicks the "Add" button

// Get the email from the form
$email = isset($_POST['new_recipient_email']) ? trim($_POST['new_recipient_email']) : '';

// Validate the email
if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    // Return an error message
    header('HX-Trigger: {"showNotification": {"type": "error", "message": "Please enter a valid email address"}}');
    exit;
}

// Get existing recipients from the session
session_start();
if (!isset($_SESSION['recipients'])) {
    $_SESSION['recipients'] = [];
}

// Check if we already have 4 recipients
if (count($_SESSION['recipients']) >= 4) {
    header('HX-Trigger: {"showNotification": {"type": "error", "message": "You can only add up to 4 recipients"}}');
    exit;
}

// Check if the email is already in the list
if (in_array($email, $_SESSION['recipients'])) {
    header('HX-Trigger: {"showNotification": {"type": "error", "message": "This email is already in the list"}}');
    exit;
}

// Add the email to the list
$_SESSION['recipients'][] = $email;

// Return the updated list of recipients
$index = 0;
foreach ($_SESSION['recipients'] as $recipient) {
    ?>
    <div class="flex items-center justify-between py-2 border-b">
        <div>
            <span><?= htmlspecialchars($recipient) ?></span>
            <input type="hidden" name="additionalRecipients[<?= $index ?>].email" value="<?= htmlspecialchars($recipient) ?>">
        </div>
        <button type="button" 
                hx-delete="api/recipients/remove?email=<?= urlencode($recipient) ?>"
                hx-target="#recipients-list"
                hx-swap="innerHTML"
                class="text-red-600 hover:text-red-800">
            Remove
        </button>
    </div>
    <?php
    $index++;
}
?>
