<?php
namespace api\subscriptions\settings;
use autodesk_api\autodesk_api;
use edge\Edge;
Print_rr($input_params, 'settingsapicall',true,true);

function add_replacement_row($p) {
    return Edge::render('subscription-table-config-replacement-row', [
        'search' => '',
        'replace' => '',
        'is_new' => true
    ]);
}

function add_column_row($p) {
    print_rr($p, 'add_column_row');
    return Edge::render('subscription-table-config-column', [
        'index' => $p['index'],
        'column' => [
            'label' => '',
            'field' => '',
            'auto_filter' => false,
            'filters' => []
        ],
        'db_fields' => get_db_fields(),
        'is_new' => true
    ]);
}


function add_filter_row($p) {
    return Edge::render('subscription-table-config-filter-row', [
        'columnIndex' => $p['columnIndex'],
        'filterKey' => '',
        'filterValue' => '',
        'is_new' => true
    ]);
}

function save_subscription_config($p) {
    $config = [
        'table_id' => 'subscriptions',
        'db_table' => 'subs',
        'columns' => []
    ];
    print_rr($p['columns'], 'save_subscription_config');
    // Process columns
    foreach ($p['columns'] as $key => $column) {
        $columnConfig = [];
        $columnConfig['label'] = $column['label'];
        $columnConfig['field'] = strpos($column['field']['input'],',') ? explode(',',$column['field']['input']) : $column['field']['input'];
        $columnConfig['field'] = is_array($columnConfig['field']) ? array_map('trim', $columnConfig['field']) : trim($columnConfig['field']);

        // Add auto_filter if specified
        if (isset($column['auto_filter'])) {
            $columnConfig['auto_filter'] = true;
            $columnConfig['filter_limit'] = 100;
        }

        // Process filters if provided
        if (isset($column['filter_keys']) && isset($column['filter_values']) &&
            is_array($column['filter_keys']) && is_array($column['filter_values'])) {
            $filters = [];
            foreach ($column['filter_keys'] as $index => $key) {
                if (!empty($key) && isset($column['filter_values'][$index])) {
                    $filters[$key] = $column['filter_values'][$index];
                }
            }
            if (!empty($filters)) $columnConfig['filters'] = $filters;
        }

        $config['columns'][$column['field_name']] = $columnConfig;
    }

    // Process replacements
    $replacements = [];
    if (isset($p['replacement_search']) && isset($p['replacement_replace'])) {
        foreach ($p['replacement_search'] as $key => $search) {
            if (!empty($search) && isset($p['replacement_replace'][$key])) {
                $replacements[$search] = $p['replacement_replace'][$key];
            }
        }
    }

    // Save configurations
    autodesk_api::database_set_storage('subscription_table_config', json_encode($config));
    autodesk_api::database_set_storage('subscription_replacements', json_encode($replacements));

    return Edge::render('alert', [
        'type' => 'success',
        'message' => 'Configuration saved successfully'
    ]);
}