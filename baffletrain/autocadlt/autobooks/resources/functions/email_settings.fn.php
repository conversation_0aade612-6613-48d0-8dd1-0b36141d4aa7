<?php
use autodesk_api\autodesk_api;
use edge\Edge;

function tcs_draw_email_send_rules_widget($rules) {
    print_rr($rules);
    $first_class = 'relative inline-flex items-center rounded-l-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-10';
    $main_class = 'relative -ml-px inline-flex items-center bg-white px-3 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-10';
    $last_class = 'relative -ml-px inline-flex items-center rounded-r-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-10';    
    
     $widget  = tcs_draw_email_send_rules_widget_buttons($rules);
     $widget .= '<div class="relative flex items-center" x-show="inputVisible" x-transition>';
     $widget .= tcs_draw_input([      
               'name'=>'new_rule', 
               'type'=>'text',
               'id'=>'new_rule',
               'class_override' => true,
               'class'=>'block w-full border-0 p-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm/6',
               'placeholder'=>'Edit Rule'
               
     ]);
     $widget .= '</div>';
     $widget .= tcs_draw_button([
        'x-show'=>'editing',  
        'label' => '+',
        'class' => $last_class,
        'class_override' => true,
        'hx-post'=> APP_ROOT . "api/email_history",
        'hx-include'=>'#new_rule',
        'hx-target'=>'#rules_buttons', 
        'hx-swap'=>'outerHTML',
        'hx-vals' => json_encode([          
          'action' => 'email_rule_add'
        ]),
    ]); 
    
    $widget .= tcs_draw_button([
        'x-show'=>'editing', 
        '@click'=>'saveRule',
        'x-transition',
        'class' => $last_class,
        'label' => 'save',
        'class_override' => true,
    ]);

    $widget .= tcs_draw_button([
        'x-show'=>'!editing', 
        '@click'=> "editing = true; inputVisible = true;", 
        'x-transition',
        'label' => 'edit',
        'class' => $last_class,
        'class_override' => true,
    ]); 

    return "<nav id='rules_nav' 
    class='-space-x-px isolate inline-flex rounded-md shadow-sm' 
 
    x-data='{ 
        editing: false, 
        inputVisible: false,
        saveRule() {
            this.inputVisible = false;
            this.editing = false;
            // Trigger HTMX to save the data
        },
        triggerDelete(event) {
            if (this.editing) {
                // Perform delete action
                htmx.trigger(event.target, \"delete\");
            }
        }
     }'>{$widget}</nav>"; 


}

function tcs_draw_email_send_rules_widget_buttons($rules) {
    $first_class = 'relative inline-flex items-center rounded-l-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-10';
    $main_class = 'relative -ml-px inline-flex items-center bg-white px-3 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-10';
    
    $buttons = '<div class="relative flex items-center" id="rules_buttons">';
    foreach ($rules as $key => $rule){
        if ($key == 0) {
            $class = $first_class;
        } else {
            $class = $main_class;
        }
        $buttons .= tcs_draw_button([
            'class' => $class,
            'class_override' => true,
            '@click="triggerDelete($event)";this.remove();',
            'container' => false,
            'label' => $rule,
            'hx-trigger' => "delete",
            'hx-post' => APP_ROOT . "api/email_history", //returns full nav with item removed
            'hx-target' => "#rules_buttons",
            'hx-vals' => json_encode(['action' => 'email_rule_delete', 'rule' => $rule]),
            'hx-swap' => "outerHTML",
        ]);
     };
     $buttons .= '</div>';
     return $buttons;
};

        /*
<div>
  <label for="email" class="text-sm/6 block font-medium text-gray-900">Search candidates</label>
  <div class="flex mt-2 rounded-md shadow-sm">
    <div class="relative flex grow items-stretch focus-within:z-10">
      <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
        <svg class="size-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
          <path d="M7 8a3 3 0 1 0 0-6 3 3 0 0 0 0 6ZM14.5 9a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5ZM1.615 16.428a1.224 1.224 0 0 1-.569-1.175 6.002 6.002 0 0 1 11.908 0c.058.467-.172.92-.57 1.174A9.953 9.953 0 0 1 7 18a9.953 9.953 0 0 1-5.385-1.572ZM14.5 16h-.106c.07-.297.088-.611.048-.933a7.47 7.47 0 0 0-1.588-3.755 4.502 4.502 0 0 1 5.874 2.636.818.818 0 0 1-.36.98A7.465 7.465 0 0 1 14.5 16Z" />
        </svg>
      </div>
      <input type="email" name="email" id="email" class="w-full block py-1.5 pl-10 text-gray-900 rounded-none rounded-l-md border-0 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 placeholder:text-gray-400 sm:text-sm/6" placeholder="John Smith">
    </div>
    <button type="button" class="-ml-px relative inline-flex items-center gap-x-1.5 px-3 py-2 text-sm font-semibold text-gray-900 rounded-r-md ring-1 ring-inset ring-gray-300 hover:bg-gray-50">
      <svg class="-ml-0.5 size-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
        <path fill-rule="evenodd" d="M2 3.75A.75.75 0 0 1 2.75 3h11.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 3.75ZM2 7.5a.75.75 0 0 1 .75-.75h6.365a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 7.5ZM14 7a.75.75 0 0 1 .55.24l3.25 3.5a.75.75 0 1 1-1.1 1.02l-1.95-2.1v6.59a.75.75 0 0 1-1.5 0V9.66l-1.95 2.1a.75.75 0 1 1-1.1-1.02l3.25-3.5A.75.75 0 0 1 14 7ZM2 11.25a.75.75 0 0 1 .75-.75H7A.75.75 0 0 1 7 12H2.75a.75.75 0 0 1-.75-.75Z" clip-rule="evenodd" />
      </svg>
      Sort
    </button>
  </div>
</div>
   
    <!-- Input Field -->
    <div class=>"relative flex items-center" x-show=>"inputVisible" x-transition>
        <input id=>"new_rule" 
               'class'=>"w-24 border border-gray-300 rounded-md shadow-sm text-sm p-2"
               'name'=>"new_rule" 
               'type'=>"text"
               'placeholder'=>"Edit Rule" 
               'hx-trigger'=>"change" 
               'hx-post'=>"api/autodesk_update_email_settings_rules_edit" 
               'hx-target'=>"#rules_nav" 
               'hx-swap'=>"outerHTML">
    </div>

    <!-- Edit Button -->
    <button x-show="!editing" 
            @click="editing = true; inputVisible = true;" 
            class="px-3 py-2 text-sm text-white bg-indigo-600 rounded-md ring-1 ring-indigo-700 shadow-sm hover:bg-indigo-500"
            x-transition>
        Edit
    </button>

    <!-- Save Button -->
    <button x-show="editing" 
            @click="saveRule" 
            class="px-3 py-2 text-sm text-white bg-green-600 rounded-md ring-1 ring-green-700 shadow-sm hover:bg-green-500"
            x-transition>
        Save
    </button>

    <!-- Additional Buttons -->
    <button x-show="!editing" 
            @click="this.remove()" 
            class="px-3 py-2 text-sm text-white bg-red-600 rounded-md ring-1 ring-red-700 shadow-sm hover:bg-red-500"
            x-transition>
        Delete
    </button>
</nav>

*/
//class='-space-x-px isolate inline-flex rounded-md shadow-sm' aria-label='Pagination'
 
?>