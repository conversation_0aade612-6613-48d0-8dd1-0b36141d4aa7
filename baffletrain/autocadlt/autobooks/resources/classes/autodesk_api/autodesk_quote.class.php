<?php

namespace autodesk_api;
use http\Exception;
use system\users;

include_once(FS_CLASSES . '/autodesk_api/config.php');

class autodesk_quote{
    private autodesk_api_interface $api;
    public array $quote_data;

    private autodesk_subscription $subscription;
    public static array $quote_column_mapping = [
            'agent_account' => [
                'table' => 'autodesk_accounts',
                'key' => 'account_csn',
                'columns' => [
                    'agentAccount.accountCsn' => 'account_csn',
                    'agentAccount.name' => 'name',
                    'agentAccount.addressLine1' => 'address1',
                    'agentAccount.addressLine2' => 'address2',
                    'agentAccount.city' => 'city',
                    'agentAccount.stateProvinceCode' => 'state_province_code',
                    'agentAccount.postalCode' => 'postal_code',
                    'agentAccount.countryCode' => 'country_code',
                    'agentAccount.stateProvince' => 'state_province',
                    'account_type' => 'agent'
                ],
            ],
            'agent_contact' => [
                'table' => 'autodesk_accounts',
                'key' => 'autodesk_accounts',
                'columns' => [
                    'agentContact.contactCsn' => 'account_csn',
                    'agentContact.email' => 'email',
                    'agentContact.firstName' => 'first_name',
                    'agentContact.lastName' => 'last_name',
                    'agentContact.phone' => 'phone',
                    'account_type' => 'agent',
                ],
            ],
            'end_customer' => [
                'table' => 'autodesk_accounts',
                'key' => 'account_csn',
                'columns' => [
                    'endCustomer.accountCsn' => 'account_csn',
                    'endCustomer.name' => 'name',
                    'endCustomer.addressLine1' => 'address1',
                    'endCustomer.addressLine2' => 'address2',
                    'endCustomer.city' => 'city',
                    'endCustomer.stateProvinceCode' => 'state_province_code',
                    'endCustomer.postalCode' => 'postal_code',
                    'endCustomer.countryCode' => 'country_code',
                    'endCustomer.stateProvince' => 'state_province',
                    'endCustomer.isIndividual' => 'individual_flag'
                ],
                'extra' => [
                    'account_type' => 'customer'
                ]
            ],
            'quote_contact' => [
                'table' => 'autodesk_accounts',
                'key'=>'account_csn',
                'columns' => [
                    'quoteContact.contactCsn' => 'account_csn',
                    'quoteContact.email' => 'email',
                    'quoteContact.firstName' => 'first_name',
                    'quoteContact.lastName' => 'last_name',
                    'quoteContact.phone' => 'phone',
                    'contact_type' => 'account_type'
                ],
            ],
            'quote' => [
                'table' => 'autodesk_quotes',
                'key'=>'quote_number',
                'columns' => [
                    'quoteNumber' => 'quote_number',
                    'quoteStatus' => 'quote_status',
                    'quoteExpirationDate' => 'quote_expiration_date',
                    'quotedDate' => 'quoted_date',
                    'quoteCreatedTime' => 'quote_created_time',
                    'quoteLanguage' => 'quote_language',
                    'opportunityNumber' => 'quote_opportunity_number',
                    'skipDDACheck' => 'skip_dda_check',
                    'pricing.currency' => 'quote_currency',
                    'pricing.totalListAmount' => 'total_list_amount',
                    'pricing.totalNetAmount' => 'total_net_amount',
                    'pricing.totalAmount' => 'total_amount',
                    'pricing.totalDiscount' => 'total_discount',
                    'pricing.estimatedTax' => 'estimated_tax',
                    'paymentTerms.code' => 'payment_terms_code',
                    'paymentTerms.description' => 'payment_terms_description',
                ],
                'extra' => [
                    'additional_recipients' => '<json_encode>additionalRecipients</json_encode>',
                    "end_customer" => "<group_insert_id>end_customer</group_insert_id>",
                    "quote_contact" => "<group_insert_id>quote_contact</group_insert_id>",
                    "agent_account" => "<group_insert_id>agent_account</group_insert_id>",
                    "admin" => "<json_encode>admin</json_encode>"
                ]
            ],
            'line_item' => [
                'table' => 'autodesk_quote_line_items',
                'key'=>'line_number',
                'columns' => [
                    'lineItems.lineNumber' => 'line_number',
                    'lineItems.quantity' => 'quantity',
                    'lineItems.action' => 'action',
                    'lineItems.specialProgramDiscountCode' => 'special_program_discount_code',
                    'lineItems.startDate' => 'start_date',
                    'lineItems.endDate' => 'end_date',
                    'lineItems.offeringId' => 'offering_id',
                    'lineItems.offeringCode' => 'offering_code',
                    'lineItems.offeringName' => 'offering_name',
                    'lineItems.marketingName' => 'marketing_name',
                    'lineItems.unitOfMeasure' => 'unit_of_measure',
                    'lineItems.referenceSubscription' => 'reference_subscription',
                    'lineItems.promotionCode' => 'promotion_code',
                    'lineItems.promotionDescription' => 'promotion_description',
                    'lineItems.promotionEndDate' => 'promotion_end_date',
                    'lineItems.declaredValueBasedOn' => 'declared_value_based_on',
                    'lineItems.scopeOfUse' => 'scope_of_use',
                    'lineItems.scopeDetails' => 'scope_details',
                    'lineItems.numberOfProjectsIncluded' => 'number_of_projects_included',
                    'lineItems.specialProgramDiscountDescription' => 'special_program_discount_description',
                    'lineItems.annualDeclaredValue' => 'annual_declared_value',
                    'lineItems.pricing.unitSRP' => 'unit_srp',
                    'lineItems.pricing.extendedSRP' => 'extended_srp',
                    'lineItems.pricing.discountsApplied' => 'discounts_applied',
                    'lineItems.pricing.endUserPrice' => 'end_user_price',
                    'lineItems.pricing.currency' => 'currency',
                    'lineItems.subscription.id' => 'subscription_id',
                    'lineItems.subscription.quantity' => 'subscription_quantity',
                    'lineItems.subscription.endDate' => 'subscription_endDate'
                ],
                'extra' => [
                    "quote_id" => "<group_insert_id>quote"
                ]
            ],
            'ref_subs' => [
                'table' => 'autodesk_quote_line_items_reference_subscriptions',
                'key'=>'id',
                'columns' => [
                    'lineItems.referenceSubscriptions.id' => 'id', //
                    'lineItems.referenceSubscriptions.quantity' => 'quantity',
                    'lineItems.referenceSubscriptions.endDate' => 'endDate',
                    'lineItems.referenceSubscriptions.term' => 'term',
                    'lineItems.referenceSubscriptions.term.code' => 'term_code',
                    'lineItems.referenceSubscriptions.term.description' => 'description',
                    'lineItems.referenceSubscriptions.offeringCode' => 'offeringCode',
                    'lineItems.referenceSubscriptions.offeringName' => 'offeringName',
                    'lineItems.referenceSubscriptions.marketingName' => 'marketingName',
                    'lineItems.referenceSubscriptions.annualDeclaredValue' => 'annualDeclaredValue',
                    'lineItems.referenceSubscriptions.pricingMethod' => 'pricingMethod',
                    'lineItems.referenceSubscriptions.pricingMethod.code' => 'code',
                    'lineItems.referenceSubscriptions.pricingMethod.description' => 'description'
                ],
                'extra' => [
                    "line_item_id" => "<group_insert_id>line_item"
                ]
            ],
            'bill_plan' => [
                'table' => 'autodesk_quote_bill_plans',
                'key'=>'id',
                'columns' => [
                    'billPlans.billDate' => 'bill_date',
                    'billPlans.startDate' => 'start_date',
                    'billPlans.endDate' => 'end_date',
                    'billPlans.extendedSRP' => 'extended_srp',
                    'billPlans.discountsApplied' => 'discounts_applied',
                    'billPlans.exclusiveDiscountsApplied' => 'exclusive_discounts_applied',
                    'billPlans.amount' => 'amount'
                ],
                'extra' => [
                    "quote_number" => "<group_insert_id>quote"
                ]
            ],
        ];


    public function __construct($api)    {
        $this->api = $api;
        $this->subscription = new autodesk_subscription($api);
    }

    public array $base_quote_data = [
        "currency" => "GBP",
        "agentAccount" => ["accountCsn" => ADWS_CSN],
        "agentContact" => ["email" => "<EMAIL>"],
        "skipDDACheck" => true
    ];
//    {
//    "currency": "USD",
//    "agentAccount": {
//    "accountCsn": "**********"
//    },
//    "agentContact": {
//        "email": "<EMAIL>"
//      },
//      "endCustomer": {
//        "accountCsn": "**********"
//      },
//      "quoteContact": {
//        "email": "<EMAIL>"
//      },
//      "additionalRecipients": [
//        {
//            "email": "<EMAIL>"
//        }
//      ],
//      "skipDDACheck": true,
//      "agentQuoteReference": "QUOTE-003870",
//      "lineItems": [
//        {
//            "action": "Renewal",
//          "agentLineReference": "L-000003",
//          "quantity": 10000,
//          "subscriptionId": "**************",
//          "promotionCode": "ABC"
//        }
//      ]
//    }

    public function create_renewal($subscriptionId, $user_id): string{
        $user = users::getUserById($user_id);
        $columns = [
            "subs_subscriptionId",
            "subs_quantity",
            "subs_term",
            "subs_offeringId",
            "subs_offeringCode",
            "subs_offeringName",
            "endcust_account_csn",
            "endcust_primary_admin_email"
        ];
        $subscription = $this->subscription->get($columns,['where' => ['subs.subscriptionId' => ['=',$subscriptionId ] ] ]);
        $line_items = [
            [
                "action" => "Renewal",
                "quantity" => $subscription['subs_quantity'],
                "subscriptionId" => $subscription['subs_subscriptionId']
            ]
        ];
        return $this->create(
            $subscription['endcust_account_csn'],
            $user['email'],
            $subscription['endcust_primary_admin_email'],
            $subscription['endcust_primary_admin_email'],
            $line_items
        );



    }
    public function finalize_from_api($json_payload) {
        $mapped_keys = [];
        $skipped_keys = [];

        // Log the start of the process
        tcs_log('Starting finalization processing at ' . date('Y-m-d H:i:s'), 'quote_finalization');

        try {
            // If no payload was passed, try to get it from the request
            if ($json_payload === null) {
                // Get the raw POST data
                $raw_post_data = file_get_contents('php://input');
                $json_payload = json_decode($raw_post_data, true);

                if (!$json_payload) {
                    throw new \Exception('Invalid JSON payload received');
                }
            }



            // Log the incoming payload
            tcs_log('Incoming payload: ' . print_r($json_payload, true), 'quote_finalization');

            // Extract quoteNumber from the payload
            $quoteNumber = $json_payload['payload']['quoteNumber'] ?? null;
            if (!$quoteNumber) {
                tcs_log('No quoteNumber found in payload', 'quote_finalization');
                throw new \Exception('No quote ID found in payload');
            }
        } catch (\Exception $e) {
            // Log any exceptions that occur
            tcs_log('Error in finalization processing: ' . $e->getMessage(), 'quote_finalization');
            tcs_log('Trace: ' . $e->getTraceAsString(), 'quote_finalization');

            // Return an error response
            $response = [
                'status' => 'error',
                'message' => $e->getMessage()
            ];

            return json_encode($response);
        }
        return $this->api->quotes_finalize($quoteNumber);
    }

    public function finalize($quote_number) {
        return $this->api->quotes_finalize($quote_number);
    }
    public function create(string $endcust_csn,string $quote_contact, string $additional_recipients, string $agent_contact, array $line_items):string{
        $quoteData = $this->base_quote_data;
        $quoteData['endCustomer'] = ['accountCsn' => $endcust_csn];
        $quoteData['quoteContact'] = ['email' => $quote_contact];
        $quoteData['additionalRecipients'] = [['email' => $additional_recipients]];
        $quoteData['agentContact'] = ['email' => $agent_contact];
        $quoteData['lineItems'] = $line_items;
        return json_encode($quoteData, JSON_PRETTY_PRINT);
    }

    /**
     * Get the schema for quote data type casting
     *
     * @return array The schema defining field types
     */
    private function get_quote_data_schema() {
        return [
            // Root level fields
            'currency' => 'string',
            'skipDDACheck' => 'boolean',
            'agentQuoteReference' => 'string',

            // Agent account fields
            'agentAccount' => [
                'accountCsn' => 'string'
            ],

            // Agent contact fields
            'agentContact' => [
                'email' => 'string',
                'firstName' => 'string',
                'lastName' => 'string',
                'phone' => 'string'
            ],

            // End customer fields
            'endCustomer' => [
                'accountCsn' => 'string',
                'name' => 'string',
                'isIndividual' => 'boolean',
                'addressLine1' => 'string',
                'addressLine2' => 'string',
                'city' => 'string',
                'stateProvinceCode' => 'string',
                'postalCode' => 'string',
                'countryCode' => 'string'
            ],

            // Quote contact fields
            'quoteContact' => [
                'email' => 'string',
                'firstName' => 'string',
                'lastName' => 'string',
                'phone' => 'string',
                'preferredLanguage' => 'string'
            ],

            // Additional recipients fields
            'additionalRecipients' => [
                '*' => [
                    'email' => 'string'
                ]
            ],

            // Line items fields
            'lineItems' => [
                '*' => [
                    'action' => 'string',
                    'agentLineReference' => 'string',
                    'quantity' => 'integer',
                    'subscriptionId' => 'string',
                    'promotionCode' => 'string',
                    'offeringId' => 'string',
                    'offeringName' => 'string',
                    'offeringCode' => 'string',
                    'startDate' => 'string',
                    'endDate' => 'string',
                    'referenceSubscriptionId' => 'string',
                    'opportunityLineItemId' => 'string',
                    'offer' => [
                        'term' => [
                            'code' => 'string',
                            'description' => 'string'
                        ],
                        'accessModel' => [
                            'code' => 'string',
                            'description' => 'string'
                        ],
                        'intendedUsage' => [
                            'code' => 'string',
                            'description' => 'string'
                        ],
                        'connectivity' => [
                            'code' => 'string',
                            'description' => 'string'
                        ],
                        'servicePlan' => [
                            'code' => 'string',
                            'description' => 'string'
                        ]
                    ]
                ]
            ]
        ];
    }

    /**
     * Cast quote data values to their correct types based on schema
     *
     * @param array $data The quote data to cast
     * @param array|null $schema The schema to use for casting (null for root schema)
     * @return array The quote data with properly cast values
     */
    private function cast_quote_data_types($data, $schema = null) {
        if (!is_array($data)) {
            return $data;
        }

        // Use root schema if none provided
        if ($schema === null) {
            $schema = $this->get_quote_data_schema();
        }

        $result = [];

        foreach ($data as $key => $value) {
            // Handle array items with wildcard schema (e.g., lineItems)
            if ($key !== '*' && isset($schema['*']) && is_numeric($key)) {
                $result[$key] = $this->cast_quote_data_types($value, $schema['*']);
                continue;
            }

            // Get the type definition for this field
            $type_def = $schema[$key] ?? null;

            // If no type definition found, keep as is
            if ($type_def === null) {
                $result[$key] = $value;
                continue;
            }

            // If type_def is an array, it's a nested schema
            if (is_array($type_def) && is_array($value)) {
                $result[$key] = $this->cast_quote_data_types($value, $type_def);
                continue;
            }

            // Cast based on the defined type
            switch ($type_def) {
                case 'integer':
                    $result[$key] = (int)$value;
                    break;

                case 'float':
                    $result[$key] = (float)$value;
                    break;

                case 'boolean':
                    $result[$key] = filter_var($value, FILTER_VALIDATE_BOOLEAN);
                    break;

                case 'string':
                    $result[$key] = (string)$value;
                    break;

                default:
                    $result[$key] = $value;
            }
        }

        return $result;
    }

    public function send_to_autodesk($quoteData) {
        // Cast the quote data to the correct types
        $quoteData = $this->cast_quote_data_types($quoteData);

        print_rr($quoteData,'final_quote_data', false, true);

        $this->quote_data = $quoteData;
        //if ($prompt) return ['response' => 'prompt', 'debug' => $this->api->debugLog];

        try {
            $response = $this->api->send_quote($this->quote_data);
            $quote = $response['body'];
            if ($quote['status'] == 'fail') return $this->api->debugLog;
            $status_response = $this->api->quotes_status($quote['transactionId']);
            $quote_status = $status_response['body'];
        } catch (Exception $e) {
            $this->api->debugLog['log']['error'] = $e->getMessage();
            return $this->api->debugLog;
        }
        if ($quote_status['status'] == 'fail') return $this->api->debugLog;

       // $database_response = $this->database_addquote($orders_id, null, null, $quote_status);
        //$this->api->debugLog['log']['database_query'] = $database_response['query_sql'];
      //  $this->api->debugLog['log']['database_response'] = $database_response['response'];
        return ['response' => $quote_status, 'debug' => $this->api->debugLog];
    }
    public static function get_from_api($quoteNumber) {
        $autodesk_api = new autodesk_api();
        return $autodesk_api->api->quote_get($quoteNumber);
    }


    public static function update_from_api($json_payload = null)    {
        $mapped_keys = [];
        $skipped_keys = [];

        // Log the start of the process
        tcs_log('Starting quote change processing at ' . date('Y-m-d H:i:s'), 'quote_update');

        try {
            // If no payload was passed, try to get it from the request
            if ($json_payload === null) {
                // Get the raw POST data
                $raw_post_data = file_get_contents('php://input');
                $json_payload = json_decode($raw_post_data, true);

                if (!$json_payload) {
                    throw new \Exception('Invalid JSON payload received');
                }
            }

            $mapping = self::$quote_column_mapping;

            // Log the mapping for debugging
            tcs_log('quote column mapping retrieved', 'quote_update');

            $query_sql = '';
            $query_array = [];

            // Log the incoming payload
            tcs_log('Incoming payload: ' . print_r($json_payload, true), 'quote_update');
            $json_data =  $json_payload['payload'];
            // Extract quote ID from the payload
            $quote_number = $json_data['quoteNumber'] ?? null;
            if (!$quote_number) {
                throw new \Exception('No quote ID found in payload');
            }

            //$autodesk = new autodesk_api;
            //quote = $autodesk->quote->get_from_api($quoteId, 'json');
            // $json_data = json_decode($quote['body']);
            // tcs_log("Got quote: " . print_r($quote , true), 'quote_update');

            //$output = autodesk_api::process_json_data($mapping, $json_data);
            //Process payload
            $db = tcs_db_query("select * from autodesk_quotes where quote_number = :quote_number limit 1", [":quote_number" => $quote_number]);
            if (!is_countable($db) || count($db) == 0) {
                $quote = self::get_from_api($quote_number, 'json');
                $json_data = $quote['body'];
                tcs_log("Got quote from api: " . print_r($quote , true), 'quote_update');
                return autodesk_api::process_json_data($mapping, $json_data);
                // Log successful completion
            }


            foreach ($json_data as $key => $value) {
                $found = false;
                foreach ($mapping as $map_key => $table){
                    if (!empty($table['columns'][$key])) {
                        $found = true;

                        $mapping_current = [$table['table'], $key];
                        $table_Name = $table['table'];

                        if (!empty($table_Name) && !empty($key)) {
                            $query_array['tables'][$table_Name]['fields'][$table['columns'][$key]] = var_export($value, true);
                            // Log successful mapping
                            tcs_log("found key: {$table['columns'][$key]} and mapped to {$table_Name}.{$table['columns'][$key]}", 'quote_update');
                            $mapped_keys[$table_Name][] = $key;
                        }else{
                            tcs_log("found key: {$table['columns'][$key]} but \$table_Name ({$table_Name}) and \$key ({$key}) are empty", 'quote_update');
                        }
                        break;
                    }
                }

                if (!$found){
                    // Log skipped keys
                    $skipped_keys[] = "Skipped unmapped key: $key";
                    tcs_log("Skipped unmapped key: $key", 'quote_update');
                }
            }
            $query_result = null;

            $response=[];
            foreach ($query_array['tables'] as $table => $fields) {
                $set_fields = [];
                foreach ($fields['fields'] as $key => $value) {
                    $set_fields[] = "{$key} = {$value}";
                }

                $set_string = implode(', ', $set_fields);

                $query_sql = "INSERT INTO {$table} SET {$set_string} " .
                    "ON DUPLICATE KEY UPDATE {$set_string};" . PHP_EOL;
                // Execute the SQL query
                tcs_log('$query_sql: ' . print_r($query_sql, true), 'quote_update');
                $query_result = tep_db_query($query_sql);
                $response[] = [
                    'status' => 'success',
                    'message' => "{$table} updated successfull",
                    'query_sql' => $query_sql,
                    'affected_rows' => tep_db_affected_rows($query_result)
                ];
                tcs_log("query_sql: " . $query_sql, 'quote_update');
                tcs_log("response: " . print_r($response, true), 'quote_update');
            }

            // Log successful completion
            return tcs_log(json_encode($response, JSON_PRETTY_PRINT), 'quote_update');

        } catch (\Exception $e) {
            // Log any exceptions that occur
            tcs_log('Error in quote change processing: ' . $e->getMessage(), 'quote_update');
            tcs_log(PHP_EOL . 'mapped: ' . print_r($mapped_keys, true) . PHP_EOL .
                'Skipped: ' . print_r($skipped_keys, true), 'quote_update');
            tcs_log('Trace: ' . $e->getTraceAsString(), 'quote_update');

            // Return an error response
            $response = [
                'status' => 'error',
                'message' => $e->getMessage()
            ];

            return json_encode($response);
        }
    }
}
