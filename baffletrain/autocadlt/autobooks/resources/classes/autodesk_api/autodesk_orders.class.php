<?php

namespace autodesk_api;
use http\Exception;
use product_attributes\product_attributes;

class autodesk_orders {
    private autodesk_api_interface $api;
    public $debugLog;
    public $customer = [];
    public $products = [];
    public $currency = "GBP";
    public $account = ['accountCsn' => ADWS_CSN];
    public $opportunityNumber;
    public $debug = false;
    public $quote_data;
    public $quote_status;
    public $lineItems = [];

    public function __construct($api) {
        $this->api = $api;
    }
    public function get_all($db_data,$criteria) {
        return $this->database_get_current_orders($db_data,$criteria );
    }
    public function get_distinct() {
        return $this->database_get_current_orders(get_distinct:true);
    }
    public function get_current() {
         return $this->database_get_current_orders();
    }
    private function database_get_current_orders($db_data = [], $criteria = [], $get_distinct = false) {
        if (!is_array($criteria)) {
            throw new InvalidArgumentException('Expected $criteria to be an array.');
        }
        if (count($db_data) == 0) $db_data = [
            'table_id' => 'orders',
            'db_table' => 'orders',
            'columns' => []
        ];
        $table_data_schema = [
            "orders" => [
                'query' => "FROM autodesk_orders orders"
            ],
            "customers" => [
                'query' => "LEFT JOIN customers on orders.customers_id = customers.customers_id"
            ],
            "cadserv" => [
                'query' => "LEFT JOIN orders cadserv on orders.orders_id = cadserv.orders_id"
            ]
        ];
        $default_criteria = [];
        $query = build_select_query($db_data,$table_data_schema,array_merge($default_criteria,$criteria),$get_distinct);
        print_rr($query,'finalq');
        return tcs_db_query($query['text']);
    }
    private function database_get_product_extra($orders_id, $products_id = null) {
        $query_sql = "SELECT * FROM orders_products_autodesk WHERE orders_id = :orders_id";
        $params = [":orders_id" => $orders_id];
        if ($products_id != null) {
            $query_sql .= " and products_id = :products_id";
            $params[':products_id'] = $products_id;
        }
        $result = tep_db_query($query_sql, null, $params);
        ////print_rr($result, 'database_get_product_extra query_sql');
        if ($products_id) return tep_db_fetch_array($result);
        return tep_db_fetch_all($result);
    }
}
?>