<?php
namespace autodesk_api;
use Guz<PERSON>Http\Client;
use GuzzleHttp\Exception\RequestException;
use ZipArchive;

class autodesk_authenticator {
private $client_id;
private $client_secret;
private $token_endpoint;
private $accessToken;
private $httpClient;
private $csn;
private $callback_url;
private $debug = false;
public $debugLog = [];
public autodesk_api_interface $api;

public function __construct(
$api = null,
$debug = false
) {
$this->client_id = ADWS_CLIENT_ID;
$this->client_secret = ADWS_CLIENT_SECRET;
$this->token_endpoint = ADWS_TOKEN_ENDPOINT;
$this->csn = ADWS_CSN;
$this->callback_url = ADWS_CALLBACK_URL;
$this->httpClient = new Client();
$this->debug = $debug;
$this->debugLog = [];
}

public function authenticate() {

/*
* Create Signature
*/

$time_stamp = strtotime("now");
$base_str = $this->callback_url . $this->client_id . $time_stamp;
$hmacsha256 = hash_hmac('sha256', $base_str, $this->client_secret, true);
$signature = base64_encode($hmacsha256);

/*
* Create Authorization
*/

$base_64_message = $this->client_id . ":" . $this->client_secret;

$base_64_encoded = base64_encode($base_64_message);


$headers = [
"Authorization" => "Basic " . $base_64_encoded,
"cache-control" => "no-cache",
"signature" => $signature,
"CSN" => $this->csn,
"timestamp" => $time_stamp
];

$query = [
"grant_type" => "client_credentials"
];


$this->debugLog = [
"Headers" => $headers,
"Form Params" => $query
];


if ($this->debug) return;

try {
$response = $this->httpClient->post($this->token_endpoint, [
'headers' => $headers,
'query' => $query
]);


$responseBody = json_decode($response->getBody(), true);


if (isset($responseBody['access_token'])) {
$this->accessToken = $responseBody['access_token'];
} else {
throw new \Exception('Authentication Failed: ' . $responseBody['error']);
}
} catch (\RequestException $e) {
throw new \Exception('Request Error: ' . $e->getMessage());
}
}

public function getAccessToken() {
if (!$this->accessToken) {
$this->authenticate();
}
return $this->accessToken;
}

public function getSecret() {
return $this->client_secret;
}

public function generateSignature($callbackUrl, $accessToken, $timestamp) {
$message = $callbackUrl . $accessToken . $timestamp;
return base64_encode(hash_hmac('sha256', $message, $this->getSecret(), true));
}

/**
* Static method to decrypt a file from autodesk api
*
* @param string $encryptedFilePath Path to the encrypted file
* @param string $decryptedFilePath Path where the decrypted file should be saved
* @param string $password Password for decryption
* @param string $targetLogfile Target log file for logging
* @return string Path to the decrypted file
*/
public static function decrypt_file($encryptedFilePath, $decryptedFilePath, $password, $targetLogfile = "decryption") {
// Temporary path for the decrypted .zip file
$tempZipFile = FS_TEMP . '/temp_decrypted_file.zip';

// Step 1: Decrypt the encrypted file to a temporary .zip file
if (!file_exists($encryptedFilePath)) {
// throw new \Exception("Encrypted file not found: " . $encryptedFilePath);
die(autodesk_api::log_message("Failed to rename extracted file."));
}

$command = sprintf(
'openssl enc -aes-256-cbc -md sha512 -d -in %s -out %s -k %s',
escapeshellarg($encryptedFilePath),
escapeshellarg($tempZipFile),
escapeshellarg($password)
);
autodesk_api::log_message("Decrypting file: " . $command);
exec($command, $output, $returnVar);

if ($returnVar !== 0) {
die(autodesk_api::log_message("Decryption failed: " . implode("\n", $output)));
}
// Step 2: Extract the .zip file and rename the extracted CSV file
$zip = new ZipArchive;
if ($zip->open($tempZipFile) === TRUE) {
// Assuming there is only one file in the ZIP archive
$zip->extractTo(FS_TEMP);
$extractedFileName = $zip->getNameIndex(0); // Get the first file's name
$zip->close();

// Rename the extracted file to $decryptedFilePath
$extractedFilePath = FS_TEMP . '/' . $extractedFileName;
if (!rename($extractedFilePath, $decryptedFilePath)) {
// throw new \Exception("Failed to rename extracted file.");
die(autodesk_api::log_message("Failed to rename extracted file."));
}

// Remove the temporary zip file
unlink($tempZipFile);
} else {
//throw new \Exception("Failed to open decrypted ZIP file.");
die(autodesk_api::log_message("Failed to open decrypted ZIP file."));
}
return $decryptedFilePath;
}

/**
* Instance method that calls the static decryptFile method
* This maintains backward compatibility with existing code
*
* @param string $encryptedFilePath Path to the encrypted file
* @param string $decryptedFilePath Path where the decrypted file should be saved
* @param string $password Password for decryption
* @param string $targetLogfile Target log file for logging
* @return string Path to the decrypted file
*/
function decryptFile($encryptedFilePath, $decryptedFilePath, $password, $targetLogfile = "decryption") {
return self::decrypt_file($encryptedFilePath, $decryptedFilePath, $password, $targetLogfile);
}
}