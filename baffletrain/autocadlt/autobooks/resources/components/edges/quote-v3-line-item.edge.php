@props([
    'index' => 0,
    'item' => [
        'action' => '',
        'quantity' => 1,
        'offeringId' => null,
        'offeringName' => null,
        'offeringCode' => null,
        'subscriptionId' => null,
        'referenceSubscriptionId' => null,
        'startDate' => null,
        'endDate' => null,
        'promotionCode' => null
    ],
    'schema' => []
])

<div class="bg-gray-50 p-4 rounded-lg border border-gray-200" id="line-item-{{ $index }}">
    <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-medium">Line Item {{ $index + 1 }}</h3>
        <button type="button"
                hx-delete="api/quote-v3/remove-line-item"
                hx-target="#line-item-{{ $index }}"
                hx-swap="outerHTML"
                hx-vals='{"index": {{ $index }}}'
                class="text-red-600 hover:text-red-800">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
        </button>
    </div>

    <div class="grid grid-cols-6 gap-x-6 gap-y-8 sm:grid-cols-6">
        <div >
            <div class="flex items-center">
                <label for="lineItems[{{ $index }}].offeringName" class="block text-sm font-medium leading-6 text-gray-900">Offering Name</label>
                <button type="button"
                        hx-get="api/quote-v3/toggle-description"
                        hx-target="#offeringName-{{ $index }}-desc"
                        hx-swap="innerHTML"
                        class="ml-2 text-gray-400 hover:text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                    </svg>
                </button>
            </div>
            <div id="offeringName-{{ $index }}-desc" class="hidden mt-1 text-sm text-gray-600 bg-gray-50 p-2 rounded">
                Autodesk Offering ID (e.g., OD-000163). Required for New, Switch (Product), True-up, Co-term, Mid-term Switch.
            </div>
            <div class="mt-2 flex rounded-md shadow-sm">
                <x-forms-input
                        type="text"
                        name="lineItems[{{ $index }}].offeringName"
                        id="lineItems[{{ $index }}].offeringName"
                        value="{{ $item['offeringName'] }}"
                        placeholder="OD-000000"
                        extra_attributes="pattern='^OD-[A-Za-z0-9]{6}$' required"
                />
                <button type="button"
                        hx-get="api/search_offerings"
                        hx-target="#offering-search-results-{{ $index }}"
                        hx-trigger="click"
                        class="relative -ml-px inline-flex items-center gap-x-1.5 rounded-r-md px-3 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50">
                    Search
                </button>
            </div>
            <div id="offering-search-results-{{ $index }}" class="mt-2"></div>
        </div>
        <div >
            <div class="flex items-center">
                <label for="lineItems[{{ $index }}].offeringId" class="block text-sm font-medium leading-6 text-gray-900">Offering ID</label>
                <button type="button"
                        hx-get="api/quote-v3/toggle-description"
                        hx-target="#offeringId-{{ $index }}-desc"
                        hx-swap="innerHTML"
                        class="ml-2 text-gray-400 hover:text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                    </svg>
                </button>
            </div>
            <div id="offeringId-{{ $index }}-desc" class="hidden mt-1 text-sm text-gray-600 bg-gray-50 p-2 rounded">
                Autodesk Offering ID (e.g., OD-000163). Required for New, Switch (Product), True-up, Co-term, Mid-term Switch.
            </div>
            <div class="mt-2 flex rounded-md shadow-sm">
                <x-forms-input-button
                    name="lineItems[{{ $index }}].offeringId"
                    id="lineItems[{{ $index }}].offeringId"
                    value="{{ $item['offeringId'] }}"
                    button_label="Search"
                    hx-get="api/quote-v3/search_offerings"
                    hx-target="#offering-search-results-{{ $index }}"
                    hx-trigger="click"
                />

            </div>
            <div id="offering-search-results-{{ $index }}" class="mt-2"></div>
        </div>

        <!-- Action -->
        <div>
            <div class="flex items-center">
                <label for="lineItems[{{ $index }}].action" class="block text-sm font-medium leading-6 text-gray-900">Action</label>
                <button type="button"
                        hx-get="api/quote-v3/toggle-description"
                        hx-target="#lineItemAction-{{ $index }}-desc"
                        hx-swap="innerHTML"
                        class="ml-2 text-gray-400 hover:text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                    </svg>
                </button>
            </div>
            <div id="lineItemAction-{{ $index }}-desc" class="hidden mt-1 text-sm text-gray-600 bg-gray-50 p-2 rounded">
                The type of action for this line item.
            </div>
            <x-forms-select
                name="lineItems[{{ $index }}].action"
                id="lineItems[{{ $index }}].action"
                extra_attributes="required"
                :options="[
                    '' => 'Select an action',
                    'New' => 'New',
                    'Renewal' => 'Renewal',
                    'Switch' => 'Switch',
                    'Mid-term Switch' => 'Mid-term Switch',
                    'Extension' => 'Extension',
                    'True-up' => 'True-up',
                    'Co-term' => 'Co-term'
                ]"
                :selected="$item['action']"
            />
        </div>

        <!-- Quantity -->
        <div>
            <div class="flex items-center">
                <label for="lineItems[{{ $index }}].quantity" class="block text-sm font-medium leading-6 text-gray-900">Quantity</label>
                <button type="button"
                        hx-get="api/quote-v3/toggle-description"
                        hx-target="#lineItemQuantity-{{ $index }}-desc"
                        hx-swap="innerHTML"
                        class="ml-2 text-gray-400 hover:text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                    </svg>
                </button>
            </div>
            <div id="lineItemQuantity-{{ $index }}-desc" class="hidden mt-1 text-sm text-gray-600 bg-gray-50 p-2 rounded">
                Number of units. Flex: >= 100. SUS: > 0. Renewal/Switch/Extension: > 0. True-up: > 0. Co-term Flex: >= 100, Co-term SUS: > 0.
            </div>
            <x-forms-input
                type="number"
                name="lineItems[{{ $index }}].quantity"
                id="lineItems[{{ $index }}].quantity"
                value="{{ $item['quantity'] }}"
                extra_attributes="min='1' required"
            />
        </div>

        <!-- Conditional fields based on action -->
        @if(in_array($item['action'], ['New', 'Switch', 'True-up', 'Co-term', 'Mid-term Switch']))
            <div >
                <div class="flex items-center">
                    <label for="lineItems[{{ $index }}].offeringId" class="block text-sm font-medium leading-6 text-gray-900">Offering ID</label>
                    <button type="button"
                            hx-get="api/quote-v3/toggle-description"
                            hx-target="#offeringId-{{ $index }}-desc"
                            hx-swap="innerHTML"
                            class="ml-2 text-gray-400 hover:text-gray-500">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                        </svg>
                    </button>
                </div>
                <div id="offeringId-{{ $index }}-desc" class="hidden mt-1 text-sm text-gray-600 bg-gray-50 p-2 rounded">
                    Autodesk Offering ID (e.g., OD-000163). Required for New, Switch (Product), True-up, Co-term, Mid-term Switch.
                </div>
                <div class="mt-2 flex rounded-md shadow-sm">
                    <x-forms-input
                        type="text"
                        name="lineItems[{{ $index }}].offeringId"
                        id="lineItems[{{ $index }}].offeringId"
                        value="{{ $item['offeringId'] }}"
                        placeholder="OD-000000"
                        extra_attributes="pattern='^OD-[A-Za-z0-9]{6}$' required"
                    />
                    <button type="button"
                            hx-get="api/quote-v3/search_offerings"
                            hx-target="#offering-search-results-{{ $index }}"
                            hx-trigger="click"
                            class="relative -ml-px inline-flex items-center gap-x-1.5 rounded-r-md px-3 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50">
                        Search
                    </button>
                </div>
                <div id="offering-search-results-{{ $index }}" class="mt-2"></div>
            </div>
        @endif

        <!-- Subscription ID for Renewal, Switch, Extension -->
        @if(in_array($item['action'], ['Renewal', 'Switch', 'Extension']))
            <div>
                <div class="flex items-center">
                    <label for="lineItems[{{ $index }}].subscriptionId" class="block text-sm font-medium leading-6 text-gray-900">Subscription ID</label>
                    <button type="button"
                            hx-get="api/quote-v3/toggle-description"
                            hx-target="#subscriptionId-{{ $index }}-desc"
                            hx-swap="innerHTML"
                            class="ml-2 text-gray-400 hover:text-gray-500">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                        </svg>
                    </button>
                </div>
                <div id="subscriptionId-{{ $index }}-desc" class="hidden mt-1 text-sm text-gray-600 bg-gray-50 p-2 rounded">
                    Subscription ID to be Renewed, Switched (Term), or Extended.
                </div>
                <x-forms-input
                    type="text"
                    name="lineItems[{{ $index }}].subscriptionId"
                    id="lineItems[{{ $index }}].subscriptionId"
                    value="{{ $item['subscriptionId'] }}"
                    extra_attributes="pattern='^[0-9]+$' required"
                />
            </div>
        @endif
    </div>
</div>
