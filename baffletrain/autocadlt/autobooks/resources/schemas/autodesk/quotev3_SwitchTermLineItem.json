// SwitchTermLineItem.json
[
  {
    "field": "lineItems[i].action",
    "description": "Action to perform. - Switch",
    "required": true,
    "type": "string"
  },
  {
    "field": "lineItems[i].agentLineReference",
    "description": "External line reference identifier, if applicable. Can be up to 37 characters and must start and end with a letter or digit. All characters must be a letter, digit, or hyphen.",
    "required": false,
    "type": "string"
  },
  {
    "field": "lineItems[i].quantity",
    "description": "Number of units. Must be greater than 0 (> 0).",
    "required": true,
    "type": "integer"
  },
  {
    "field": "lineItems[i].subscriptionId",
    "description": "Subscription ID of subscription to be term switched. The subscription must be in the renewal window. Please refer to Appendix C: Same Subscription on Multiple Line Items for more information if multiple line items on the quote reference the same subscription. The subscription will be renewed with the specified term.",
    "required": true,
    "type": "string"
  },
  {
    "field": "lineItems[i].promotionCode",
    "description": "Manually apply a promotion

  [
    {
      "field": "lineItems[i].action",
      "description": "Action to perform. - Switch",
      "required": true,
      "type": "string"
    },
    {
      "field": "lineItems[i].agentLineReference",
      "description": "External line reference identifier, if applicable. Can be up to 37 characters and must start and end with a letter or digit. All characters must be a letter, digit, or hyphen.",
      "required": false,
      "type": "string"
    },
    {
      "field": "lineItems[i].quantity",
      "description": "Number of units. Must be greater than 0 (> 0).",
      "required": true,
      "type": "integer"
    },
    {
      "field": "lineItems[i].subscriptionId",
      "description": "Subscription ID of subscription to be term switched. The subscription must be in the renewal window. Please refer to Appendix C: Same Subscription on Multiple Line Items for more information if multiple line items on the quote reference the same subscription. The subscription will be renewed with the specified term.",
      "required": true,
      "type": "string"
    },
    {
      "field": "lineItems[i].promotionCode",
      "description": "Manually apply a promotion code to the line item. This promotion code overrides any automatically applied promotion codes. If the promotion code end date is earlier than the quote expiration date, the promotion code end date overrides the quote expiration date. The quote will expire when the promotion code expires.",
      "required": false,
      "type": "string"
    },
    {
      "field": "lineItems[i].offer",
      "description": "Offer information.",
      "required": true,
      "type": "object",
      "properties": {
        "term": {
          "field": "lineItems[i].offer.term",
          "description": "Term.",
          "required": true,
          "type": "object",
          "properties": {
            "code": {
              "field": "lineItems[i].offer.term.code",
              "description": "Term code. - A01 - A06",
              "required": true,
              "type": "string"
            },
            "description": {
              "field": "lineItems[i].offer.term.description",
              "description": "Term description.",
              "required": true,
              "type": "string"
            }
          }
        }
      }
    }
  ]
