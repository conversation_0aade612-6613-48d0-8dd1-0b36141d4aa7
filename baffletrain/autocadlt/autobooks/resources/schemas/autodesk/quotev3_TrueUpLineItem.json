// TrueUpLineItem.json
[
  {
    "field": "lineItems[i].action",
    "description": "Action to perform. - True-up",
    "required": true,
    "type": "string"
  },
  {
    "field": "lineItems[i].agentLineReference",
    "description": "External line reference identifier, if applicable. Can be up to 37 characters and must start and end with a letter or digit. All characters must be a letter, digit, or hyphen.",
    "required": false,
    "type": "string"
  },
  {
    "field": "lineItems[i].quantity",
    "description": "Units to true-up. Must be greater than 0 (> 0).",
    "required": true,
    "type": "integer"
  },
  {
    "field": "lineItems[i].offeringId",
    "description": "Offering id for premium.",
    "required": true,
    "type": "string"
  },
  {
    "field": "lineItems[i].offeringName",
    "description": "Offering name for premium.",
    "required": true,
    "type": "string"
  },
  {
    "field": "lineItems[i].offeringCode",
    "description": "Offering code for premium.",
    "required": true,
    "type": "string"
  },
  {
    "field": "lineItems[i].startDate",
    "description": "Start date of the new premium subscription associated with the true-up.",
    "required": true,
    "type": "string"
  },
  {
    "field": "lineItems[i].endDate",
    "description": "End date of the new premium subscription associated with the true-up.",
    "required": true,
    "type": "string"
  },
  {
    "field": "lineItems[i].offer",
    "description": "Offer information.",
    "required": true,
    "type": "object",
    "properties": {
      "term": {
        "field": "lineItems[i].offer.term",
        "description": "The term of the offer (e.g., Annual, 3 Year).",
        "required": true,
        "type": "object",
        "properties": {
          "code": {
            "field": "lineItems[i].offer.term.code",
            "description": "Term code. - A01 - A06",
            "required": true,
            "type": "string"
          },
          "description": {
            "field": "lineItems[i].offer.term.description",
            "description": "Term description. - Annual - 3 year",
            "required": true,
            "type": "string"
          }
        }
      },
      "accessModel": {
        "field": "lineItems[i].offer.accessModel",
        "description": "The access model of the offer. How access is provided to the subscription (e.g., Single User, Flex).",
        "required": true,
        "type": "object",
        "properties": {
          "code": {
            "field": "lineItems[i].offer.accessModel.code",
            "description": "Access model code. - S",
            "required": true,
            "type": "string"
          },
          "description": {
            "field": "lineItems[i].offer.accessModel.description",
            "description": "Access model description. - Single User",
            "required": true,
            "type": "string"
          }
        }
      },
      "intendedUsage": {
        "field": "lineItems[i].offer.intendedUsage",
        "description": "The usage type of the offer. Defines if the offer is for intended commercial or other use (e.g., COM, NFR).",
        "required": true,
        "type": "object",
        "properties": {
          "code": {
            "field": "lineItems[i].offer.intendedUsage.code",
            "description": "Usage type code. - COM",
            "required": true,
            "type": "string"
          },
          "description": {
            "field": "lineItems[i].offer.intendedUsage.description",
            "description": "Usage type description. - Commercial",
            "required": true,
            "type": "string"
          }
        }
      },
      "connectivity": {
        "field": "lineItems[i].offer.connectivity",
        "description": "The connectivity of the offer. Whether the customer needs to be connected to the cloud to run the software (e.g., Online, Offline).",
        "required": true,
        "type": "object",
        "properties": {
          "code": {
            "field": "lineItems[i].offer.connectivity.code",
            "description": "Connectivity code. - C100",
            "required": true,
            "type": "string"
          },
          "description": {
            "field": "lineItems[i].offer.connectivity.description",
            "description": "Connectivity description. - Online",
            "required": true,
            "type": "string"
          }
        }
      },
      "servicePlan": {
        "field": "lineItems[i].offer.servicePlan",
        "description": "The service plan of the offer (e.g., Standard, Premium).",
        "required": true,
        "type": "object",
        "properties": {
          "code": {
            "field": "lineItems[i].offer.servicePlan.code",
            "description": "Service plan code. - PREMSUB - PREMNS",
            "required": true,
            "type": "string"
          },
          "description": {
            "field": "lineItems[i].offer.servicePlan.description",
            "description": "Service plan description. - Premium - Premium No Support",
            "required": true,
            "type": "string"
          }
        }
      }
    }
  }
]
