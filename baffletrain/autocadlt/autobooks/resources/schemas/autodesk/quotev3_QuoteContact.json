// QuoteContact.json
[
  {
    "field": "quoteContact.email",
    "description": "Quote contact’s email address. The email alone (for an existing quote contact) or all quote contact details (including email) are required. If the endCustomer account has “isIndividual” set to true, all quote contact details are required regardless of if the contact already exists.",
    "required": true,
    "type": "string"
  },
  {
    "field": "quoteContact.firstName",
    "description": "Quote contact’s first name.",
    "required": true,
    "type": "string"
  },
  {
    "field": "quoteContact.lastName",
    "description": "Quote contact’s last name.",
    "required": true,
    "type": "string"
  },
  {
    "field": "quoteContact.phone",
    "description": "Quote contact’s phone number.",
    "required": true,
    "type": "string"
  },
  {
    "field": "quoteContact.preferredLanguage",
    "description": "Quote contact’s preferred language. Please refer to Appendix B: Supported Language Codes.",
    "required": true,
    "type": "string"
  }
]