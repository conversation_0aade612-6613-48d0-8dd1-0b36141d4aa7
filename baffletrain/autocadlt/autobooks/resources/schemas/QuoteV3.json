{"formName": "Autodesk CreateQuote v3 Request", "description": "Generates a UI for creating a request payload for the Autodesk Partner WebServices CreateQuote v3 API (Version 1.0.24 based on provided document).", "apiVersion": "3.0", "documentationVersion": "1.0.24", "fields": [{"name": "currency", "label": "<PERSON><PERSON><PERSON><PERSON>", "type": "select", "required": true, "optionsSource": "ISO4217", "options": ["USD", "EUR", "GBP", "JPY", "AUD", "CAD"], "description": "Currency for the quote (ISO 4217 code). Refer to the policy guide for supported currencies. If quoting from an opportunity with value-based items, this must match the opportunity currency.", "conditionalLogic": {"checkOpportunityValueBased": true}}, {"name": "quoteLanguage", "label": "Quote Language", "type": "select", "required": false, "optionsSource": "AppendixB", "options": [{"value": "cs", "label": "Czech"}, {"value": "da", "label": "Danish"}, {"value": "de", "label": "German"}, {"value": "en", "label": "English"}, {"value": "es", "label": "Spanish"}, {"value": "fi", "label": "Finnish"}, {"value": "fr", "label": "French"}, {"value": "hu", "label": "Hungarian"}, {"value": "it", "label": "Italian"}, {"value": "ja", "label": "Japanese"}, {"value": "ko", "label": "Korean"}, {"value": "nl", "label": "Dutch"}, {"value": "no", "label": "Norwegian"}, {"value": "pl", "label": "Polish"}, {"value": "pt", "label": "Portuguese"}, {"value": "pt_BR", "label": "Portuguese Brazilian"}, {"value": "ru", "label": "Russian"}, {"value": "sv", "label": "Swedish"}, {"value": "zh_CN", "label": "Chinese Simplified"}, {"value": "zh_TW", "label": "Traditional Chinese"}], "description": "Language for the quote (ISO 639-1 code). See Appendix D for logic on how language is determined if not specified. Must be supported by the end customer's country.", "conditionalLogic": {"checkEndCustomerCountrySupport": true}}, {"name": "quoteNotes", "label": "Quote Notes (Visible to Customer)", "type": "textarea", "required": false, "maxLength": 254, "description": "Optional free text field. Notes specified here will be visible to the customer on the quote PDF. Cannot be blank (''). To omit, provide null or don't include the field.", "validationRules": {"notEmptyString": true}}, {"name": "opportunityNumber", "label": "Opportunity Number", "type": "text", "required": false, "pattern": "^A-[A-Za-z0-9]{8}$", "description": "Opportunity number to be renewed/referenced (Format: A-XXXXXXXX). Required for 'As-Is Renewals' (with no line items), True-Ups, and DDA quotes.", "conditionalLogic": {"requiredIf": "lineItems.length === 0 || lineItems.some(item => ['True-up', 'New (DDA Modification)', 'Renewal (DDA Modification)', 'Extension (DDA Modification)', 'Co-term (DDA Modification)'].includes(item.action))"}}, {"name": "agent<PERSON><PERSON>unt", "label": "Agent Account", "type": "object", "required": true, "description": "Details of the Agent creating the quote.", "fields": [{"name": "accountCsn", "label": "Agent Account CSN", "type": "text", "required": true, "pattern": "^[0-9]{10}$", "description": "10-digit CSN of the Agent account. Must exist in Autodesk systems and match the CSN used for API authentication (Header CSN)."}]}, {"name": "agentContact", "label": "Agent Contact", "type": "object", "required": true, "description": "Contact information for the Agent.", "fields": [{"name": "email", "label": "Agent <PERSON><PERSON>", "type": "email", "required": true, "description": "Agent's email address."}]}, {"name": "endCustomer", "label": "End Customer", "type": "object", "required": true, "description": "End Customer account information. Provide EITHER accountCsn OR full details for a new customer.", "fields": [{"name": "accountCsn", "label": "End Customer CSN (Existing)", "type": "text", "required": false, "pattern": "^[0-9]{10}$", "description": "10-digit CSN for an existing End Customer. Required if action is Renewal, Switch, Extension, or True-up, and must match the subscription's customer. If creating from an Opportunity (As-Is), must match opportunity's customer. Mutually exclusive with providing full address details below.", "conditionalLogic": {"requiredIf": "lineItems.some(item => ['Renewal', 'Switch', 'Extension', 'True-up'].includes(item.action))", "exclusiveWith": ["endCustomer.name", "endCustomer.addressLine1", "endCustomer.city", "endCustomer.countryCode"]}}, {"name": "isIndividual", "label": "Is Individual Customer?", "type": "boolean", "required": false, "defaultValue": false, "description": "Determines if the end customer is an individual (true) or a company (false). Required when creating a new customer.", "conditionalLogic": {"requiredIf": "endCustomer.accountCsn == null"}}, {"name": "name", "label": "Customer Name (Company)", "type": "text", "required": false, "maxLength": 100, "description": "Company name. Required if creating a new company customer (isIndividual=false). Max 100 chars.", "conditionalLogic": {"requiredIf": "endCustomer.accountCsn == null && endCustomer.isIndividual == false", "visibleIf": "endCustomer.isIndividual == false"}}, {"name": "addressLine1", "label": "Address Line 1", "type": "text", "required": false, "description": "Street address. Required when creating a new customer.", "conditionalLogic": {"requiredIf": "endCustomer.accountCsn == null"}}, {"name": "addressLine2", "label": "Address Line 2", "type": "text", "required": false, "description": "Suite or secondary address information."}, {"name": "city", "label": "City", "type": "text", "required": false, "description": "City. Required when creating a new customer.", "conditionalLogic": {"requiredIf": "endCustomer.accountCsn == null"}}, {"name": "stateProvinceCode", "label": "State/Province Code", "type": "text", "required": false, "maxLength": 2, "description": "Two-digit code for state or province. Required for certain countries (see Appendix A).", "conditionalLogic": {"requiredIf": "endCustomer.accountCsn == null && ['AU', 'BD', 'BR', 'CA', 'CN', 'IE', 'IN', 'IT', 'JP', 'MX', 'PH', 'RU', 'US', 'VE'].includes(endCustomer.countryCode)"}}, {"name": "postalCode", "label": "Postal Code", "type": "text", "required": false, "description": "Postal code. Required for certain countries (see Appendix A).", "conditionalLogic": {"checkCountryRequiresPostalCode": true}}, {"name": "countryCode", "label": "Country Code", "type": "select", "required": false, "optionsSource": "ISO3166-1-alpha-2", "description": "Standard two-letter country code (ISO 3166-1 alpha-2). Required when creating a new customer.", "conditionalLogic": {"requiredIf": "endCustomer.accountCsn == null"}}]}, {"name": "quoteContact", "label": "Quote Contact (End Customer)", "type": "object", "required": true, "description": "Contact person for the quote at the End Customer. Email alone may suffice if contact exists, otherwise all details needed (esp. if isIndividual=true).", "fields": [{"name": "email", "label": "Quote Contact Email", "type": "email", "required": true, "description": "Email address of the quote contact."}, {"name": "firstName", "label": "Quote Contact First Name", "type": "text", "required": false, "description": "First name. Required if contact doesn't exist or end customer is individual.", "conditionalLogic": {"requiredIf": "contactDoesNotExist || endCustomer.isIndividual == true"}}, {"name": "lastName", "label": "Quote Contact Last Name", "type": "text", "required": false, "description": "Last name. Required if contact doesn't exist or end customer is individual.", "conditionalLogic": {"requiredIf": "contactDoesNotExist || endCustomer.isIndividual == true"}}, {"name": "phone", "label": "Quote Contact Phone", "type": "tel", "required": false, "description": "Phone number. Required if contact doesn't exist or end customer is individual.", "conditionalLogic": {"requiredIf": "contactDoesNotExist || endCustomer.isIndividual == true"}}, {"name": "preferredLanguage", "label": "Quote Contact Language", "type": "select", "required": false, "optionsSource": "AppendixB", "options": [{"value": "cs", "label": "Czech"}, {"value": "da", "label": "Danish"}, {"value": "de", "label": "German"}, {"value": "en", "label": "English"}, {"value": "es", "label": "Spanish"}, {"value": "fi", "label": "Finnish"}, {"value": "fr", "label": "French"}, {"value": "hu", "label": "Hungarian"}, {"value": "it", "label": "Italian"}, {"value": "ja", "label": "Japanese"}, {"value": "ko", "label": "Korean"}, {"value": "nl", "label": "Dutch"}, {"value": "no", "label": "Norwegian"}, {"value": "pl", "label": "Polish"}, {"value": "pt", "label": "Portuguese"}, {"value": "pt_BR", "label": "Portuguese Brazilian"}, {"value": "ru", "label": "Russian"}, {"value": "sv", "label": "Swedish"}, {"value": "zh_CN", "label": "Chinese Simplified"}, {"value": "zh_TW", "label": "Traditional Chinese"}], "description": "Preferred language (ISO 639-1). Required if contact doesn't exist or end customer is individual. Used if quoteLanguage is not specified.", "conditionalLogic": {"requiredIf": "contactDoesNotExist || endCustomer.isIndividual == true"}}]}, {"name": "admin", "label": "Primary Admin (Optional)", "type": "object", "required": false, "description": "Primary admin for NEW subscriptions. If omitted, quoteContact becomes admin. Same rules as quoteContact regarding existing vs new.", "fields": [{"name": "email", "label": "<PERSON><PERSON>", "type": "email", "required": true, "description": "Email address of the primary admin."}, {"name": "firstName", "label": "Admin First Name", "type": "text", "required": false, "description": "First name. Required if admin contact doesn't exist or end customer is individual.", "conditionalLogic": {"requiredIf": "adminContactDoesNotExist || endCustomer.isIndividual == true"}}, {"name": "lastName", "label": "Admin Last Name", "type": "text", "required": false, "description": "Last name. Required if admin contact doesn't exist or end customer is individual.", "conditionalLogic": {"requiredIf": "adminContactDoesNotExist || endCustomer.isIndividual == true"}}, {"name": "phone", "label": "Admin Phone", "type": "tel", "required": false, "description": "Phone number. Required if admin contact doesn't exist or end customer is individual.", "conditionalLogic": {"requiredIf": "adminContactDoesNotExist || endCustomer.isIndividual == true"}}, {"name": "preferredLanguage", "label": "Admin Language", "type": "select", "required": false, "optionsSource": "AppendixB", "options": [{"value": "cs", "label": "Czech"}, {"value": "da", "label": "Danish"}, {"value": "de", "label": "German"}, {"value": "en", "label": "English"}, {"value": "es", "label": "Spanish"}, {"value": "fi", "label": "Finnish"}, {"value": "fr", "label": "French"}, {"value": "hu", "label": "Hungarian"}, {"value": "it", "label": "Italian"}, {"value": "ja", "label": "Japanese"}, {"value": "ko", "label": "Korean"}, {"value": "nl", "label": "Dutch"}, {"value": "no", "label": "Norwegian"}, {"value": "pl", "label": "Polish"}, {"value": "pt", "label": "Portuguese"}, {"value": "pt_BR", "label": "Portuguese Brazilian"}, {"value": "ru", "label": "Russian"}, {"value": "sv", "label": "Swedish"}, {"value": "zh_CN", "label": "Chinese Simplified"}, {"value": "zh_TW", "label": "Traditional Chinese"}], "description": "Admin's preferred language (ISO 639-1). Required if admin contact doesn't exist or end customer is individual.", "conditionalLogic": {"requiredIf": "adminContactDoesNotExist || endCustomer.isIndividual == true"}}]}, {"name": "additionalRecipients", "label": "Additional Recipients (CC)", "type": "array", "required": false, "maxItems": 4, "description": "List of additional email addresses to receive the quote (max 4). Cannot be the same as the quoteContact email.", "itemSchema": {"type": "object", "fields": [{"name": "email", "label": "Recipient Email", "type": "email", "required": true, "description": "Email address of the additional recipient.", "validationRules": {"notEqualToField": "quoteContact.email"}}]}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "Skip DDA Check", "type": "boolean", "required": false, "defaultValue": false, "description": "Set to true to bypass Deal Discount Approval validation if an end customer has an active DDA but it's not referenced in opportunityNumber. Defaults to false (validation performed)."}, {"name": "agentQuoteReference", "label": "Agent Quote Reference", "type": "text", "required": false, "maxLength": 63, "pattern": "^[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]$", "description": "External quote reference identifier (e.g., Partner's system ID). Max 63 chars, start/end with letter/digit, can contain hyphens."}, {"name": "skipM2SDiscountValidation", "label": "Skip M2S Discount Validation", "type": "boolean", "required": false, "defaultValue": false, "description": "Set to true if extending subscription(s) on the quote with M2S pricing past the M2S program end date, to skip the eligibility check. Will be false if not included."}, {"name": "lineItems", "label": "Line Items", "type": "array", "required": false, "minItems": 0, "maxItems": 250, "description": "List of items on the quote. Max 250 total line items (less if quoting from an opportunity: 250 - # eligible lines on opp). Required unless doing an As-Is Renewal or DDA quote via opportunityNumber.", "conditionalLogic": {"requiredIf": "opportunityNumber == null"}, "itemSchema": {"type": "object", "fields": [{"name": "action", "label": "Action", "type": "select", "required": true, "options": ["New", "Renewal", "Switch", "Mid-term Switch", "Extension", "True-up", "Co-term", "New (DDA Modification)", "Renewal (DDA Modification)", "Extension (DDA Modification)", "Co-term (DDA Modification)"], "description": "The type of action for this line item."}, {"name": "agentLineReference", "label": "Agent Line Reference", "type": "text", "required": false, "maxLength": 37, "pattern": "^[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]$", "description": "External line reference identifier (e.g., L-002). Max 37 chars, start/end with letter/digit, can contain hyphens. Not allowed for DDA modification line items during creation (only update)."}, {"name": "quantity", "label": "Quantity", "type": "integer", "required": true, "minValue": 0, "description": "Number of units. Flex: >= 100. SUS: > 0. Renewal/Switch/Extension: > 0. True-up: > 0. Co-term Flex: >= 100, Co-term SUS: > 0. Mid-term Switch: Must match total quantity of reference subs unless value-based.", "conditionalLogic": {"minValueRule": "CheckFlexSUS"}}, {"name": "offeringId", "label": "Offering ID", "type": "text", "required": false, "pattern": "^OD-[A-Za-z0-9]{6}$", "description": "Autodesk Offering ID (e.g., OD-000163). Required for New, Switch (Product), True-up, Co-term, Mid-term Switch.", "conditionalLogic": {"requiredIf": "['New', 'Switch', 'True-up', 'Co-term', 'Mid-term Switch'].includes(action)"}}, {"name": "offeringName", "label": "Offering Name", "type": "text", "required": false, "description": "Autodesk Offering Name (e.g., Flex). Required for New, Switch (Product), True-up, Co-term, Mid-term Switch.", "conditionalLogic": {"requiredIf": "['New', 'Switch', 'True-up', 'Co-term', 'Mid-term Switch'].includes(action)"}}, {"name": "offeringCode", "label": "Offering Code", "type": "text", "required": false, "description": "Autodesk Offering Code (e.g., FLEXACCESS). Required for New, Switch (Product), True-up, Co-term, Mid-term Switch.", "conditionalLogic": {"requiredIf": "['New', 'Switch', 'True-up', 'Co-term', 'Mid-term Switch'].includes(action)"}}, {"name": "subscriptionId", "label": "Subscription ID (Target)", "type": "text", "required": false, "pattern": "^[0-9]+$", "description": "Subscription ID to be Renewed, Switched (Term), or Extended.", "conditionalLogic": {"requiredIf": "['Renewal', 'Switch', 'Extension'].includes(action)"}}, {"name": "referenceSubscriptionId", "label": "Reference Subscription ID (Source)", "type": "text", "required": false, "pattern": "^[0-9]+$", "description": "Subscription ID to reference for Co-term or Switch (Product/Product+Term - single source). Mutually exclusive with referenceSubscriptions.", "conditionalLogic": {"requiredIf": "['Co-term', 'Switch'].includes(action) && referenceSubscriptions == null", "exclusiveWith": ["referenceSubscriptions"]}}, {"name": "referenceSubscriptions", "label": "Reference Subscriptions (Source - M:1)", "type": "array", "required": false, "description": "Array of subscriptions for M:1 Switch (Product/Product+Term) or Mid-term Switch. Mutually exclusive with referenceSubscriptionId. All must be ACS for M:1 Switch. Same sub cannot appear twice.", "conditionalLogic": {"requiredIf": "['Switch', 'Mid-term Switch'].includes(action) && referenceSubscriptionId == null", "exclusiveWith": ["referenceSubscriptionId"]}, "itemSchema": {"type": "object", "fields": [{"name": "id", "label": "Subscription ID", "type": "text", "required": true, "pattern": "^[0-9]+$", "description": "ID of the subscription to be switched."}, {"name": "quantity", "label": "Quantity", "type": "integer", "required": true, "minValue": 1, "description": "Quantity of the subscription being switched. Must match the subscription's actual quantity."}]}}, {"name": "startDate", "label": "Start Date", "type": "date", "required": false, "description": "Intended start date for New, True-up, Co-term, Mid-term Switch. Cannot be > 30 days in future. If omitted for New/Co-term, starts immediately upon order (if before expiration).", "conditionalLogic": {"requiredIf": "['True-up', 'Mid-term Switch'].includes(action)"}}, {"name": "endDate", "label": "End Date", "type": "date", "required": false, "description": "Intended end date for Extension or Co-term. For Extension, defines the new end date. For Co-term, must be > reference subscription's end date. For True-Up, end date of the new premium subscription.", "conditionalLogic": {"requiredIf": "action == 'Co-term' && isAlignedToRenewingSub == false"}}, {"name": "promotionCode", "label": "Promotion Code", "type": "text", "required": false, "description": "Manually apply a promotion code, overriding automatic ones. Quote expiration might be affected."}, {"name": "opportunityLineItemId", "label": "Opportunity Line Item ID (DDA Mod)", "type": "text", "required": false, "pattern": "^[a-zA-Z0-9]{18}$", "description": "Unique ID of the line item from the DDA Opportunity being modified. Required when changing a DDA line item (e.g., New to Co-term, Renewal to Extension, Future Start Date, Quantity Increase).", "conditionalLogic": {"requiredIf": "action.includes('DDA Modification')"}}, {"name": "offer", "label": "Offer Details", "type": "object", "required": false, "description": "Details about the specific product offer being quoted.", "conditionalLogic": {"requiredIf": "['New', 'Switch', 'True-up', 'Co-term', 'Mid-term Switch'].includes(action)"}, "fields": [{"name": "term", "label": "Term", "type": "object", "required": true, "fields": [{"name": "code", "label": "Term Code", "type": "text", "required": true, "description": "e.g., A01 (Annual), A06 (3 Year)"}, {"name": "description", "label": "Term Description", "type": "text", "required": true, "description": "e.g., Annual, 3 year"}]}, {"name": "accessModel", "label": "Access Model", "type": "object", "required": true, "fields": [{"name": "code", "label": "Access Model Code", "type": "text", "required": true, "description": "e.g., S (Single User), F (Flex)"}, {"name": "description", "label": "Access Model Description", "type": "text", "required": true, "description": "e.g., Single User, Flex"}]}, {"name": "intendedUsage", "label": "Intended Usage", "type": "object", "required": true, "fields": [{"name": "code", "label": "Usage Code", "type": "text", "required": true, "description": "e.g., COM (Commercial), NFR (Not for Resale)"}, {"name": "description", "label": "Usage Description", "type": "text", "required": true, "description": "e.g., Commercial, Not for Resale"}]}, {"name": "connectivity", "label": "Connectivity", "type": "object", "required": true, "fields": [{"name": "code", "label": "Connectivity Code", "type": "text", "required": true, "description": "e.g., C100 (Online)"}, {"name": "description", "label": "Connectivity Description", "type": "text", "required": true, "description": "e.g., Online, Offline"}]}, {"name": "servicePlan", "label": "Service Plan", "type": "object", "required": true, "fields": [{"name": "code", "label": "Service Plan Code", "type": "text", "required": true, "description": "e.g., STND, PREMSUB"}, {"name": "description", "label": "Service Plan Description", "type": "text", "required": true, "description": "e.g., Standard, Premium"}]}]}]}}]}