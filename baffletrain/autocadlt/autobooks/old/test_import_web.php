<?php
// Include necessary files
require_once(FS_SYSTEM . '/startup_sequence.php');
require_once('resources/classes/autodesk_api.class.php');

use autodesk_api\autodesk_api;

// Set up logging
define('LOG_FILE', 'import_test.log');
function log_message($message) {
    file_put_contents(LOG_FILE, date('Y-m-d H:i:s') . ' - ' . $message . "\n", FILE_APPEND);
}

log_message("Starting import test");

// Create an instance of autodesk_api
$api = new autodesk_api();

// Run the import
log_message("Running import");
$result = $api->products->get_catalog();

// Log the result
log_message("Import completed with result: " . print_r($result, true));

// Check if hash_string is populated
$query = tep_db_query("SELECT id, unique_hash, hash_string FROM products_autodesk_catalog LIMIT 10");
$rows = tep_db_fetch_all($query);
log_message("Database check results: " . print_r($rows, true));

// Output the result
echo "<h1>Import Test</h1>";
echo "<p>Import completed. Check the log file for details.</p>";
echo "<pre>";
print_r($result);
echo "</pre>";

echo "<h2>Database Check</h2>";
echo "<pre>";
print_r($rows);
echo "</pre>";
