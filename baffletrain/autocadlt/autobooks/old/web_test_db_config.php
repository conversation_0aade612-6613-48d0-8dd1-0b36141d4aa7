<?php
/**
 * Web Test for Database Configuration
 * Access this via: http://localhost/baffletrain/autocadlt/autobooks/web_test_db_config.php
 */

echo "<h1>Database Configuration Test</h1>";
echo "<h2>Environment Detection</h2>";

// Include the configuration
require_once 'system/db_config.php';

echo "<table border='1' cellpadding='5'>";
echo "<tr><th>Setting</th><th>Value</th></tr>";
echo "<tr><td>Is Local</td><td>" . ($is_local ? '<strong style="color:green">YES</strong>' : '<strong style="color:red">NO</strong>') . "</td></tr>";
echo "<tr><td>PHP SAPI</td><td>" . php_sapi_name() . "</td></tr>";
echo "<tr><td>SERVER_NAME</td><td>" . ($_SERVER['SERVER_NAME'] ?? 'Not Set') . "</td></tr>";
echo "<tr><td>SERVER_ADDR</td><td>" . ($_SERVER['SERVER_ADDR'] ?? 'Not Set') . "</td></tr>";
echo "<tr><td>REQUEST_URI</td><td>" . ($_SERVER['REQUEST_URI'] ?? 'Not Set') . "</td></tr>";
echo "</table>";

echo "<h2>Database Configuration</h2>";
echo "<table border='1' cellpadding='5'>";
echo "<tr><th>Setting</th><th>Value</th></tr>";
echo "<tr><td>Server</td><td>$db_server</td></tr>";
echo "<tr><td>Database</td><td>$db_database</td></tr>";
echo "<tr><td>Username</td><td>$db_username</td></tr>";
echo "<tr><td>Password</td><td>" . (empty($db_password) ? '<em>(empty)</em>' : '<em>***</em>') . "</td></tr>";
echo "</table>";

echo "<h2>Connection Test</h2>";

try {
    $dsn = "mysql:host=$db_server;dbname=$db_database;charset=utf8";
    $pdo = new PDO($dsn, $db_username, $db_password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    $version = $pdo->query('SELECT VERSION()')->fetchColumn();
    echo "<p style='color:green'>✓ Connection successful</p>";
    echo "<p>Database version: $version</p>";
    
    // Test if we can query tables
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "<p>Found " . count($tables) . " tables</p>";
    
    if (count($tables) > 0) {
        echo "<h3>Available Tables:</h3>";
        echo "<ul>";
        foreach ($tables as $table) {
            echo "<li>$table</li>";
        }
        echo "</ul>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color:red'>❌ Connection failed: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>Next Steps</h2>";
echo "<ul>";
echo "<li><a href='http://localhost/phpmyadmin/' target='_blank'>Open phpMyAdmin</a></li>";
echo "<li><a href='http://localhost/' target='_blank'>XAMPP Dashboard</a></li>";
echo "<li>Run database sync: <code>php_helper.bat db db_sync.php pull</code></li>";
echo "</ul>";

echo "<hr>";
echo "<p><small>Generated on " . date('Y-m-d H:i:s') . "</small></p>";
?>
