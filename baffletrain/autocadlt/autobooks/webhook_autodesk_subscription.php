<?php
/**
 * Autodesk Subscription Webhook Handler
 * 
 * This file handles incoming webhook requests from Autodesk's API
 * for subscription updates. It bypasses the normal authentication flow
 * but implements its own security measures.
 */

// Set up the application environment
$path['fs_app_root'] = __DIR__ . '/';
require_once $path['fs_app_root'] . 'system/startup_sequence_minimal.php';

use autodesk_api\autodesk_subscription;
use autodesk_api\autodesk_api;

// Set up logging
tcs_log('Webhook request received at ' . date('Y-m-d H:i:s'), 'webhook');

// Load webhook configuration
$webhook_config = include($path['fs_app_root'] . 'system/webhook_config.php');

// Verify API key
$api_key = $_SERVER['HTTP_X_API_KEY'] ?? '';
if (empty($api_key) || $api_key !== $webhook_config['api_keys']['autodesk_subscription']) {
    tcs_log('Invalid or missing API key', 'webhook');
    http_response_code(401);
    echo json_encode(['status' => 'error', 'message' => 'Unauthorized']);
    exit;
}

// Optional: IP restriction
if (!empty($webhook_config['endpoints']['autodesk_subscription_webhook']['allowed_ips'])) {
    $client_ip = $_SERVER['REMOTE_ADDR'];
    if (!in_array($client_ip, $webhook_config['endpoints']['autodesk_subscription_webhook']['allowed_ips'])) {
        tcs_log('Request from unauthorized IP: ' . $client_ip, 'webhook');
        http_response_code(403);
        echo json_encode(['status' => 'error', 'message' => 'Forbidden']);
        exit;
    }
}

// Get the raw POST data
$raw_post_data = file_get_contents('php://input');
tcs_log('Received webhook data: ' . $raw_post_data, 'webhook');

// Process the webhook
try {
    // Decode the JSON payload
    $json_payload = json_decode($raw_post_data, true);
    
    if (!$json_payload) {
        throw new Exception('Invalid JSON payload');
    }
    
    // Call the handler method
    $result = autodesk_subscription::update_from_api($json_payload);
    
    // Return success response
    header('Content-Type: application/json');
    echo $result;
    
} catch (Exception $e) {
    // Log the error
    tcs_log('Error processing webhook: ' . $e->getMessage(), 'webhook');
    
    // Return error response
    http_response_code(500);
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'error',
        'message' => 'Error processing webhook: ' . $e->getMessage()
    ]);
}
