[subscription_update] [2025-06-06 11:39:27] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-06 11:39:27
[subscription_update] [2025-06-06 11:39:27] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-06 11:39:27] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 96d6db7f-4ad2-457e-b7c9-1e246dc683f1
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 74920602263259
            [quantity] => 6
            [message] => subscription quantity changed.
            [modifiedAt] => 2025-06-06T11:24:19.000+0000
        )

    [publishedAt] => 2025-06-06T11:39:24.000Z
    [csn] => **********
)

[subscription_update] [2025-06-06 11:39:27] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-06-06 11:39:27] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-06 11:39:27
[subscription_update] [2025-06-06 11:39:27] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-06 11:39:27] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 757151fd-911f-470e-ad22-ccc8c87dff16
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 74920602259458
            [quantity] => 3
            [message] => subscription quantity changed.
            [modifiedAt] => 2025-06-06T11:24:19.000+0000
        )

    [publishedAt] => 2025-06-06T11:39:25.000Z
    [csn] => **********
)

[subscription_update] [2025-06-06 11:39:27] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-06-06 11:39:30] [autodesk_subscription.class.php:445] Got subscription from api: Array
(
    [0] => Array
        (
            [subscriptionId] => 74920602259458
            [subscriptionReferenceNumber] => 
            [switch] => Array
                (
                    [fromSubscriptions] => Array
                        (
                        )

                    [toSubscription] => 
                )

            [quantity] => 3
            [status] => Inactive
            [startDate] => 2025-06-09
            [endDate] => 2025-07-05
            [term] => Annual
            [billingBehavior] => Recurring
            [billingFrequency] => Annual
            [intendedUsage] => COM
            [connectivity] => Online
            [connectivityInterval] => 30 Days
            [opportunityNumber] => **********
            [servicePlan] => Standard
            [accessModel] => Single User
            [paymentMethod] => LOC
            [recordType] => Attribute based
            [renewalCounter] => 
            [autoRenew] => ON
            [offeringId] => OD-000031
            [offeringCode] => ACDLT
            [offeringName] => AutoCAD LT
            [marketingName] => AutoCAD LT
            [currency] => GBP
            [annualDeclaredValue] => 
            [pricingMethod] => Array
                (
                    [code] => QTY
                    [description] => Quantity based
                )

            [accounts] => Array
                (
                    [soldTo] => Array
                        (
                            [csn] => **********
                            [name] => ETCH ASSOCIATES
                        )

                    [solutionProvider] => Array
                        (
                            [csn] => **********
                            [name] => TCS CAD & BIM Solutions Limited
                            [localLanguageName] => 
                            [type] => Reseller
                            [address1] => Unit F, Yorkway
                            [address2] => Mandale Ind Est
                            [address3] => 
                            [city] => Stockton On Tees
                            [stateProvince] => 
                            [postalCode] => TS17 6BX
                            [country] => United Kingdom
                            [stateProvinceCode] => 
                            [countryCode] => GB
                        )

                    [nurtureReseller] => Array
                        (
                            [csn] => 
                            [name] => 
                            [lockDate] => 
                            [nurtureDiscountEligibility] => 
                        )

                )

            [endCustomer] => Array
                (
                    [account] => Array
                        (
                            [csn] => **********
                            [name] => ETCH ASSOCIATES
                            [type] => End Customer
                            [address1] => 1 Union Way
                            [address2] => 
                            [address3] => 
                            [city] => Witney
                            [stateProvince] => OXFORDSHIRE
                            [postalCode] => OX28 6HD
                            [country] => United Kingdom
                            [individualFlag] => 
                            [namedAccountFlag] => 1
                            [namedAccountGroup] => Territory
                            [parentIndustryGroup] => AEC
                            [parentIndustrySegment] => Engineering Service Providers
                            [primaryAdminFirstName] => Etch
                            [primaryAdminLastName] => Associates
                            [primaryAdminEmail] => <EMAIL>
                            [teamId] => ********
                            [teamName] => David Harris - 6927
                            [stateProvinceCode] => 
                            [countryCode] => GB
                        )

                    [purchaser] => Array
                        (
                            [first] => Etch
                            [last] => Associates
                            [email] => <EMAIL>
                            [status] => Active
                            [portalRegistration] => Active
                            [doNotCall] => 
                            [doNotEmail] => 
                            [doNotMail] => 1
                        )

                )

        )

)

[subscription_update] [2025-06-06 11:39:30] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, New record inserted, <br>
[subscription_update] [2025-06-06 11:39:30] [autodesk_subscription.class.php:445] Got subscription from api: Array
(
    [0] => Array
        (
            [subscriptionId] => 74920602263259
            [subscriptionReferenceNumber] => 
            [switch] => Array
                (
                    [fromSubscriptions] => Array
                        (
                        )

                    [toSubscription] => 
                )

            [quantity] => 6
            [status] => Inactive
            [startDate] => 2025-06-09
            [endDate] => 2026-04-30
            [term] => Annual
            [billingBehavior] => Recurring
            [billingFrequency] => Annual
            [intendedUsage] => COM
            [connectivity] => Online
            [connectivityInterval] => 30 Days
            [opportunityNumber] => A-30567902
            [servicePlan] => Standard
            [accessModel] => Single User
            [paymentMethod] => LOC
            [recordType] => Attribute based
            [renewalCounter] => 
            [autoRenew] => ON
            [offeringId] => OD-000105
            [offeringCode] => BM36DP
            [offeringName] => Docs
            [marketingName] => Docs
            [currency] => GBP
            [annualDeclaredValue] => 
            [pricingMethod] => Array
                (
                    [code] => QTY
                    [description] => Quantity based
                )

            [accounts] => Array
                (
                    [soldTo] => Array
                        (
                            [csn] => **********
                            [name] => ETCH ASSOCIATES
                        )

                    [solutionProvider] => Array
                        (
                            [csn] => **********
                            [name] => TCS CAD & BIM Solutions Limited
                            [localLanguageName] => 
                            [type] => Reseller
                            [address1] => Unit F, Yorkway
                            [address2] => Mandale Ind Est
                            [address3] => 
                            [city] => Stockton On Tees
                            [stateProvince] => 
                            [postalCode] => TS17 6BX
                            [country] => United Kingdom
                            [stateProvinceCode] => 
                            [countryCode] => GB
                        )

                    [nurtureReseller] => Array
                        (
                            [csn] => 
                            [name] => 
                            [lockDate] => 
                            [nurtureDiscountEligibility] => 
                        )

                )

            [endCustomer] => Array
                (
                    [account] => Array
                        (
                            [csn] => **********
                            [name] => ETCH ASSOCIATES
                            [type] => End Customer
                            [address1] => 1 Union Way
                            [address2] => 
                            [address3] => 
                            [city] => Witney
                            [stateProvince] => OXFORDSHIRE
                            [postalCode] => OX28 6HD
                            [country] => United Kingdom
                            [individualFlag] => 
                            [namedAccountFlag] => 1
                            [namedAccountGroup] => Territory
                            [parentIndustryGroup] => AEC
                            [parentIndustrySegment] => Engineering Service Providers
                            [primaryAdminFirstName] => Etch
                            [primaryAdminLastName] => Associates
                            [primaryAdminEmail] => <EMAIL>
                            [teamId] => ********
                            [teamName] => Albie Welch - 7165
                            [stateProvinceCode] => 
                            [countryCode] => GB
                        )

                    [purchaser] => Array
                        (
                            [first] => Etch
                            [last] => Associates
                            [email] => <EMAIL>
                            [status] => Active
                            [portalRegistration] => Active
                            [doNotCall] => 
                            [doNotEmail] => 
                            [doNotMail] => 1
                        )

                )

        )

)

[subscription_update] [2025-06-06 11:39:30] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, New record inserted, <br>
[subscription_update] [2025-06-06 14:56:04] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-06 14:56:04
[subscription_update] [2025-06-06 14:56:04] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-06 14:56:04] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 0bc2108b-8371-48b4-805f-890c1cce6a71
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 71706126221671
            [status] => Active
            [quantity] => 1
            [endDate] => 2026-05-29
            [autoRenew] => ON
            [term] => Annual
            [message] => subscription status,quantity,endDate,autoRenew,term changed.
            [modifiedAt] => 2025-06-06T13:57:14.000+0000
        )

    [publishedAt] => 2025-06-06T14:56:01.000Z
    [csn] => **********
)

[subscription_update] [2025-06-06 14:56:04] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-06 14:56:04] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-06 14:56:04] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-06 14:56:04] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-06 14:56:04] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-06 14:56:04] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-06 14:56:04] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-06 14:56:04] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-06 14:56:04] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '71706126221671', status = 'Active', quantity = 1, endDate = '2026-05-29', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '71706126221671', status = 'Active', quantity = 1, endDate = '2026-05-29', autoRenew = 'ON', term = 'Annual';\n",
        "affected_rows": 2
    }
]
[subscription_update] [2025-06-06 15:38:59] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-06 15:38:59
[subscription_update] [2025-06-06 15:38:59] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-06 15:38:59] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 7c2f868a-3192-4a2a-8c05-fcc4af9e8b9a
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 71706126221671
            [quantity] => 1
            [message] => subscription quantity changed.
            [modifiedAt] => 2025-06-06T15:08:43.000+0000
        )

    [publishedAt] => 2025-06-06T15:38:57.000Z
    [csn] => **********
)

[subscription_update] [2025-06-06 15:38:59] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-06 15:38:59] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-06 15:38:59] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-06 15:38:59] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-06 15:38:59] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '71706126221671', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '71706126221671', quantity = 1;\n",
        "affected_rows": 0
    }
]
[subscription_update] [2025-06-07 06:09:31] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-07 06:09:31
[subscription_update] [2025-06-07 06:09:31] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-07 06:09:31] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => e8c3deed-b455-40e9-9f3d-d98f65c29887
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 55303232878149
            [status] => Expired
            [quantity] => 1
            [message] => subscription status,quantity changed.
            [modifiedAt] => 2025-06-07T05:39:27.000+0000
        )

    [publishedAt] => 2025-06-07T06:09:29.000Z
    [csn] => **********
)

[subscription_update] [2025-06-07 06:09:31] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-07 06:09:31] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-07 06:09:31] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-07 06:09:31] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-07 06:09:31] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-07 06:09:31] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '55303232878149', status = 'Expired', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '55303232878149', status = 'Expired', quantity = 1;\n",
        "affected_rows": 2
    }
]
[subscription_update] [2025-06-07 09:37:12] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-07 09:37:12
[subscription_update] [2025-06-07 09:37:12] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-07 09:37:12] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 5929d4ef-641f-4c47-ad9a-e66ed52e129c
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 62452879030377
            [autoRenew] => OFF
            [message] => subscription autoRenew changed.
            [modifiedAt] => 2025-06-07T09:22:06.000+0000
        )

    [publishedAt] => 2025-06-07T09:37:10.000Z
    [csn] => **********
)

[subscription_update] [2025-06-07 09:37:12] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-07 09:37:12] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-07 09:37:12] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-07 09:37:12] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-07 09:37:12] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '62452879030377', autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '62452879030377', autoRenew = 'OFF';\n",
        "affected_rows": 2
    }
]
[subscription_update] [2025-06-07 09:40:41] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-07 09:40:41
[subscription_update] [2025-06-07 09:40:41] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-07 09:40:41] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 6cf0fce3-18ae-43c7-9f65-5ffb72a495cc
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 62452879030377
            [status] => Active
            [quantity] => 1
            [endDate] => 2026-06-23
            [term] => Annual
            [message] => subscription status,quantity,endDate,term changed.
            [modifiedAt] => 2025-06-07T09:20:37.000+0000
        )

    [publishedAt] => 2025-06-07T09:40:39.000Z
    [csn] => **********
)

[subscription_update] [2025-06-07 09:40:41] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-07 09:40:41] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-07 09:40:41] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-07 09:40:41] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-07 09:40:41] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-07 09:40:41] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-07 09:40:41] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-07 09:40:41] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '62452879030377', status = 'Active', quantity = 1, endDate = '2026-06-23', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '62452879030377', status = 'Active', quantity = 1, endDate = '2026-06-23', term = 'Annual';\n",
        "affected_rows": 2
    }
]
[subscription_update] [2025-06-07 11:40:05] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-07 11:40:05
[subscription_update] [2025-06-07 11:40:05] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-07 11:40:05] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 01575b38-64e2-44e3-b8e7-f6bb88a333d8
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 62452879030377
            [quantity] => 1
            [message] => subscription quantity changed.
            [modifiedAt] => 2025-06-07T11:14:55.000+0000
        )

    [publishedAt] => 2025-06-07T11:40:02.000Z
    [csn] => **********
)

[subscription_update] [2025-06-07 11:40:05] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-07 11:40:05] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-07 11:40:05] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-07 11:40:05] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-07 11:40:05] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '62452879030377', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '62452879030377', quantity = 1;\n",
        "affected_rows": 0
    }
]
[subscription_update] [2025-06-08 00:11:09] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-08 00:11:09
[subscription_update] [2025-06-08 00:11:09] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-08 00:11:09] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 2a35017d-f2c4-48bc-8040-1bb1d30a6893
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 74781323386745
            [status] => Suspended
            [message] => subscription status changed.
            [modifiedAt] => 2025-06-07T23:41:05.000+0000
        )

    [publishedAt] => 2025-06-08T00:11:06.000Z
    [csn] => **********
)

[subscription_update] [2025-06-08 00:11:09] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-08 00:11:09] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-08 00:11:09] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-08 00:11:09] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-08 00:11:09] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '74781323386745', status = 'Suspended' ON DUPLICATE KEY UPDATE subscriptionId = '74781323386745', status = 'Suspended';\n",
        "affected_rows": 2
    }
]
[subscription_update] [2025-06-08 06:11:58] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-08 06:11:58
[subscription_update] [2025-06-08 06:11:58] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-08 06:11:58] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => ad7eb003-1414-460c-b3be-fdd93e726a33
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 71593456656960
            [status] => Expired
            [quantity] => 1
            [message] => subscription status,quantity changed.
            [modifiedAt] => 2025-06-08T05:36:52.000+0000
        )

    [publishedAt] => 2025-06-08T06:11:56.000Z
    [csn] => **********
)

[subscription_update] [2025-06-08 06:11:58] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-08 06:11:58] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-08 06:11:58] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-08 06:11:58] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-08 06:11:58] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-08 06:11:58] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '71593456656960', status = 'Expired', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '71593456656960', status = 'Expired', quantity = 1;\n",
        "affected_rows": 2
    }
]
[subscription_update] [2025-06-08 20:40:37] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-08 20:40:37
[subscription_update] [2025-06-08 20:40:37] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-08 20:40:37] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 966bd251-a7e4-4abb-85e7-cda150ecaf6d
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 74920602263259
            [status] => Active
            [quantity] => 6
            [message] => subscription status,quantity changed.
            [modifiedAt] => 2025-06-08T20:15:31.000+0000
        )

    [publishedAt] => 2025-06-08T20:40:34.000Z
    [csn] => **********
)

[subscription_update] [2025-06-08 20:40:37] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-08 20:40:37] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-08 20:40:37] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-08 20:40:37] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-08 20:40:37] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-08 20:40:37] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '74920602263259', status = 'Active', quantity = 6 ON DUPLICATE KEY UPDATE subscriptionId = '74920602263259', status = 'Active', quantity = 6;\n",
        "affected_rows": 2
    }
]
[subscription_update] [2025-06-08 20:40:37] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-08 20:40:37
[subscription_update] [2025-06-08 20:40:37] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-08 20:40:37] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => bae43c54-f8c0-4f27-88cb-0cf0906c8cad
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 74920602259458
            [status] => Active
            [quantity] => 3
            [message] => subscription status,quantity changed.
            [modifiedAt] => 2025-06-08T20:15:32.000+0000
        )

    [publishedAt] => 2025-06-08T20:40:35.000Z
    [csn] => **********
)

[subscription_update] [2025-06-08 20:40:37] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-08 20:40:37] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-08 20:40:37] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-08 20:40:37] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-08 20:40:37] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-08 20:40:37] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '74920602259458', status = 'Active', quantity = 3 ON DUPLICATE KEY UPDATE subscriptionId = '74920602259458', status = 'Active', quantity = 3;\n",
        "affected_rows": 2
    }
]
[subscription_update] [2025-06-09 06:10:01] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-09 06:10:01
[subscription_update] [2025-06-09 06:10:01] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-09 06:10:01] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 741117e8-0e48-4b3b-ba72-264a068f21b4
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 74289458973177
            [status] => Canceled
            [message] => subscription status changed.
            [modifiedAt] => 2025-06-09T05:39:56.000+0000
        )

    [publishedAt] => 2025-06-09T06:09:58.000Z
    [csn] => **********
)

[subscription_update] [2025-06-09 06:10:01] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-09 06:10:01] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-09 06:10:01] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-09 06:10:01] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-09 06:10:01] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '74289458973177', status = 'Canceled' ON DUPLICATE KEY UPDATE subscriptionId = '74289458973177', status = 'Canceled';\n",
        "affected_rows": 2
    }
]
[subscription_update] [2025-06-09 11:37:21] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-09 11:37:21
[subscription_update] [2025-06-09 11:37:21] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-09 11:37:21] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => b8ea888b-d0f5-4c36-abae-68a99edb1060
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 55441228350329
            [status] => Active
            [quantity] => 1
            [endDate] => 2026-06-20
            [autoRenew] => ON
            [term] => Annual
            [message] => subscription status,quantity,endDate,autoRenew,term changed.
            [modifiedAt] => 2025-06-09T11:12:16.000+0000
        )

    [publishedAt] => 2025-06-09T11:37:19.000Z
    [csn] => **********
)

[subscription_update] [2025-06-09 11:37:21] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-06-09 11:37:25] [autodesk_subscription.class.php:445] Got subscription from api: Array
(
    [0] => Array
        (
            [subscriptionId] => 55441228350329
            [subscriptionReferenceNumber] => 562-91311698
            [switch] => Array
                (
                    [fromSubscriptions] => Array
                        (
                        )

                    [toSubscription] => 
                )

            [quantity] => 1
            [status] => Active
            [startDate] => 2019-04-06
            [endDate] => 2026-06-20
            [term] => Annual
            [billingBehavior] => Recurring
            [billingFrequency] => Annual
            [intendedUsage] => COM
            [connectivity] => Online
            [connectivityInterval] => 30 Days
            [opportunityNumber] => 
            [servicePlan] => Standard
            [accessModel] => Single User
            [paymentMethod] => Credit Card
            [recordType] => Attribute based
            [renewalCounter] => 
            [autoRenew] => ON
            [offeringId] => OD-000031
            [offeringCode] => ACDLT
            [offeringName] => AutoCAD LT
            [marketingName] => AutoCAD LT
            [currency] => 
            [annualDeclaredValue] => 
            [pricingMethod] => Array
                (
                    [code] => QTY
                    [description] => Quantity based
                )

            [accounts] => Array
                (
                    [soldTo] => Array
                        (
                            [csn] => **********
                            [name] => ARCHITECTURAL METAL CRAFTSMEN LT
                        )

                    [solutionProvider] => Array
                        (
                            [csn] => **********
                            [name] => TCS CAD & BIM Solutions Limited
                            [localLanguageName] => 
                            [type] => Reseller
                            [address1] => Unit F, Yorkway
                            [address2] => Mandale Ind Est
                            [address3] => 
                            [city] => Stockton On Tees
                            [stateProvince] => 
                            [postalCode] => TS17 6BX
                            [country] => United Kingdom
                            [stateProvinceCode] => 
                            [countryCode] => GB
                        )

                    [nurtureReseller] => Array
                        (
                            [csn] => 
                            [name] => 
                            [lockDate] => 
                            [nurtureDiscountEligibility] => 
                        )

                )

            [endCustomer] => Array
                (
                    [account] => Array
                        (
                            [csn] => **********
                            [name] => ARCHITECTURAL METAL CRAFTSMEN LT
                            [type] => End Customer
                            [address1] => Finedon Road Ind Est 11 Bradfield
                            [address2] => Close Finedon Road Industrial
                            [address3] => Estate
                            [city] => Wellingborough
                            [stateProvince] => NORTHAMPTONSHIRE
                            [postalCode] => NN8 4RQ
                            [country] => United Kingdom
                            [individualFlag] => 
                            [namedAccountFlag] => 1
                            [namedAccountGroup] => Territory
                            [parentIndustryGroup] => MFG
                            [parentIndustrySegment] => Building Products & Fabricat'n
                            [primaryAdminFirstName] => Scott
                            [primaryAdminLastName] => Odell
                            [primaryAdminEmail] => <EMAIL>
                            [teamId] => 6974473
                            [teamName] => Scott Odell - 4473
                            [stateProvinceCode] => 
                            [countryCode] => GB
                        )

                    [purchaser] => Array
                        (
                            [first] => Scott
                            [last] => Odell
                            [email] => <EMAIL>
                            [status] => Active
                            [portalRegistration] => Registered
                            [doNotCall] => 
                            [doNotEmail] => 
                            [doNotMail] => 
                        )

                )

        )

)

[subscription_update] [2025-06-09 11:37:25] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, Record updated, <br>
[subscription_update] [2025-06-09 11:37:38] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-09 11:37:38
[subscription_update] [2025-06-09 11:37:38] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-09 11:37:38] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 275621d6-41c3-418b-82ce-2100a127b17a
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 74945556048388
            [quantity] => 1
            [message] => subscription quantity changed.
            [modifiedAt] => 2025-06-09T11:22:28.000+0000
        )

    [publishedAt] => 2025-06-09T11:37:34.000Z
    [csn] => **********
)

[subscription_update] [2025-06-09 11:37:38] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-06-09 11:37:42] [autodesk_subscription.class.php:445] Got subscription from api: Array
(
    [0] => Array
        (
            [subscriptionId] => 74945556048388
            [subscriptionReferenceNumber] => 
            [switch] => Array
                (
                    [fromSubscriptions] => Array
                        (
                        )

                    [toSubscription] => 
                )

            [quantity] => 1
            [status] => Active
            [startDate] => 2025-06-09
            [endDate] => 2026-06-08
            [term] => Annual
            [billingBehavior] => Recurring
            [billingFrequency] => Annual
            [intendedUsage] => COM
            [connectivity] => Online
            [connectivityInterval] => 30 Days
            [opportunityNumber] => 
            [servicePlan] => Standard
            [accessModel] => Single User
            [paymentMethod] => Credit Card
            [recordType] => Attribute based
            [renewalCounter] => 
            [autoRenew] => ON
            [offeringId] => OD-000280
            [offeringCode] => RVTLTS
            [offeringName] => AutoCAD Revit LT Suite
            [marketingName] => AutoCAD Revit LT Suite
            [currency] => GBP
            [annualDeclaredValue] => 
            [pricingMethod] => Array
                (
                    [code] => QTY
                    [description] => Quantity based
                )

            [accounts] => Array
                (
                    [soldTo] => Array
                        (
                            [csn] => **********
                            [name] => Studio Forty
                        )

                    [solutionProvider] => Array
                        (
                            [csn] => **********
                            [name] => TCS CAD & BIM Solutions Limited
                            [localLanguageName] => 
                            [type] => Reseller
                            [address1] => Unit F, Yorkway
                            [address2] => Mandale Ind Est
                            [address3] => 
                            [city] => Stockton On Tees
                            [stateProvince] => 
                            [postalCode] => TS17 6BX
                            [country] => United Kingdom
                            [stateProvinceCode] => 
                            [countryCode] => GB
                        )

                    [nurtureReseller] => Array
                        (
                            [csn] => 
                            [name] => 
                            [lockDate] => 
                            [nurtureDiscountEligibility] => 
                        )

                )

            [endCustomer] => Array
                (
                    [account] => Array
                        (
                            [csn] => **********
                            [name] => Studio Forty
                            [type] => End Customer
                            [address1] => 40 Pauline Gardens
                            [address2] => 
                            [address3] => 
                            [city] => Billericay
                            [stateProvince] => 
                            [postalCode] => CM12 0LB
                            [country] => United Kingdom
                            [individualFlag] => 
                            [namedAccountFlag] => 1
                            [namedAccountGroup] => Territory
                            [parentIndustryGroup] => 
                            [parentIndustrySegment] => 
                            [primaryAdminFirstName] => Kieron
                            [primaryAdminLastName] => Peaty
                            [primaryAdminEmail] => <EMAIL>
                            [teamId] => 9705732
                            [teamName] => Kieron Peaty - 5732
                            [stateProvinceCode] => 
                            [countryCode] => GB
                        )

                    [purchaser] => Array
                        (
                            [first] => Kieron
                            [last] => Peaty
                            [email] => <EMAIL>
                            [status] => Active
                            [portalRegistration] => Registered
                            [doNotCall] => 
                            [doNotEmail] => 
                            [doNotMail] => 1
                        )

                )

        )

)

[subscription_update] [2025-06-09 11:37:42] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, New record inserted, <br>
[subscription_update] [2025-06-09 11:38:12] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-09 11:38:12
[subscription_update] [2025-06-09 11:38:12] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-09 11:38:12] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => d150c652-cbd6-4ad8-b7f5-144618e23a6f
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 55441228350329
            [quantity] => 1
            [message] => subscription quantity changed.
            [modifiedAt] => 2025-06-09T11:18:00.000+0000
        )

    [publishedAt] => 2025-06-09T11:38:10.000Z
    [csn] => **********
)

[subscription_update] [2025-06-09 11:38:12] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-06-09 11:38:15] [autodesk_subscription.class.php:445] Got subscription from api: Array
(
    [0] => Array
        (
            [subscriptionId] => 55441228350329
            [subscriptionReferenceNumber] => 562-91311698
            [switch] => Array
                (
                    [fromSubscriptions] => Array
                        (
                        )

                    [toSubscription] => 
                )

            [quantity] => 1
            [status] => Active
            [startDate] => 2019-04-06
            [endDate] => 2026-06-20
            [term] => Annual
            [billingBehavior] => Recurring
            [billingFrequency] => Annual
            [intendedUsage] => COM
            [connectivity] => Online
            [connectivityInterval] => 30 Days
            [opportunityNumber] => 
            [servicePlan] => Standard
            [accessModel] => Single User
            [paymentMethod] => Credit Card
            [recordType] => Attribute based
            [renewalCounter] => 
            [autoRenew] => ON
            [offeringId] => OD-000031
            [offeringCode] => ACDLT
            [offeringName] => AutoCAD LT
            [marketingName] => AutoCAD LT
            [currency] => 
            [annualDeclaredValue] => 
            [pricingMethod] => Array
                (
                    [code] => QTY
                    [description] => Quantity based
                )

            [accounts] => Array
                (
                    [soldTo] => Array
                        (
                            [csn] => **********
                            [name] => ARCHITECTURAL METAL CRAFTSMEN LT
                        )

                    [solutionProvider] => Array
                        (
                            [csn] => **********
                            [name] => TCS CAD & BIM Solutions Limited
                            [localLanguageName] => 
                            [type] => Reseller
                            [address1] => Unit F, Yorkway
                            [address2] => Mandale Ind Est
                            [address3] => 
                            [city] => Stockton On Tees
                            [stateProvince] => 
                            [postalCode] => TS17 6BX
                            [country] => United Kingdom
                            [stateProvinceCode] => 
                            [countryCode] => GB
                        )

                    [nurtureReseller] => Array
                        (
                            [csn] => 
                            [name] => 
                            [lockDate] => 
                            [nurtureDiscountEligibility] => 
                        )

                )

            [endCustomer] => Array
                (
                    [account] => Array
                        (
                            [csn] => **********
                            [name] => ARCHITECTURAL METAL CRAFTSMEN LT
                            [type] => End Customer
                            [address1] => Finedon Road Ind Est 11 Bradfield
                            [address2] => Close Finedon Road Industrial
                            [address3] => Estate
                            [city] => Wellingborough
                            [stateProvince] => NORTHAMPTONSHIRE
                            [postalCode] => NN8 4RQ
                            [country] => United Kingdom
                            [individualFlag] => 
                            [namedAccountFlag] => 1
                            [namedAccountGroup] => Territory
                            [parentIndustryGroup] => MFG
                            [parentIndustrySegment] => Building Products & Fabricat'n
                            [primaryAdminFirstName] => Scott
                            [primaryAdminLastName] => Odell
                            [primaryAdminEmail] => <EMAIL>
                            [teamId] => 6974473
                            [teamName] => Scott Odell - 4473
                            [stateProvinceCode] => 
                            [countryCode] => GB
                        )

                    [purchaser] => Array
                        (
                            [first] => Scott
                            [last] => Odell
                            [email] => <EMAIL>
                            [status] => Active
                            [portalRegistration] => Registered
                            [doNotCall] => 
                            [doNotEmail] => 
                            [doNotMail] => 
                        )

                )

        )

)

[subscription_update] [2025-06-09 11:38:15] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, Record updated, <br>
[subscription_update] [2025-06-09 11:38:45] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-09 11:38:45
[subscription_update] [2025-06-09 11:38:45] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-09 11:38:45] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 90bf790d-40d0-45c6-abc3-34b5a01c5116
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 74946381873089
            [quantity] => 3
            [message] => subscription quantity changed.
            [modifiedAt] => 2025-06-09T11:23:37.000+0000
        )

    [publishedAt] => 2025-06-09T11:38:42.000Z
    [csn] => **********
)

[subscription_update] [2025-06-09 11:38:45] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-06-09 11:38:45] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-09 11:38:45
[subscription_update] [2025-06-09 11:38:45] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-09 11:38:45] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 4b00f7b5-db16-4ebb-8b66-555ac9fe508a
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 74946381876790
            [quantity] => 2
            [message] => subscription quantity changed.
            [modifiedAt] => 2025-06-09T11:23:37.000+0000
        )

    [publishedAt] => 2025-06-09T11:38:42.000Z
    [csn] => **********
)

[subscription_update] [2025-06-09 11:38:45] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-06-09 11:38:48] [autodesk_subscription.class.php:445] Got subscription from api: Array
(
    [0] => Array
        (
            [subscriptionId] => 74946381873089
            [subscriptionReferenceNumber] => 
            [switch] => Array
                (
                    [fromSubscriptions] => Array
                        (
                        )

                    [toSubscription] => 
                )

            [quantity] => 3
            [status] => Active
            [startDate] => 2025-06-09
            [endDate] => 2026-06-08
            [term] => Annual
            [billingBehavior] => Recurring
            [billingFrequency] => Annual
            [intendedUsage] => COM
            [connectivity] => Online
            [connectivityInterval] => 30 Days
            [opportunityNumber] => 
            [servicePlan] => Standard
            [accessModel] => Single User
            [paymentMethod] => LOC
            [recordType] => Attribute based
            [renewalCounter] => 
            [autoRenew] => ON
            [offeringId] => OD-000031
            [offeringCode] => ACDLT
            [offeringName] => AutoCAD LT
            [marketingName] => AutoCAD LT
            [currency] => GBP
            [annualDeclaredValue] => 
            [pricingMethod] => Array
                (
                    [code] => QTY
                    [description] => Quantity based
                )

            [accounts] => Array
                (
                    [soldTo] => Array
                        (
                            [csn] => **********
                            [name] => Protech Supplies Ltd
                        )

                    [solutionProvider] => Array
                        (
                            [csn] => **********
                            [name] => TCS CAD & BIM Solutions Limited
                            [localLanguageName] => 
                            [type] => Reseller
                            [address1] => Unit F, Yorkway
                            [address2] => Mandale Ind Est
                            [address3] => 
                            [city] => Stockton On Tees
                            [stateProvince] => 
                            [postalCode] => TS17 6BX
                            [country] => United Kingdom
                            [stateProvinceCode] => 
                            [countryCode] => GB
                        )

                    [nurtureReseller] => Array
                        (
                            [csn] => 
                            [name] => 
                            [lockDate] => 
                            [nurtureDiscountEligibility] => 
                        )

                )

            [endCustomer] => Array
                (
                    [account] => Array
                        (
                            [csn] => **********
                            [name] => Protech Supplies Ltd
                            [type] => End Customer
                            [address1] => Brookhill Way
                            [address2] => 
                            [address3] => 
                            [city] => Buckley
                            [stateProvince] => CLWYD
                            [postalCode] => CH7 3PS
                            [country] => United Kingdom
                            [individualFlag] => 
                            [namedAccountFlag] => 
                            [namedAccountGroup] => Territory
                            [parentIndustryGroup] => OTH
                            [parentIndustrySegment] => Other
                            [primaryAdminFirstName] => Dan
                            [primaryAdminLastName] => Morris
                            [primaryAdminEmail] => <EMAIL>
                            [teamId] => ********
                            [teamName] => Dan Morris - 5550
                            [stateProvinceCode] => 
                            [countryCode] => GB
                        )

                    [purchaser] => Array
                        (
                            [first] => Dan
                            [last] => Morris
                            [email] => <EMAIL>
                            [status] => Active
                            [portalRegistration] => 
                            [doNotCall] => 
                            [doNotEmail] => 
                            [doNotMail] => 
                        )

                )

        )

)

[subscription_update] [2025-06-09 11:38:48] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, New record inserted, <br>
[subscription_update] [2025-06-09 11:38:53] [autodesk_subscription.class.php:445] Got subscription from api: Array
(
    [0] => Array
        (
            [subscriptionId] => 74946381876790
            [subscriptionReferenceNumber] => 
            [switch] => Array
                (
                    [fromSubscriptions] => Array
                        (
                        )

                    [toSubscription] => 
                )

            [quantity] => 2
            [status] => Active
            [startDate] => 2025-06-09
            [endDate] => 2026-06-08
            [term] => Annual
            [billingBehavior] => Recurring
            [billingFrequency] => Annual
            [intendedUsage] => COM
            [connectivity] => Online
            [connectivityInterval] => 30 Days
            [opportunityNumber] => 
            [servicePlan] => Standard
            [accessModel] => Single User
            [paymentMethod] => LOC
            [recordType] => Attribute based
            [renewalCounter] => 
            [autoRenew] => ON
            [offeringId] => OD-002134
            [offeringCode] => FSN
            [offeringName] => Fusion
            [marketingName] => Fusion
            [currency] => GBP
            [annualDeclaredValue] => 
            [pricingMethod] => Array
                (
                    [code] => QTY
                    [description] => Quantity based
                )

            [accounts] => Array
                (
                    [soldTo] => Array
                        (
                            [csn] => **********
                            [name] => Protech Supplies Ltd
                        )

                    [solutionProvider] => Array
                        (
                            [csn] => **********
                            [name] => TCS CAD & BIM Solutions Limited
                            [localLanguageName] => 
                            [type] => Reseller
                            [address1] => Unit F, Yorkway
                            [address2] => Mandale Ind Est
                            [address3] => 
                            [city] => Stockton On Tees
                            [stateProvince] => 
                            [postalCode] => TS17 6BX
                            [country] => United Kingdom
                            [stateProvinceCode] => 
                            [countryCode] => GB
                        )

                    [nurtureReseller] => Array
                        (
                            [csn] => 
                            [name] => 
                            [lockDate] => 
                            [nurtureDiscountEligibility] => 
                        )

                )

            [endCustomer] => Array
                (
                    [account] => Array
                        (
                            [csn] => **********
                            [name] => Protech Supplies Ltd
                            [type] => End Customer
                            [address1] => Brookhill Way
                            [address2] => 
                            [address3] => 
                            [city] => Buckley
                            [stateProvince] => CLWYD
                            [postalCode] => CH7 3PS
                            [country] => United Kingdom
                            [individualFlag] => 
                            [namedAccountFlag] => 
                            [namedAccountGroup] => Territory
                            [parentIndustryGroup] => OTH
                            [parentIndustrySegment] => Other
                            [primaryAdminFirstName] => Dan
                            [primaryAdminLastName] => Morris
                            [primaryAdminEmail] => <EMAIL>
                            [teamId] => ********
                            [teamName] => Dan Morris - 5550
                            [stateProvinceCode] => 
                            [countryCode] => GB
                        )

                    [purchaser] => Array
                        (
                            [first] => Dan
                            [last] => Morris
                            [email] => <EMAIL>
                            [status] => Active
                            [portalRegistration] => 
                            [doNotCall] => 
                            [doNotEmail] => 
                            [doNotMail] => 
                        )

                )

        )

)

[subscription_update] [2025-06-09 11:38:53] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, New record inserted, <br>
[subscription_update] [2025-06-09 13:06:18] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-09 13:06:18
[subscription_update] [2025-06-09 13:06:18] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-09 13:06:18] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 46cb3c6e-9d69-4779-8940-88f2fc2ac354
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 72294573291968
            [status] => Active
            [quantity] => 1
            [endDate] => 2026-08-05
            [autoRenew] => ON
            [term] => Annual
            [message] => subscription status,quantity,endDate,autoRenew,term changed.
            [modifiedAt] => 2025-06-09T12:36:13.000+0000
        )

    [publishedAt] => 2025-06-09T13:06:15.000Z
    [csn] => **********
)

[subscription_update] [2025-06-09 13:06:18] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-09 13:06:18] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-09 13:06:18] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-09 13:06:18] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-09 13:06:18] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-09 13:06:18] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-09 13:06:18] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-09 13:06:18] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-09 13:06:18] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '72294573291968', status = 'Active', quantity = 1, endDate = '2026-08-05', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '72294573291968', status = 'Active', quantity = 1, endDate = '2026-08-05', autoRenew = 'ON', term = 'Annual';\n",
        "affected_rows": 2
    }
]
[subscription_update] [2025-06-09 13:06:33] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-09 13:06:33
[subscription_update] [2025-06-09 13:06:33] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-09 13:06:33] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => c36da0fe-d4e8-4b74-a0fc-8ab067cea1e2
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 56097959794144
            [status] => Active
            [quantity] => 1
            [endDate] => 2026-06-20
            [autoRenew] => ON
            [term] => Annual
            [message] => subscription status,quantity,endDate,autoRenew,term changed.
            [modifiedAt] => 2025-06-09T12:36:27.000+0000
        )

    [publishedAt] => 2025-06-09T13:06:30.000Z
    [csn] => **********
)

[subscription_update] [2025-06-09 13:06:33] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-06-09 13:06:36] [autodesk_subscription.class.php:445] Got subscription from api: Array
(
    [0] => Array
        (
            [subscriptionId] => 56097959794144
            [subscriptionReferenceNumber] => 564-48197718
            [switch] => Array
                (
                    [fromSubscriptions] => Array
                        (
                        )

                    [toSubscription] => 
                )

            [quantity] => 1
            [status] => Active
            [startDate] => 2019-06-21
            [endDate] => 2026-06-20
            [term] => Annual
            [billingBehavior] => Recurring
            [billingFrequency] => Annual
            [intendedUsage] => COM
            [connectivity] => Online
            [connectivityInterval] => 30 Days
            [opportunityNumber] => 
            [servicePlan] => Standard
            [accessModel] => Single User
            [paymentMethod] => Credit Card
            [recordType] => Attribute based
            [renewalCounter] => 
            [autoRenew] => ON
            [offeringId] => OD-000031
            [offeringCode] => ACDLT
            [offeringName] => AutoCAD LT
            [marketingName] => AutoCAD LT
            [currency] => 
            [annualDeclaredValue] => 
            [pricingMethod] => Array
                (
                    [code] => QTY
                    [description] => Quantity based
                )

            [accounts] => Array
                (
                    [soldTo] => Array
                        (
                            [csn] => **********
                            [name] => Rodrigues Associates
                        )

                    [solutionProvider] => Array
                        (
                            [csn] => **********
                            [name] => TCS CAD & BIM Solutions Limited
                            [localLanguageName] => 
                            [type] => Reseller
                            [address1] => Unit F, Yorkway
                            [address2] => Mandale Ind Est
                            [address3] => 
                            [city] => Stockton On Tees
                            [stateProvince] => 
                            [postalCode] => TS17 6BX
                            [country] => United Kingdom
                            [stateProvinceCode] => 
                            [countryCode] => GB
                        )

                    [nurtureReseller] => Array
                        (
                            [csn] => 
                            [name] => 
                            [lockDate] => 
                            [nurtureDiscountEligibility] => 
                        )

                )

            [endCustomer] => Array
                (
                    [account] => Array
                        (
                            [csn] => **********
                            [name] => Rodrigues Associates
                            [type] => End Customer
                            [address1] => 3 Amwell Street
                            [address2] => 
                            [address3] => 
                            [city] => London
                            [stateProvince] => LONDON
                            [postalCode] => EC1R 1UL
                            [country] => United Kingdom
                            [individualFlag] => 
                            [namedAccountFlag] => 1
                            [namedAccountGroup] => Territory
                            [parentIndustryGroup] => AEC
                            [parentIndustrySegment] => Engineering Service Providers
                            [primaryAdminFirstName] => Mervyn
                            [primaryAdminLastName] => Rodrigues
                            [primaryAdminEmail] => <EMAIL>
                            [teamId] => 7533407
                            [teamName] => Rodrigues Assoc - 3407
                            [stateProvinceCode] => 
                            [countryCode] => GB
                        )

                    [purchaser] => Array
                        (
                            [first] => Mervyn
                            [last] => Rodrigues
                            [email] => <EMAIL>
                            [status] => Active
                            [portalRegistration] => Active
                            [doNotCall] => 1
                            [doNotEmail] => 1
                            [doNotMail] => 
                        )

                )

        )

)

[subscription_update] [2025-06-09 13:06:36] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, Record updated, <br>
[subscription_update] [2025-06-09 14:08:56] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-09 14:08:56
[subscription_update] [2025-06-09 14:08:56] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-09 14:08:56] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 140514d8-e7ed-4d41-bea7-c2e2a532e2f1
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 71861567707131
            [status] => Active
            [quantity] => 1
            [endDate] => 2026-06-16
            [autoRenew] => ON
            [term] => Annual
            [message] => subscription status,quantity,endDate,autoRenew,term changed.
            [modifiedAt] => 2025-06-09T13:43:51.000+0000
        )

    [publishedAt] => 2025-06-09T14:08:53.000Z
    [csn] => **********
)

[subscription_update] [2025-06-09 14:08:56] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-09 14:08:56] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-09 14:08:56] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-09 14:08:56] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-09 14:08:56] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-09 14:08:56] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-09 14:08:56] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-09 14:08:56] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-09 14:08:56] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '71861567707131', status = 'Active', quantity = 1, endDate = '2026-06-16', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '71861567707131', status = 'Active', quantity = 1, endDate = '2026-06-16', autoRenew = 'ON', term = 'Annual';\n",
        "affected_rows": 2
    }
]
[subscription_update] [2025-06-09 15:37:00] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-09 15:37:00
[subscription_update] [2025-06-09 15:37:00] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-09 15:37:00] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 223e97f1-70db-4c03-9569-23e61a66cbcb
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 72294573291968
            [quantity] => 1
            [message] => subscription quantity changed.
            [modifiedAt] => 2025-06-09T15:11:49.000+0000
        )

    [publishedAt] => 2025-06-09T15:36:58.000Z
    [csn] => **********
)

[subscription_update] [2025-06-09 15:37:00] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-09 15:37:00] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-09 15:37:00] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-09 15:37:00] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-09 15:37:00] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '72294573291968', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '72294573291968', quantity = 1;\n",
        "affected_rows": 0
    }
]
[subscription_update] [2025-06-09 15:38:50] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-09 15:38:50
[subscription_update] [2025-06-09 15:38:50] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-09 15:38:50] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 0fac2e55-1276-47be-b5df-b93a1db7c37c
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 56097959794144
            [quantity] => 1
            [message] => subscription quantity changed.
            [modifiedAt] => 2025-06-09T15:08:41.000+0000
        )

    [publishedAt] => 2025-06-09T15:38:48.000Z
    [csn] => **********
)

[subscription_update] [2025-06-09 15:38:50] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-06-09 15:39:00] [autodesk_subscription.class.php:445] Got subscription from api: Array
(
    [0] => Array
        (
            [subscriptionId] => 56097959794144
            [subscriptionReferenceNumber] => 564-48197718
            [switch] => Array
                (
                    [fromSubscriptions] => Array
                        (
                        )

                    [toSubscription] => 
                )

            [quantity] => 1
            [status] => Active
            [startDate] => 2019-06-21
            [endDate] => 2026-06-20
            [term] => Annual
            [billingBehavior] => Recurring
            [billingFrequency] => Annual
            [intendedUsage] => COM
            [connectivity] => Online
            [connectivityInterval] => 30 Days
            [opportunityNumber] => 
            [servicePlan] => Standard
            [accessModel] => Single User
            [paymentMethod] => Credit Card
            [recordType] => Attribute based
            [renewalCounter] => 
            [autoRenew] => ON
            [offeringId] => OD-000031
            [offeringCode] => ACDLT
            [offeringName] => AutoCAD LT
            [marketingName] => AutoCAD LT
            [currency] => GBP
            [annualDeclaredValue] => 
            [pricingMethod] => Array
                (
                    [code] => QTY
                    [description] => Quantity based
                )

            [accounts] => Array
                (
                    [soldTo] => Array
                        (
                            [csn] => **********
                            [name] => Rodrigues Associates
                        )

                    [solutionProvider] => Array
                        (
                            [csn] => **********
                            [name] => TCS CAD & BIM Solutions Limited
                            [localLanguageName] => 
                            [type] => Reseller
                            [address1] => Unit F, Yorkway
                            [address2] => Mandale Ind Est
                            [address3] => 
                            [city] => Stockton On Tees
                            [stateProvince] => 
                            [postalCode] => TS17 6BX
                            [country] => United Kingdom
                            [stateProvinceCode] => 
                            [countryCode] => GB
                        )

                    [nurtureReseller] => Array
                        (
                            [csn] => 
                            [name] => 
                            [lockDate] => 
                            [nurtureDiscountEligibility] => 
                        )

                )

            [endCustomer] => Array
                (
                    [account] => Array
                        (
                            [csn] => **********
                            [name] => Rodrigues Associates
                            [type] => End Customer
                            [address1] => 3 Amwell Street
                            [address2] => 
                            [address3] => 
                            [city] => London
                            [stateProvince] => LONDON
                            [postalCode] => EC1R 1UL
                            [country] => United Kingdom
                            [individualFlag] => 
                            [namedAccountFlag] => 1
                            [namedAccountGroup] => Territory
                            [parentIndustryGroup] => AEC
                            [parentIndustrySegment] => Engineering Service Providers
                            [primaryAdminFirstName] => Mervyn
                            [primaryAdminLastName] => Rodrigues
                            [primaryAdminEmail] => <EMAIL>
                            [teamId] => 7533407
                            [teamName] => Rodrigues Assoc - 3407
                            [stateProvinceCode] => 
                            [countryCode] => GB
                        )

                    [purchaser] => Array
                        (
                            [first] => Mervyn
                            [last] => Rodrigues
                            [email] => <EMAIL>
                            [status] => Active
                            [portalRegistration] => Active
                            [doNotCall] => 1
                            [doNotEmail] => 1
                            [doNotMail] => 
                        )

                )

        )

)

[subscription_update] [2025-06-09 15:39:00] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, Record updated, <br>
[subscription_update] [2025-06-09 15:41:47] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-09 15:41:47
[subscription_update] [2025-06-09 15:41:47] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-09 15:41:47] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => bb7f8460-fc2c-4936-a573-02c787a8e40d
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 71861567707131
            [quantity] => 1
            [message] => subscription quantity changed.
            [modifiedAt] => 2025-06-09T15:11:39.000+0000
        )

    [publishedAt] => 2025-06-09T15:41:45.000Z
    [csn] => **********
)

[subscription_update] [2025-06-09 15:41:47] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-09 15:41:47] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-09 15:41:47] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-09 15:41:47] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-09 15:41:47] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '71861567707131', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '71861567707131', quantity = 1;\n",
        "affected_rows": 0
    }
]
[subscription_update] [2025-06-09 17:09:45] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-09 17:09:45
[subscription_update] [2025-06-09 17:09:45] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-09 17:09:45] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 517f6b25-0087-4516-915b-ead4b1cc968a
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 71810280138434
            [status] => Active
            [quantity] => 1
            [endDate] => 2026-06-10
            [autoRenew] => ON
            [term] => Annual
            [message] => subscription status,quantity,endDate,autoRenew,term changed.
            [modifiedAt] => 2025-06-09T16:35:49.000+0000
        )

    [publishedAt] => 2025-06-09T17:09:43.000Z
    [csn] => **********
)

[subscription_update] [2025-06-09 17:09:45] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-09 17:09:45] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-09 17:09:45] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-09 17:09:45] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-09 17:09:45] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-09 17:09:45] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-09 17:09:45] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-09 17:09:45] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-09 17:09:45] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '71810280138434', status = 'Active', quantity = 1, endDate = '2026-06-10', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '71810280138434', status = 'Active', quantity = 1, endDate = '2026-06-10', autoRenew = 'ON', term = 'Annual';\n",
        "affected_rows": 2
    }
]
[subscription_update] [2025-06-09 17:38:18] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-09 17:38:18
[subscription_update] [2025-06-09 17:38:18] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-09 17:38:18] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 2b6d3bdf-6ef1-4fda-a912-305197a9726a
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 65547094153537
            [status] => Active
            [quantity] => 1
            [endDate] => 2026-06-16
            [term] => Annual
            [message] => subscription status,quantity,endDate,term changed.
            [modifiedAt] => 2025-06-09T17:12:11.000+0000
        )

    [publishedAt] => 2025-06-09T17:38:16.000Z
    [csn] => **********
)

[subscription_update] [2025-06-09 17:38:18] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-09 17:38:18] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-09 17:38:18] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-09 17:38:18] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-09 17:38:18] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-09 17:38:18] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-09 17:38:18] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-09 17:38:18] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '65547094153537', status = 'Active', quantity = 1, endDate = '2026-06-16', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '65547094153537', status = 'Active', quantity = 1, endDate = '2026-06-16', term = 'Annual';\n",
        "affected_rows": 2
    }
]
[subscription_update] [2025-06-09 17:43:47] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-09 17:43:47
[subscription_update] [2025-06-09 17:43:47] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-09 17:43:47] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 98502894-14ca-47b7-9ddc-9db64cc453fb
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 56468981173027
            [quantity] => 1
            [autoRenew] => OFF
            [message] => subscription quantity,autoRenew changed.
            [modifiedAt] => 2025-06-09T17:28:41.000+0000
        )

    [publishedAt] => 2025-06-09T17:43:45.000Z
    [csn] => **********
)

[subscription_update] [2025-06-09 17:43:47] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-09 17:43:47] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-09 17:43:47] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-09 17:43:47] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-09 17:43:47] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-09 17:43:47] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '56468981173027', quantity = 1, autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '56468981173027', quantity = 1, autoRenew = 'OFF';\n",
        "affected_rows": 2
    }
]
[subscription_update] [2025-06-09 17:45:15] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-09 17:45:15
[subscription_update] [2025-06-09 17:45:15] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-09 17:45:15] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => ed96e5be-ddc6-4110-9216-8649af3b539c
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 65547094153537
            [autoRenew] => OFF
            [message] => subscription autoRenew changed.
            [modifiedAt] => 2025-06-09T17:29:49.000+0000
        )

    [publishedAt] => 2025-06-09T17:45:12.000Z
    [csn] => **********
)

[subscription_update] [2025-06-09 17:45:15] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-09 17:45:15] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-09 17:45:15] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-09 17:45:15] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-09 17:45:15] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '65547094153537', autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '65547094153537', autoRenew = 'OFF';\n",
        "affected_rows": 2
    }
]
[subscription_update] [2025-06-09 19:39:01] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-09 19:39:01
[subscription_update] [2025-06-09 19:39:01] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-09 19:39:01] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => f19db2d6-2763-4485-81ce-0bfad854ce13
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 65547094153537
            [quantity] => 1
            [message] => subscription quantity changed.
            [modifiedAt] => 2025-06-09T19:13:49.000+0000
        )

    [publishedAt] => 2025-06-09T19:38:58.000Z
    [csn] => **********
)

[subscription_update] [2025-06-09 19:39:01] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-09 19:39:01] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-09 19:39:01] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-09 19:39:01] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-09 19:39:01] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '65547094153537', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '65547094153537', quantity = 1;\n",
        "affected_rows": 0
    }
]
[subscription_update] [2025-06-09 19:40:08] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-09 19:40:08
[subscription_update] [2025-06-09 19:40:08] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-09 19:40:08] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => a3df3b0b-d218-4d04-a19e-ca79b5fe3b88
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 71810280138434
            [quantity] => 1
            [message] => subscription quantity changed.
            [modifiedAt] => 2025-06-09T19:14:58.000+0000
        )

    [publishedAt] => 2025-06-09T19:40:06.000Z
    [csn] => **********
)

[subscription_update] [2025-06-09 19:40:08] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-09 19:40:08] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-09 19:40:08] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-09 19:40:08] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-09 19:40:08] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '71810280138434', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '71810280138434', quantity = 1;\n",
        "affected_rows": 0
    }
]
[subscription_update] [2025-06-10 07:06:39] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-10 07:06:39
[subscription_update] [2025-06-10 07:06:39] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-10 07:06:39] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 97102679-5df8-4b41-a0db-06800923ce76
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 55303232878149
            [status] => Active
            [message] => subscription status changed.
            [modifiedAt] => 2025-06-10T06:33:42.000+0000
        )

    [publishedAt] => 2025-06-10T07:06:37.000Z
    [csn] => **********
)

[subscription_update] [2025-06-10 07:06:39] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-10 07:06:39] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-10 07:06:39] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-10 07:06:39] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-10 07:06:39] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '55303232878149', status = 'Active' ON DUPLICATE KEY UPDATE subscriptionId = '55303232878149', status = 'Active';\n",
        "affected_rows": 2
    }
]
[subscription_update] [2025-06-10 07:07:18] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-10 07:07:18
[subscription_update] [2025-06-10 07:07:18] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-10 07:07:18] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 5baf8853-a3e9-4390-905e-de552662fac8
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 71593456656960
            [status] => Active
            [message] => subscription status changed.
            [modifiedAt] => 2025-06-10T06:36:18.000+0000
        )

    [publishedAt] => 2025-06-10T07:07:16.000Z
    [csn] => **********
)

[subscription_update] [2025-06-10 07:07:18] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-10 07:07:18] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-10 07:07:18] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-10 07:07:18] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-10 07:07:18] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '71593456656960', status = 'Active' ON DUPLICATE KEY UPDATE subscriptionId = '71593456656960', status = 'Active';\n",
        "affected_rows": 2
    }
]
[subscription_update] [2025-06-10 08:09:35] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-10 08:09:35
[subscription_update] [2025-06-10 08:09:35] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-10 08:09:35] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => ced20a2b-a6d0-454b-8126-bc1b53f5ec67
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 71741020087145
            [status] => Active
            [quantity] => 1
            [endDate] => 2026-06-09
            [term] => Annual
            [message] => subscription status,quantity,endDate,term changed.
            [modifiedAt] => 2025-06-10T07:45:43.000+0000
        )

    [publishedAt] => 2025-06-10T08:09:32.000Z
    [csn] => **********
)

[subscription_update] [2025-06-10 08:09:35] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-10 08:09:35] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-10 08:09:35] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-10 08:09:35] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-10 08:09:35] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-10 08:09:35] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-10 08:09:35] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-10 08:09:35] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '71741020087145', status = 'Active', quantity = 1, endDate = '2026-06-09', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '71741020087145', status = 'Active', quantity = 1, endDate = '2026-06-09', term = 'Annual';\n",
        "affected_rows": 2
    }
]
[subscription_update] [2025-06-10 08:13:16] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-10 08:13:16
[subscription_update] [2025-06-10 08:13:16] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-10 08:13:16] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => cf2a623f-70cb-4690-8cb4-114a4f83a95f
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 71741020087145
            [autoRenew] => OFF
            [message] => subscription autoRenew changed.
            [modifiedAt] => 2025-06-10T07:49:04.000+0000
        )

    [publishedAt] => 2025-06-10T08:13:13.000Z
    [csn] => **********
)

[subscription_update] [2025-06-10 08:13:16] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-10 08:13:16] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-10 08:13:16] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-10 08:13:16] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-10 08:13:16] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '71741020087145', autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '71741020087145', autoRenew = 'OFF';\n",
        "affected_rows": 2
    }
]
[subscription_update] [2025-06-10 11:13:23] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-10 11:13:23
[subscription_update] [2025-06-10 11:13:23] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-10 11:13:23] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => b7e65967-92b5-4073-b6b6-b9f34498ddc9
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 71930963170248
            [status] => Active
            [quantity] => 1
            [endDate] => 2026-06-24
            [autoRenew] => ON
            [term] => Annual
            [message] => subscription status,quantity,endDate,autoRenew,term changed.
            [modifiedAt] => 2025-06-10T10:58:18.000+0000
        )

    [publishedAt] => 2025-06-10T11:13:21.000Z
    [csn] => **********
)

[subscription_update] [2025-06-10 11:13:23] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-10 11:13:23] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-10 11:13:23] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-10 11:13:23] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-10 11:13:23] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-10 11:13:23] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-10 11:13:23] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-10 11:13:23] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-10 11:13:23] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '71930963170248', status = 'Active', quantity = 1, endDate = '2026-06-24', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '71930963170248', status = 'Active', quantity = 1, endDate = '2026-06-24', autoRenew = 'ON', term = 'Annual';\n",
        "affected_rows": 2
    }
]
[subscription_update] [2025-06-10 11:38:19] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-10 11:38:19
[subscription_update] [2025-06-10 11:38:19] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-10 11:38:19] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => a94d47df-70e8-4766-b116-2101d9bdedd7
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 71741020087145
            [quantity] => 1
            [message] => subscription quantity changed.
            [modifiedAt] => 2025-06-10T11:23:10.000+0000
        )

    [publishedAt] => 2025-06-10T11:38:17.000Z
    [csn] => **********
)

[subscription_update] [2025-06-10 11:38:19] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-10 11:38:19] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-10 11:38:19] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-10 11:38:19] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-10 11:38:19] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '71741020087145', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '71741020087145', quantity = 1;\n",
        "affected_rows": 0
    }
]
[subscription_update] [2025-06-10 11:38:39] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-10 11:38:39
[subscription_update] [2025-06-10 11:38:39] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-10 11:38:39] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 773978ef-5769-44fe-b1eb-2fc8b1787f87
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 71930963170248
            [quantity] => 1
            [message] => subscription quantity changed.
            [modifiedAt] => 2025-06-10T11:23:31.000+0000
        )

    [publishedAt] => 2025-06-10T11:38:37.000Z
    [csn] => **********
)

[subscription_update] [2025-06-10 11:38:39] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-10 11:38:39] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-10 11:38:39] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-10 11:38:39] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-10 11:38:39] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '71930963170248', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '71930963170248', quantity = 1;\n",
        "affected_rows": 0
    }
]
[subscription_update] [2025-06-10 16:37:50] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-10 16:37:50
[subscription_update] [2025-06-10 16:37:50] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-10 16:37:50] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 3875d320-200f-47ca-9119-fe1fd62a01a3
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 62186764789584
            [status] => Active
            [quantity] => 1
            [endDate] => 2026-05-23
            [autoRenew] => ON
            [term] => Annual
            [message] => subscription status,quantity,endDate,autoRenew,term changed.
            [modifiedAt] => 2025-06-10T16:12:44.000+0000
        )

    [publishedAt] => 2025-06-10T16:37:47.000Z
    [csn] => **********
)

[subscription_update] [2025-06-10 16:37:50] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-10 16:37:50] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-10 16:37:50] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-10 16:37:50] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-10 16:37:50] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-10 16:37:50] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-10 16:37:50] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-10 16:37:50] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-10 16:37:50] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '62186764789584', status = 'Active', quantity = 1, endDate = '2026-05-23', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '62186764789584', status = 'Active', quantity = 1, endDate = '2026-05-23', autoRenew = 'ON', term = 'Annual';\n",
        "affected_rows": 2
    }
]
[subscription_update] [2025-06-11 03:36:48] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-11 03:36:48
[subscription_update] [2025-06-11 03:36:48] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-11 03:36:48] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 78b35f52-f68b-4c9e-8264-ebb34fb4644b
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 62186764789584
            [quantity] => 1
            [message] => subscription quantity changed.
            [modifiedAt] => 2025-06-11T03:18:06.000+0000
        )

    [publishedAt] => 2025-06-11T03:36:46.000Z
    [csn] => **********
)

[subscription_update] [2025-06-11 03:36:48] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-11 03:36:48] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-11 03:36:48] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-11 03:36:48] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-11 03:36:48] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '62186764789584', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '62186764789584', quantity = 1;\n",
        "affected_rows": 0
    }
]
[subscription_update] [2025-06-11 06:43:37] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-11 06:43:37
[subscription_update] [2025-06-11 06:43:37] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-11 06:43:37] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 8551d565-ae4f-43b9-90a1-f13df639bc63
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 74946381873089
            [quantity] => 3
            [message] => subscription quantity changed.
            [modifiedAt] => 2025-06-11T06:28:32.000+0000
        )

    [publishedAt] => 2025-06-11T06:43:34.000Z
    [csn] => **********
)

[subscription_update] [2025-06-11 06:43:37] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-11 06:43:37] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-11 06:43:37] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-11 06:43:37] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-11 06:43:37] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '74946381873089', quantity = 3 ON DUPLICATE KEY UPDATE subscriptionId = '74946381873089', quantity = 3;\n",
        "affected_rows": 0
    }
]
[subscription_update] [2025-06-11 06:45:04] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-11 06:45:04
[subscription_update] [2025-06-11 06:45:04] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-11 06:45:04] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 77b388a8-ab0b-493d-9a12-e73c1a37ef1c
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 74946381876790
            [quantity] => 2
            [message] => subscription quantity changed.
            [modifiedAt] => 2025-06-11T06:29:59.000+0000
        )

    [publishedAt] => 2025-06-11T06:45:01.000Z
    [csn] => **********
)

[subscription_update] [2025-06-11 06:45:04] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-11 06:45:04] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-11 06:45:04] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-11 06:45:04] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-11 06:45:04] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '74946381876790', quantity = 2 ON DUPLICATE KEY UPDATE subscriptionId = '74946381876790', quantity = 2;\n",
        "affected_rows": 0
    }
]
[subscription_update] [2025-06-11 07:39:05] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-11 07:39:05
[subscription_update] [2025-06-11 07:39:05] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-11 07:39:05] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 3a407c19-7462-4fe5-94c2-900e1123ca42
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 56094712461866
            [status] => Active
            [quantity] => 1
            [endDate] => 2028-06-18
            [autoRenew] => ON
            [term] => 3 Year
            [message] => subscription status,quantity,endDate,autoRenew,term changed.
            [modifiedAt] => 2025-06-11T07:19:01.000+0000
        )

    [publishedAt] => 2025-06-11T07:39:03.000Z
    [csn] => **********
)

[subscription_update] [2025-06-11 07:39:06] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-11 07:39:06] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-11 07:39:06] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-11 07:39:06] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-11 07:39:06] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-11 07:39:06] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-11 07:39:06] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-11 07:39:06] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-11 07:39:06] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '56094712461866', status = 'Active', quantity = 1, endDate = '2028-06-18', autoRenew = 'ON', term = '3 Year' ON DUPLICATE KEY UPDATE subscriptionId = '56094712461866', status = 'Active', quantity = 1, endDate = '2028-06-18', autoRenew = 'ON', term = '3 Year';\n",
        "affected_rows": 2
    }
]
[subscription_update] [2025-06-11 09:08:38] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-11 09:08:38
[subscription_update] [2025-06-11 09:08:38] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-11 09:08:38] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 0d727a0b-ac2e-47f6-aeee-7c0d5247b450
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 58258380428586
            [status] => Active
            [quantity] => 1
            [endDate] => 2026-07-10
            [autoRenew] => ON
            [term] => Annual
            [message] => subscription status,quantity,endDate,autoRenew,term changed.
            [modifiedAt] => 2025-06-11T08:53:33.000+0000
        )

    [publishedAt] => 2025-06-11T09:08:35.000Z
    [csn] => **********
)

[subscription_update] [2025-06-11 09:08:38] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-11 09:08:38] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-11 09:08:38] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-11 09:08:38] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-11 09:08:38] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-11 09:08:38] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-11 09:08:38] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-11 09:08:38] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-11 09:08:38] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '58258380428586', status = 'Active', quantity = 1, endDate = '2026-07-10', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '58258380428586', status = 'Active', quantity = 1, endDate = '2026-07-10', autoRenew = 'ON', term = 'Annual';\n",
        "affected_rows": 2
    }
]
[subscription_update] [2025-06-11 09:46:08] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-11 09:46:08
[subscription_update] [2025-06-11 09:46:08] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-11 09:46:08] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => b6b3c189-15a8-473d-81dd-d3f0d665cd16
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 65538526966740
            [status] => Active
            [quantity] => 1
            [endDate] => 2026-06-12
            [autoRenew] => ON
            [term] => Annual
            [message] => subscription status,quantity,endDate,autoRenew,term changed.
            [modifiedAt] => 2025-06-11T09:07:04.000+0000
        )

    [publishedAt] => 2025-06-11T09:46:05.000Z
    [csn] => **********
)

[subscription_update] [2025-06-11 09:46:08] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-11 09:46:08] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-11 09:46:08] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-11 09:46:08] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-11 09:46:08] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-11 09:46:08] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-11 09:46:08] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-11 09:46:08] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-11 09:46:08] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '65538526966740', status = 'Active', quantity = 1, endDate = '2026-06-12', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '65538526966740', status = 'Active', quantity = 1, endDate = '2026-06-12', autoRenew = 'ON', term = 'Annual';\n",
        "affected_rows": 2
    }
]
[subscription_update] [2025-06-11 10:15:36] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-11 10:15:36
[subscription_update] [2025-06-11 10:15:36] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-11 10:15:36] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 527cd82a-57b4-45e3-bf97-1e749ab68dcd
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 62340821508147
            [status] => Active
            [quantity] => 4
            [endDate] => 2026-06-16
            [autoRenew] => ON
            [term] => Annual
            [message] => subscription status,quantity,endDate,autoRenew,term changed.
            [modifiedAt] => 2025-06-11T10:00:26.000+0000
        )

    [publishedAt] => 2025-06-11T10:15:33.000Z
    [csn] => **********
)

[subscription_update] [2025-06-11 10:15:36] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-11 10:15:36] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-11 10:15:36] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-11 10:15:36] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-11 10:15:36] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-11 10:15:36] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-11 10:15:36] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-11 10:15:36] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-11 10:15:36] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '62340821508147', status = 'Active', quantity = 4, endDate = '2026-06-16', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '62340821508147', status = 'Active', quantity = 4, endDate = '2026-06-16', autoRenew = 'ON', term = 'Annual';\n",
        "affected_rows": 2
    }
]
[subscription_update] [2025-06-11 10:15:43] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-11 10:15:43
[subscription_update] [2025-06-11 10:15:43] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-11 10:15:43] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 2133aea9-94ab-41d3-9c49-0734a5460aa6
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 68683034038427
            [status] => Active
            [quantity] => 2
            [endDate] => 2026-06-16
            [autoRenew] => ON
            [term] => Annual
            [message] => subscription status,quantity,endDate,autoRenew,term changed.
            [modifiedAt] => 2025-06-11T10:00:27.000+0000
        )

    [publishedAt] => 2025-06-11T10:15:41.000Z
    [csn] => **********
)

[subscription_update] [2025-06-11 10:15:43] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-11 10:15:43] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-11 10:15:43] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-11 10:15:43] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-11 10:15:43] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-11 10:15:43] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-11 10:15:43] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-11 10:15:43] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-11 10:15:43] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '68683034038427', status = 'Active', quantity = 2, endDate = '2026-06-16', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '68683034038427', status = 'Active', quantity = 2, endDate = '2026-06-16', autoRenew = 'ON', term = 'Annual';\n",
        "affected_rows": 2
    }
]
[subscription_update] [2025-06-11 10:36:44] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-11 10:36:44
[subscription_update] [2025-06-11 10:36:44] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-11 10:36:44] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => e943c6aa-ac86-472c-b334-fd51e1050cfc
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 65468891106219
            [status] => Active
            [quantity] => 1
            [endDate] => 2026-06-07
            [autoRenew] => ON
            [term] => Annual
            [message] => subscription status,quantity,endDate,autoRenew,term changed.
            [modifiedAt] => 2025-06-11T10:06:39.000+0000
        )

    [publishedAt] => 2025-06-11T10:36:42.000Z
    [csn] => **********
)

[subscription_update] [2025-06-11 10:36:44] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-11 10:36:44] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-11 10:36:44] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-11 10:36:44] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-11 10:36:44] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-11 10:36:44] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-11 10:36:44] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-11 10:36:44] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-11 10:36:44] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '65468891106219', status = 'Active', quantity = 1, endDate = '2026-06-07', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '65468891106219', status = 'Active', quantity = 1, endDate = '2026-06-07', autoRenew = 'ON', term = 'Annual';\n",
        "affected_rows": 2
    }
]
[subscription_update] [2025-06-11 11:35:11] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-11 11:35:11
[subscription_update] [2025-06-11 11:35:11] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-11 11:35:11] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 7ce149ac-5996-4c31-97a1-fea865653c53
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 65468891106219
            [quantity] => 1
            [message] => subscription quantity changed.
            [modifiedAt] => 2025-06-11T11:20:02.000+0000
        )

    [publishedAt] => 2025-06-11T11:35:09.000Z
    [csn] => **********
)

[subscription_update] [2025-06-11 11:35:11] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-11 11:35:11] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-11 11:35:11] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-11 11:35:11] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-11 11:35:12] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '65468891106219', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '65468891106219', quantity = 1;\n",
        "affected_rows": 0
    }
]
[subscription_update] [2025-06-11 11:35:21] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-11 11:35:21
[subscription_update] [2025-06-11 11:35:21] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-11 11:35:21] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 486ed325-1e52-45aa-8f1d-b125088531c2
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 65538526966740
            [quantity] => 1
            [message] => subscription quantity changed.
            [modifiedAt] => 2025-06-11T11:20:13.000+0000
        )

    [publishedAt] => 2025-06-11T11:35:19.000Z
    [csn] => **********
)

[subscription_update] [2025-06-11 11:35:21] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-11 11:35:21] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-11 11:35:21] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-11 11:35:21] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-11 11:35:21] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '65538526966740', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '65538526966740', quantity = 1;\n",
        "affected_rows": 0
    }
]
[subscription_update] [2025-06-11 11:36:00] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-11 11:36:00
[subscription_update] [2025-06-11 11:36:00] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-11 11:36:00] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 292e5d0f-8618-467a-a1ca-587f10b3849c
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 68683034038427
            [quantity] => 2
            [message] => subscription quantity changed.
            [modifiedAt] => 2025-06-11T11:20:52.000+0000
        )

    [publishedAt] => 2025-06-11T11:35:58.000Z
    [csn] => **********
)

[subscription_update] [2025-06-11 11:36:00] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-11 11:36:00] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-11 11:36:00] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-11 11:36:00] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-11 11:36:00] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '68683034038427', quantity = 2 ON DUPLICATE KEY UPDATE subscriptionId = '68683034038427', quantity = 2;\n",
        "affected_rows": 0
    }
]
[subscription_update] [2025-06-11 11:38:32] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-11 11:38:32
[subscription_update] [2025-06-11 11:38:32] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-11 11:38:32] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 769fb9e7-8f80-4838-bcc0-2f5c069d47fa
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 56094712461866
            [quantity] => 1
            [message] => subscription quantity changed.
            [modifiedAt] => 2025-06-11T11:18:25.000+0000
        )

    [publishedAt] => 2025-06-11T11:38:30.000Z
    [csn] => **********
)

[subscription_update] [2025-06-11 11:38:32] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-11 11:38:32] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-11 11:38:32] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-11 11:38:32] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-11 11:38:32] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '56094712461866', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '56094712461866', quantity = 1;\n",
        "affected_rows": 0
    }
]
[subscription_update] [2025-06-11 11:39:02] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-11 11:39:02
[subscription_update] [2025-06-11 11:39:02] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-11 11:39:02] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 63af7410-1ca7-4cf9-b189-166a72dfd29e
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 58258380428586
            [quantity] => 1
            [message] => subscription quantity changed.
            [modifiedAt] => 2025-06-11T11:18:54.000+0000
        )

    [publishedAt] => 2025-06-11T11:39:00.000Z
    [csn] => **********
)

[subscription_update] [2025-06-11 11:39:02] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-11 11:39:02] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-11 11:39:02] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-11 11:39:02] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-11 11:39:02] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '58258380428586', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '58258380428586', quantity = 1;\n",
        "affected_rows": 0
    }
]
[subscription_update] [2025-06-11 11:39:42] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-11 11:39:42
[subscription_update] [2025-06-11 11:39:42] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-11 11:39:42] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 0c03317d-d1e4-4432-9443-5483310ad3cb
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 62340821508147
            [quantity] => 4
            [message] => subscription quantity changed.
            [modifiedAt] => 2025-06-11T11:19:34.000+0000
        )

    [publishedAt] => 2025-06-11T11:39:40.000Z
    [csn] => **********
)

[subscription_update] [2025-06-11 11:39:42] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-11 11:39:42] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-11 11:39:42] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-11 11:39:42] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-11 11:39:42] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '62340821508147', quantity = 4 ON DUPLICATE KEY UPDATE subscriptionId = '62340821508147', quantity = 4;\n",
        "affected_rows": 0
    }
]
[subscription_update] [2025-06-11 11:39:51] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-11 11:39:51
[subscription_update] [2025-06-11 11:39:51] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-11 11:39:51] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => c4f7dd6f-34dc-4412-8dae-333a9b1c3abf
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 74963510826685
            [quantity] => 1
            [message] => subscription quantity changed.
            [modifiedAt] => 2025-06-11T11:24:43.000+0000
        )

    [publishedAt] => 2025-06-11T11:39:49.000Z
    [csn] => **********
)

[subscription_update] [2025-06-11 11:39:51] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-06-11 11:39:54] [autodesk_subscription.class.php:445] Got subscription from api: Array
(
    [0] => Array
        (
            [subscriptionId] => 74963510826685
            [subscriptionReferenceNumber] => 
            [switch] => Array
                (
                    [fromSubscriptions] => Array
                        (
                        )

                    [toSubscription] => 
                )

            [quantity] => 1
            [status] => Active
            [startDate] => 2025-06-11
            [endDate] => 2026-06-10
            [term] => Annual
            [billingBehavior] => Recurring
            [billingFrequency] => Annual
            [intendedUsage] => COM
            [connectivity] => Online
            [connectivityInterval] => 30 Days
            [opportunityNumber] => 
            [servicePlan] => Standard
            [accessModel] => Single User
            [paymentMethod] => LOC
            [recordType] => Attribute based
            [renewalCounter] => 
            [autoRenew] => ON
            [offeringId] => OD-000031
            [offeringCode] => ACDLT
            [offeringName] => AutoCAD LT
            [marketingName] => AutoCAD LT
            [currency] => GBP
            [annualDeclaredValue] => 
            [pricingMethod] => Array
                (
                    [code] => QTY
                    [description] => Quantity based
                )

            [accounts] => Array
                (
                    [soldTo] => Array
                        (
                            [csn] => **********
                            [name] => Lichfield & Hatherton Canals Restoration Trust Ltd
                        )

                    [solutionProvider] => Array
                        (
                            [csn] => **********
                            [name] => TCS CAD & BIM Solutions Limited
                            [localLanguageName] => 
                            [type] => Reseller
                            [address1] => Unit F, Yorkway
                            [address2] => Mandale Ind Est
                            [address3] => 
                            [city] => Stockton On Tees
                            [stateProvince] => 
                            [postalCode] => TS17 6BX
                            [country] => United Kingdom
                            [stateProvinceCode] => 
                            [countryCode] => GB
                        )

                    [nurtureReseller] => Array
                        (
                            [csn] => 
                            [name] => 
                            [lockDate] => 
                            [nurtureDiscountEligibility] => 
                        )

                )

            [endCustomer] => Array
                (
                    [account] => Array
                        (
                            [csn] => **********
                            [name] => Lichfield & Hatherton Canals Restoration Trust Ltd
                            [type] => End Customer
                            [address1] => 45 Stowe Street
                            [address2] => 
                            [address3] => 
                            [city] => Lichfield
                            [stateProvince] => STAFFORDSHIRE
                            [postalCode] => WS13 6AQ
                            [country] => United Kingdom
                            [individualFlag] => 
                            [namedAccountFlag] => 
                            [namedAccountGroup] => Territory
                            [parentIndustryGroup] => AEC
                            [parentIndustrySegment] => Architecture Services
                            [primaryAdminFirstName] => Colin
                            [primaryAdminLastName] => Booker
                            [primaryAdminEmail] => <EMAIL>
                            [teamId] => ********
                            [teamName] => Colin Booker - 4843
                            [stateProvinceCode] => 
                            [countryCode] => GB
                        )

                    [purchaser] => Array
                        (
                            [first] => Colin
                            [last] => Booker
                            [email] => <EMAIL>
                            [status] => Active
                            [portalRegistration] => 
                            [doNotCall] => 
                            [doNotEmail] => 
                            [doNotMail] => 
                        )

                )

        )

)

[subscription_update] [2025-06-11 11:39:54] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, New record inserted, <br>
[subscription_update] [2025-06-11 13:38:22] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-11 13:38:22
[subscription_update] [2025-06-11 13:38:22] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-11 13:38:22] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 69e50954-f6c1-4708-b990-ac584da492f3
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 71751250261890
            [status] => Active
            [quantity] => 1
            [endDate] => 2026-06-03
            [autoRenew] => ON
            [term] => Annual
            [message] => subscription status,quantity,endDate,autoRenew,term changed.
            [modifiedAt] => 2025-06-11T13:23:17.000+0000
        )

    [publishedAt] => 2025-06-11T13:38:20.000Z
    [csn] => **********
)

[subscription_update] [2025-06-11 13:38:22] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-11 13:38:22] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-11 13:38:22] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-11 13:38:22] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-11 13:38:22] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-11 13:38:22] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-11 13:38:22] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-11 13:38:22] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-11 13:38:22] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '71751250261890', status = 'Active', quantity = 1, endDate = '2026-06-03', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '71751250261890', status = 'Active', quantity = 1, endDate = '2026-06-03', autoRenew = 'ON', term = 'Annual';\n",
        "affected_rows": 2
    }
]
[subscription_update] [2025-06-11 15:39:14] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-11 15:39:14
[subscription_update] [2025-06-11 15:39:14] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-11 15:39:14] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 97e8da90-9537-4f98-ad09-50d59119ef20
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 71751250261890
            [quantity] => 1
            [message] => subscription quantity changed.
            [modifiedAt] => 2025-06-11T15:09:05.000+0000
        )

    [publishedAt] => 2025-06-11T15:39:11.000Z
    [csn] => **********
)

[subscription_update] [2025-06-11 15:39:14] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-11 15:39:14] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-11 15:39:14] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-11 15:39:14] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-11 15:39:14] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '71751250261890', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '71751250261890', quantity = 1;\n",
        "affected_rows": 0
    }
]
[subscription_update] [2025-06-12 06:09:09] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-12 06:09:09
[subscription_update] [2025-06-12 06:09:09] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-12 06:09:09] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => ceb2d21e-2eaf-4420-b2f7-33568d991b4a
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 74704383629006
            [status] => Expired
            [quantity] => 1
            [message] => subscription status,quantity changed.
            [modifiedAt] => 2025-06-12T05:34:04.000+0000
        )

    [publishedAt] => 2025-06-12T06:09:07.000Z
    [csn] => **********
)

[subscription_update] [2025-06-12 06:09:09] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-12 06:09:09] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-12 06:09:09] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-12 06:09:09] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-12 06:09:09] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-12 06:09:09] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '74704383629006', status = 'Expired', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '74704383629006', status = 'Expired', quantity = 1;\n",
        "affected_rows": 2
    }
]
[subscription_update] [2025-06-12 07:09:50] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-12 07:09:50
[subscription_update] [2025-06-12 07:09:50] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-12 07:09:50] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 60f0f64c-86bc-4ee7-b6fc-e2600d8a07dc
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 71698769308963
            [status] => Active
            [message] => subscription status changed.
            [modifiedAt] => 2025-06-12T06:44:46.000+0000
        )

    [publishedAt] => 2025-06-12T07:09:48.000Z
    [csn] => **********
)

[subscription_update] [2025-06-12 07:09:50] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-12 07:09:50] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-12 07:09:50] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-12 07:09:50] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-12 07:09:50] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '71698769308963', status = 'Active' ON DUPLICATE KEY UPDATE subscriptionId = '71698769308963', status = 'Active';\n",
        "affected_rows": 2
    }
]
[subscription_update] [2025-06-12 12:35:27] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-12 12:35:27
[subscription_update] [2025-06-12 12:35:27] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-12 12:35:27] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => f6ba76fb-d5b7-4ca7-85cb-d3a5e0db985a
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 73322776300658
            [quantity] => 1
            [autoRenew] => OFF
            [message] => subscription quantity,autoRenew changed.
            [modifiedAt] => 2025-06-12T12:20:23.000+0000
        )

    [publishedAt] => 2025-06-12T12:35:25.000Z
    [csn] => **********
)

[subscription_update] [2025-06-12 12:35:27] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-12 12:35:27] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-12 12:35:27] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-12 12:35:27] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-12 12:35:27] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-12 12:35:27] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '73322776300658', quantity = 1, autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '73322776300658', quantity = 1, autoRenew = 'OFF';\n",
        "affected_rows": 2
    }
]
[subscription_update] [2025-06-12 13:37:35] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-12 13:37:35
[subscription_update] [2025-06-12 13:37:35] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-12 13:37:35] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => d950e3cb-22a5-41e4-83a0-b6c6060be8bd
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 65530223063217
            [status] => Active
            [quantity] => 1
            [endDate] => 2026-06-14
            [autoRenew] => ON
            [term] => Annual
            [message] => subscription status,quantity,endDate,autoRenew,term changed.
            [modifiedAt] => 2025-06-12T13:07:29.000+0000
        )

    [publishedAt] => 2025-06-12T13:37:32.000Z
    [csn] => **********
)

[subscription_update] [2025-06-12 13:37:35] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-12 13:37:35] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-12 13:37:35] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-12 13:37:35] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-12 13:37:35] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-12 13:37:35] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-12 13:37:35] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-12 13:37:35] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-12 13:37:35] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '65530223063217', status = 'Active', quantity = 1, endDate = '2026-06-14', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '65530223063217', status = 'Active', quantity = 1, endDate = '2026-06-14', autoRenew = 'ON', term = 'Annual';\n",
        "affected_rows": 2
    }
]
[subscription_update] [2025-06-12 13:40:29] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-12 13:40:29
[subscription_update] [2025-06-12 13:40:29] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-12 13:40:29] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 5a3f7ecc-125c-40ff-b511-ba9e83d1684a
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 74669658062297
            [status] => Active
            [endDate] => 2026-05-20
            [message] => subscription status,endDate changed.
            [modifiedAt] => 2025-06-12T13:15:24.000+0000
        )

    [publishedAt] => 2025-06-12T13:40:26.000Z
    [csn] => **********
)

[subscription_update] [2025-06-12 13:40:29] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-12 13:40:29] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-12 13:40:29] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-12 13:40:29] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-12 13:40:29] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-12 13:40:29] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '74669658062297', status = 'Active', endDate = '2026-05-20' ON DUPLICATE KEY UPDATE subscriptionId = '74669658062297', status = 'Active', endDate = '2026-05-20';\n",
        "affected_rows": 2
    }
]
[subscription_update] [2025-06-12 15:37:52] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-12 15:37:52
[subscription_update] [2025-06-12 15:37:52] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-12 15:37:52] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => decf6673-05c5-44bb-9e66-cdbad3f18e44
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 65530223063217
            [quantity] => 1
            [message] => subscription quantity changed.
            [modifiedAt] => 2025-06-12T15:07:41.000+0000
        )

    [publishedAt] => 2025-06-12T15:37:49.000Z
    [csn] => **********
)

[subscription_update] [2025-06-12 15:37:52] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-12 15:37:52] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-12 15:37:52] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-12 15:37:52] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-12 15:37:52] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '65530223063217', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '65530223063217', quantity = 1;\n",
        "affected_rows": 0
    }
]
[subscription_update] [2025-06-12 15:38:33] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-12 15:38:33
[subscription_update] [2025-06-12 15:38:33] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-12 15:38:33] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 540545c3-0217-4c46-9154-6c4f9936a279
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 74973535400236
            [quantity] => 1
            [message] => subscription quantity changed.
            [modifiedAt] => 2025-06-12T15:12:08.000+0000
        )

    [publishedAt] => 2025-06-12T15:38:31.000Z
    [csn] => **********
)

[subscription_update] [2025-06-12 15:38:33] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-06-12 15:38:36] [autodesk_subscription.class.php:445] Got subscription from api: Array
(
    [0] => Array
        (
            [subscriptionId] => 74973535400236
            [subscriptionReferenceNumber] => 
            [switch] => Array
                (
                    [fromSubscriptions] => Array
                        (
                        )

                    [toSubscription] => 
                )

            [quantity] => 1
            [status] => Active
            [startDate] => 2025-06-12
            [endDate] => 2028-06-11
            [term] => 3 Year
            [billingBehavior] => Recurring
            [billingFrequency] => Annual
            [intendedUsage] => COM
            [connectivity] => Online
            [connectivityInterval] => 30 Days
            [opportunityNumber] => 
            [servicePlan] => Standard
            [accessModel] => Single User
            [paymentMethod] => LOC
            [recordType] => Attribute based
            [renewalCounter] => 
            [autoRenew] => ON
            [offeringId] => OD-000027
            [offeringCode] => ACDIST
            [offeringName] => AutoCAD - including specialized toolsets
            [marketingName] => AutoCAD - including specialized toolsets
            [currency] => GBP
            [annualDeclaredValue] => 
            [pricingMethod] => Array
                (
                    [code] => QTY
                    [description] => Quantity based
                )

            [accounts] => Array
                (
                    [soldTo] => Array
                        (
                            [csn] => **********
                            [name] => NWRC
                        )

                    [solutionProvider] => Array
                        (
                            [csn] => **********
                            [name] => TCS CAD & BIM Solutions Limited
                            [localLanguageName] => 
                            [type] => Reseller
                            [address1] => Unit F, Yorkway
                            [address2] => Mandale Ind Est
                            [address3] => 
                            [city] => Stockton On Tees
                            [stateProvince] => 
                            [postalCode] => TS17 6BX
                            [country] => United Kingdom
                            [stateProvinceCode] => 
                            [countryCode] => GB
                        )

                    [nurtureReseller] => Array
                        (
                            [csn] => 
                            [name] => 
                            [lockDate] => 
                            [nurtureDiscountEligibility] => 
                        )

                )

            [endCustomer] => Array
                (
                    [account] => Array
                        (
                            [csn] => **********
                            [name] => NWRC
                            [type] => End Customer
                            [address1] => Strand Road
                            [address2] => 
                            [address3] => 
                            [city] => Londonderry
                            [stateProvince] => County Londonderry
                            [postalCode] => BT4 8BT
                            [country] => United Kingdom
                            [individualFlag] => 
                            [namedAccountFlag] => 
                            [namedAccountGroup] => Territory
                            [parentIndustryGroup] => EDU
                            [parentIndustrySegment] => Education
                            [primaryAdminFirstName] => Ronan
                            [primaryAdminLastName] => McElduff
                            [primaryAdminEmail] => <EMAIL>
                            [teamId] => 7498857
                            [teamName] => Ronan McElduff - 8857
                            [stateProvinceCode] => 
                            [countryCode] => GB
                        )

                    [purchaser] => Array
                        (
                            [first] => Ronan
                            [last] => McElduff
                            [email] => <EMAIL>
                            [status] => Active
                            [portalRegistration] => Registered
                            [doNotCall] => 
                            [doNotEmail] => 
                            [doNotMail] => 1
                        )

                )

        )

)

[subscription_update] [2025-06-12 15:38:36] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, New record inserted, <br>
[subscription_update] [2025-06-12 15:40:24] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-12 15:40:24
[subscription_update] [2025-06-12 15:40:24] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-12 15:40:24] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => d45f5b0f-55fc-470f-8d56-74f0de5d84d0
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 74669658062297
            [quantity] => 1
            [message] => subscription quantity changed.
            [modifiedAt] => 2025-06-12T15:10:17.000+0000
        )

    [publishedAt] => 2025-06-12T15:40:22.000Z
    [csn] => **********
)

[subscription_update] [2025-06-12 15:40:24] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-12 15:40:24] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-12 15:40:24] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-12 15:40:24] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-12 15:40:24] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '74669658062297', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '74669658062297', quantity = 1;\n",
        "affected_rows": 0
    }
]
[subscription_update] [2025-06-13 06:09:37] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-13 06:09:37
[subscription_update] [2025-06-13 06:09:37] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-13 06:09:37] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => cfd53d2e-be0a-464a-8cd9-4c386e7caf4a
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 74714263863628
            [status] => Expired
            [quantity] => 3
            [message] => subscription status,quantity changed.
            [modifiedAt] => 2025-06-13T05:39:32.000+0000
        )

    [publishedAt] => 2025-06-13T06:09:34.000Z
    [csn] => **********
)

[subscription_update] [2025-06-13 06:09:37] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-13 06:09:37] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-13 06:09:37] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-13 06:09:37] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-13 06:09:37] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-13 06:09:37] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '74714263863628', status = 'Expired', quantity = 3 ON DUPLICATE KEY UPDATE subscriptionId = '74714263863628', status = 'Expired', quantity = 3;\n",
        "affected_rows": 2
    }
]
[subscription_update] [2025-06-13 07:05:54] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-13 07:05:54
[subscription_update] [2025-06-13 07:05:54] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-13 07:05:54] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 2063065d-d61a-4924-b435-98fd3ab4fcdb
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 74704383629006
            [status] => Active
            [message] => subscription status changed.
            [modifiedAt] => 2025-06-13T06:40:50.000+0000
        )

    [publishedAt] => 2025-06-13T07:05:52.000Z
    [csn] => **********
)

[subscription_update] [2025-06-13 07:05:54] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-13 07:05:54] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-13 07:05:54] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-13 07:05:54] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-13 07:05:54] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '74704383629006', status = 'Active' ON DUPLICATE KEY UPDATE subscriptionId = '74704383629006', status = 'Active';\n",
        "affected_rows": 2
    }
]
[subscription_update] [2025-06-13 07:08:26] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-13 07:08:26
[subscription_update] [2025-06-13 07:08:26] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-13 07:08:26] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 5e3d67ed-79ac-4e76-90be-429e38eb00df
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 71403297452591
            [quantity] => 1
            [message] => subscription quantity changed.
            [modifiedAt] => 2025-06-13T06:53:22.000+0000
        )

    [publishedAt] => 2025-06-13T07:08:24.000Z
    [csn] => **********
)

[subscription_update] [2025-06-13 07:08:26] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-13 07:08:26] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-13 07:08:26] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-13 07:08:26] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-13 07:08:26] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '71403297452591', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '71403297452591', quantity = 1;\n",
        "affected_rows": 0
    }
]
[subscription_update] [2025-06-13 10:08:28] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-13 10:08:28
[subscription_update] [2025-06-13 10:08:28] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-13 10:08:28] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => c5d7ae2d-b74a-4e57-b5ce-1dd986fdd757
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 57615947812131
            [status] => Active
            [quantity] => 3
            [endDate] => 2026-07-17
            [autoRenew] => ON
            [term] => Annual
            [message] => subscription status,quantity,endDate,autoRenew,term changed.
            [modifiedAt] => 2025-06-13T09:38:22.000+0000
        )

    [publishedAt] => 2025-06-13T10:08:26.000Z
    [csn] => **********
)

[subscription_update] [2025-06-13 10:08:28] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-13 10:08:28] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-13 10:08:28] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-13 10:08:28] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-13 10:08:28] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-13 10:08:28] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-13 10:08:28] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-13 10:08:28] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-13 10:08:28] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '57615947812131', status = 'Active', quantity = 3, endDate = '2026-07-17', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '57615947812131', status = 'Active', quantity = 3, endDate = '2026-07-17', autoRenew = 'ON', term = 'Annual';\n",
        "affected_rows": 2
    }
]
[subscription_update] [2025-06-13 11:36:50] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-13 11:36:50
[subscription_update] [2025-06-13 11:36:50] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-13 11:36:50] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 2f2c81e8-e4b7-45ea-8548-93cc8bbc2ee9
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 57615947812131
            [quantity] => 3
            [message] => subscription quantity changed.
            [modifiedAt] => 2025-06-13T11:16:38.000+0000
        )

    [publishedAt] => 2025-06-13T11:36:48.000Z
    [csn] => **********
)

[subscription_update] [2025-06-13 11:36:50] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-13 11:36:50] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-13 11:36:50] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-13 11:36:50] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-13 11:36:51] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '57615947812131', quantity = 3 ON DUPLICATE KEY UPDATE subscriptionId = '57615947812131', quantity = 3;\n",
        "affected_rows": 0
    }
]
[subscription_update] [2025-06-13 17:11:50] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-13 17:11:50
[subscription_update] [2025-06-13 17:11:50] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-13 17:11:50] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 75c59e3d-502d-4dcf-894d-4c95d9c010a3
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 59178204432920
            [status] => Active
            [quantity] => 1
            [endDate] => 2026-06-09
            [autoRenew] => ON
            [term] => Annual
            [message] => subscription status,quantity,endDate,autoRenew,term changed.
            [modifiedAt] => 2025-06-13T16:36:28.000+0000
        )

    [publishedAt] => 2025-06-13T17:11:47.000Z
    [csn] => **********
)

[subscription_update] [2025-06-13 17:11:50] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-13 17:11:50] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-13 17:11:50] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-13 17:11:50] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-13 17:11:50] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-13 17:11:50] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-13 17:11:50] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-13 17:11:50] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-13 17:11:50] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '59178204432920', status = 'Active', quantity = 1, endDate = '2026-06-09', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '59178204432920', status = 'Active', quantity = 1, endDate = '2026-06-09', autoRenew = 'ON', term = 'Annual';\n",
        "affected_rows": 2
    }
]
[subscription_update] [2025-06-13 19:36:38] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-13 19:36:38
[subscription_update] [2025-06-13 19:36:38] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-13 19:36:38] [autodesk_subscription.class.php:424] Incoming payload: Array
(
    [id] => 8fe3f329-3705-4aa3-8c53-1066b2cbd965
    [topic] => subscription-change
    [event] => changed
    [sender] => PWS Subscription Service
    [environment] => prd
    [payload] => Array
        (
            [subscriptionId] => 59178204432920
            [quantity] => 1
            [message] => subscription quantity changed.
            [modifiedAt] => 2025-06-13T19:11:54.000+0000
        )

    [publishedAt] => 2025-06-13T19:36:34.000Z
    [csn] => **********
)

[subscription_update] [2025-06-13 19:36:38] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-13 19:36:38] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-13 19:36:38] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-13 19:36:38] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-13 19:36:38] [autodesk_subscription.class.php:520] [
    {
        "status": "success",
        "message": "autodesk_subscriptions updated successfull",
        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '59178204432920', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '59178204432920', quantity = 1;\n",
        "affected_rows": 0
    }
]
