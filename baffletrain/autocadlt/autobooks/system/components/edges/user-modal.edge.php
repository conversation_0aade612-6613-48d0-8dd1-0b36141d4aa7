@props([
    'user' => null,
    'roles' => ['user' => 'User', 'admin' => 'Admin', 'dev' => 'Developer'],
    'statuses' => ['active' => 'Active', 'inactive' => 'Inactive'],
])

<div class="space-y-4 p-5">
    <div class="border-b border-gray-200 pb-4">
        <h3 class="text-lg font-medium leading-6 text-gray-900">
            {{ $user ? 'Edit User' : 'Create New User' }}
        </h3>
    </div>

    <form hx-post="api/system/save_user" 
          hx-target="#users_table" 
          hx-swap="innerHTML"
          @submit="showModal = false"
          class="space-y-4">
        
        <input type="hidden" name="id" value="{{ $user ? $user['id'] : '' }}">
        
        <div>
            <x-forms-input name="name" label="Name" :value="$user ? $user['name'] : ''" {{ $user ? 'readonly' : 'required' }} />
        </div>
        
        <div>
            <x-forms-input name="email" label="Email" :value="$user ? $user['email'] : ''" required />

        </div>
        <div>
           <x-forms-select name="role" label="Role" :options="$roles" :selected="{{ $user ? $user['role'] : null }}" />
          </div>

        <div>
           <x-forms-select name="status" label="Status" :options="$statuses" :selected="{{ $user ? $user['status'] : null }}" />

       </div>
        
        @if(!$user)
        <div>
            <x-forms-input name="password" label="Password" required />
        </div>
        @endif

        <div class="flex justify-end gap-x-3 pt-4">
            <x-forms-button label="Cancel" variant="secondary" @click="showModal = false" />
            <x-forms-button type="submit" :label="$user ? 'Update' : 'Create'" variant="primary" />
        </div>
    </form>
</div>

<div id="password_reset_message" 
     class="fixed top-4 right-4" 
     x-data="{ show: false, message: '' }" 
     @password-reset.window="show = true; message = $event.detail; setTimeout(() => show = false, 3000)"
     x-show="show"
     x-transition>
    <div class="bg-green-50 p-4 rounded-md">
        <p class="text-green-800" x-text="message"></p>
    </div>
</div>