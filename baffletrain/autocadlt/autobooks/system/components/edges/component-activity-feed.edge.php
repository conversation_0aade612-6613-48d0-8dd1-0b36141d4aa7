{{-- resources/views/components/activity-feed.blade.php --}}
@props([
    'title' => 'activity_feed',
    'description' => 'displays a list of stuff',
    'activity' => [],
    'currentUser' => ''
    ])
@php
    print_rr($activity,'activityey',true,true);
@endphp
<ul role="list" class="space-y-6">
    @foreach ($activity as $index => $activityItem)
        <ul role="list" class="space-y-6">
            <li class="relative flex gap-x-4">
                <div class="absolute -bottom-6 left-0 top-0 flex w-6 justify-center">
                    <div class="w-px bg-gray-200"></div>
                </div>

                <div class="relative flex size-6 flex-none items-center justify-center bg-white text-gray-300">

                        @if ($activityItem['hist_media'] == 'E-Mail')
                            @icon_micro('envelope')
                        @elseif ($activityItem['hist_media'] == 'phone')
                            @icon_micro('phone')
                        @elseif ($activityItem['hist_media'] == 'In-Person')
                            @icon_micro('user')
                        @elseif ($activityItem['hist_media'] == 'Letter')
                            @icon_micro('inbox_icon_down')
                        @else
                        <div class="size-1.5 rounded-full bg-gray-100 ring-1 ring-gray-300 text-gray-300  items-center"></div>
                        @endif

                </div>
                <p class="flex-auto py-0.5 text-xs/5 text-gray-500"><span class="mr-2 font-medium text-gray-900">{{ $activityItem['users_name'] }}</span>{{ $activityItem['hist_message'] }}</p>
                <time datetime="{{ $activityItem['hist_date'] }}" class="flex-none py-0.5 text-xs/5 text-gray-500">{{ $activityItem['hist_date'] }}</time>
            </li>

        </ul>
    @endforeach
</ul>
