@props([
    'parent_path' => '',
    'icons' => []
])
@php
// Initialize hilt templates array - only show hilt templates
$templates = [
    'default_template' => [
        'filename' => 'default_template.hilt.php',
        'id' => 'default_template',
        'name' => 'Basic Template',
        'description' => 'A basic hilt template for general content',
        'icon' => 'document-text',
        'type' => 'default'
    ],
    'data_table_template' => [
        'filename' => 'data_table_template.hilt.php',
        'id' => 'data_table_template',
        'name' => 'Database Table',
        'description' => 'Database-driven data table with CSV import',
        'icon' => 'table-cells',
        'type' => 'csv'
    ],
    'custom_html_template' => [
        'filename' => 'custom_html_template.hilt.php',
        'id' => 'custom_html_template',
        'name' => 'Custom HTML',
        'description' => 'Custom HTML content template',
        'icon' => 'code-bracket',
        'type' => 'custom_html'
    ],
    'file_upload_template' => [
        'filename' => 'file_upload_template.hilt.php',
        'id' => 'file_upload_template',
        'name' => 'File Upload',
        'description' => 'Template based on uploaded file content',
        'icon' => 'arrow-up-tray',
        'type' => 'file_upload'
    ]
];

// Get all hilt template files from the templates directory
$template_dir = FS_SYS_TEMPLATES;
$hilt_files = glob($template_dir . '*.hilt.php');

// Process each hilt template file
foreach ($hilt_files as $file) {
    $filename = basename($file);
    $template_key = str_replace('.hilt.php', '', $filename);

    // Skip if already defined above
    if (isset($templates[$template_key])) {
        continue;
    }

    $template_id = pathinfo($filename, PATHINFO_FILENAME);
    $display_name = ucwords(str_replace(['-', '_'], ' ', $template_id));

    // Read the file to extract props information
    $content = file_get_contents($file);
    $title = $display_name;
    $type = 'default';
    $description = 'Custom hilt template';

    // Extract props from the file
    if (preg_match("/'name' => '([^']+)'/", $content, $matches)) {
        $title = $matches[1];
    }

    if (preg_match("/'type' => 'hilt-([^']+)'/", $content, $matches)) {
        $type = $matches[1];
    } elseif (preg_match("/'type' => '([^']+)'/", $content, $matches)) {
        $type = $matches[1];
    }

    if (preg_match("/'description' => '([^']+)'/", $content, $matches)) {
        $description = $matches[1];
    }

    $templates[$template_key] = [
        'filename' => $filename,
        'id' => $template_id,
        'name' => $title,
        'description' => $description,
        'path' => $file,
        'icon' => 'document-text',
        'type' => $type
    ];
}

@endphp
<div class="p-6">
    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Add Navigation Entry</h3>
    <form
        hx-post="{{ APP_ROOT }}api/system/nav_tree/save_nav_entry"
        hx-target="#content_wrapper"
        hx-swap="innerHTML"
        @submit="$dispatch('hide-modal')"
        class="space-y-4"
        x-data="{ selectedTemplate: 'default' }"
        enctype="multipart/form-data"
    >
        <input type="hidden" name="parent_path" value="{{ $parent_path }}">
        <input type="hidden" name="template_type" x-bind:value="selectedTemplate">
        
        <div>
            <x-forms-input 
                name="key" 
                label="Route Key" 
                placeholder="e.g., dashboard" 
                required
            />
        </div>
        
        <div>
            <x-forms-input 
                name="name" 
                label="Display Name" 
                placeholder="e.g., Dashboard" 
                required
            />
        </div>
        
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Icon</label>
            <x-forms-select-with-icons name="icon" :options="array_keys(ICONS)"></x-forms-select-with-icons>
        </div>
        
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Hilt Template</label>
            <select
                name="template"
                @change="selectedTemplate = $event.target.options[$event.target.selectedIndex].dataset.type"
                class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6"
            >
                @foreach($templates as $key => $template)
                    @php
                    $isDefault = $key === 'default_template';
                    @endphp
                    <option value="{{ $key }}"
                            data-type="{{ $template['type'] }}"
                            @if($isDefault) selected @endif>
                        {{ $template['name'] }}
                        @if(!empty($template['description']))
                            - {{ $template['description'] }}
                        @endif
                    </option>
                @endforeach
            </select>
            <p class="mt-1 text-sm text-gray-500">All templates use the Hilt system for enhanced functionality</p>
        </div>
        
        <!-- Hilt Template-specific fields -->
        <div x-show="selectedTemplate === 'csv'" class="space-y-3">
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 class="text-sm font-medium text-blue-800 mb-2">Database Table Template</h4>
                <p class="text-sm text-blue-700 mb-3">This template creates a database table and imports CSV data automatically.</p>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">CSV Data (Optional)</label>
                    <textarea
                        name="template_data[csv_data]"
                        rows="4"
                        placeholder="Paste CSV data here (optional - you can also upload data later)..."
                        class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6"
                    ></textarea>
                </div>
                <div class="mt-3">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Or Upload CSV File</label>
                    <input
                        type="file"
                        name="csv_file"
                        accept=".csv,.txt"
                        class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100"
                    />
                </div>
                <p class="text-xs text-blue-600 mt-2">💡 You can manage data later through the settings page</p>
            </div>
        </div>

        <div x-show="selectedTemplate === 'custom_html'" class="space-y-3">
            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                <h4 class="text-sm font-medium text-green-800 mb-2">Custom HTML Template</h4>
                <p class="text-sm text-green-700 mb-3">Enter custom HTML content that will be embedded in the template.</p>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Custom HTML</label>
                    <textarea
                        name="template_data[html]"
                        rows="6"
                        placeholder="Enter custom HTML here..."
                        class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6"
                    ></textarea>
                </div>
                <p class="text-xs text-green-600 mt-2">💡 You can use full HTML including CSS and JavaScript</p>
            </div>
        </div>

        <div x-show="selectedTemplate === 'file_upload'" class="space-y-3">
            <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                <h4 class="text-sm font-medium text-purple-800 mb-2">File Upload Template</h4>
                <p class="text-sm text-purple-700 mb-3">Upload a file that will be used as the template content.</p>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Upload Template File</label>
                    <input
                        type="file"
                        name="template_file"
                        accept=".html,.php,.txt"
                        class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100"
                    />
                </div>
                <p class="text-xs text-purple-600 mt-2">💡 Supports HTML, PHP, and text files</p>
            </div>
        </div>

        <div x-show="selectedTemplate === 'default'" class="space-y-3">
            <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <h4 class="text-sm font-medium text-gray-800 mb-2">Basic Template</h4>
                <p class="text-sm text-gray-700">A basic template that provides a starting point for custom content. No additional configuration needed.</p>
                <p class="text-xs text-gray-600 mt-2">💡 You can edit the template file later to customize the content</p>
            </div>
        </div>
        
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Required Roles</label>
            <div class="space-y-2">
                @foreach(['user', 'admin', 'dev'] as $role)
                    <div class="flex items-center">
                        <input type="checkbox" name="required_roles[]" value="{{ $role }}" id="role_{{ $role }}" class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                        <label for="role_{{ $role }}" class="ml-2 block text-sm text-gray-900">{{ ucfirst($role) }}</label>
                    </div>
                @endforeach
            </div>
        </div>
        
        <div class="flex justify-end space-x-3 pt-4">
            <button 
                type="button" 
                @click="$dispatch('hide-modal')"
                class="rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
            >
                Cancel
            </button>
            <button 
                type="submit"
                class="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
            >
                Save
            </button>
        </div>
    </form>
</div>