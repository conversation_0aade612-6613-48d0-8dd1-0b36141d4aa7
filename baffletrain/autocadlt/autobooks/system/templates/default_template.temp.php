Doing preparation checks...
Required pre-conversion condition 'checking if all packages are up to date' not                                                                                                                       met:
        There are packages which are not up to date. Call `yum update -y && rebo                                                                                                                      ot` to update the packages.

Required pre-conversion condition 'checking if PermitRootLogin is configured in                                                                                                                       sshd_config' not met:
        The PermitRootLogin setting is missing in the /etc/ssh/sshd_config file.
        By default, this will be set to 'prohibit-password' on the new system, w                                                                                                                      hich may prevent SSH connection.
        To proceed with the conversion, you need to set PermitRootLogin explicit                                                                                                                      ly in /etc/ssh/sshd_config.
        - If you use password authentication, add "PermitRootLogin yes" to the f                                                                                                                      ile.
        - If you use key-based authentication, add "PermitRootLogin prohibit-pas                                                                                                                      sword" to the file.

Conversion can't be performed due to the problems noted above
