
<?php
namespace api\settings;
use autodesk_api\autodesk_api;


include_once 'resources/functions/func_email_settings.php';
//print_rr($input_params, 'input_params');
switch ($input_params['action']){
    case 'save_email_template':   save_email_template($input_params['email_from'],$input_params['email_subject'],$input_params['email_template']); break;
    case 'email_rule_delete':     email_rule_delete($input_params['rule']); break;    
    case 'email_rule_add':        email_rule_add($input_params['new_rule']); break;
    case 'emails_send_reminders': emails_send_reminders(); break;
    case 'send_test_email':       send_test_email($input_params['test_email_to']); break;
    case 'update_days_to_send':   update_days_to_send($input_params['days_to_send']); break;
    case 'update_time_to_send':   update_time_to_send($input_params['time_to_send']); break;
}

function save_email_template($from, $subject, $email_template) {
    $email_template = $from . PHP_EOL . $subject . PHP_EOL . $email_template;

    // Attempt to save the file
    $email_template_save = file_put_contents(SOURCE_FS_PATH . 'reminder_email.view.php', $email_template);

    // Check if the operation was successful
    if ($email_template_save === false) {
        // Get the last PHP error
        $last_error = error_get_last();
        
        // If there's an error, it will be in the array returned by error_get_last
        echo 'Error: Failed to save the email template. ';
        
        // Optionally display more detailed error information
        if ($last_error) {
            echo 'Error Message: ' . $last_error['message'];
            echo ' in File: ' . $last_error['file'];
            echo ' on Line: ' . $last_error['line'];
        }
    } else {
        // Success message
        echo 'Saved';
    }
}

function email_rule_delete(string $rule) {
    print_rr($rule, 'rule');
    $email_rules = autodesk_api::database_get_storage('subscription_renew_email_send_rules');
    $email_rules_array = explode(',',$email_rules);
    foreach ($email_rules_array as $key => $value) {
        if ($value == $rule) {
            unset($email_rules_array[$key]);
        }
    }  
    asort($email_rules_array);
    $email_rules = implode(',', $email_rules_array);
    print_rr($email_rules, 'email_rules after processing');   
    autodesk_api::database_set_storage('subscription_renew_email_send_rules',$email_rules);
    print_rr($email_rules_array);
    echo tcs_draw_email_send_rules_widget_buttons($email_rules_array);
}
function email_rule_add($new_rule) {
    $email_rules = autodesk_api::database_get_storage('subscription_renew_email_send_rules');
    $rules_array = explode(',', $email_rules);
    $rules_array[] = $new_rule;
    asort($rules_array, SORT_NUMERIC);
    $email_rules = implode(',', $rules_array);
    $email_rules = trim(str_replace(",,", "", $email_rules), ',');
    autodesk_api::database_set_storage('subscription_renew_email_send_rules',$email_rules);
    echo tcs_draw_email_send_rules_widget_buttons($rules_array);
}

function send_test_email($test_email_to) {
   print_rr("sending email to " . $test_email_to,null,true,true);
    $api = new autodesk_api(); 
    $sub = [
        'status' => 'active',
        'endCustomerName' => 'Terry Mahmood',
        'product_name' => 'Autodesk Revit',
        'term' => 'Annual',
        'seats' => '1',
        'subscriptionReferenceNumber' => '123-07851167',
        'opportunityNumber' => '099986545'
    ];
    echo $api->subscriptions->send_reminder_email($sub,$test_email_to);
}

function update_days_to_send($days) {
    autodesk_api::database_set_storage('subscription_renew_email_send_days', $days);
}
function update_time_to_send($time) {
    autodesk_api::database_set_storage('subscription_renew_email_send_time', $time);
}

?>