<?php
namespace system\notifications;

use Users\user;

class Notification {
    private $db;
    private $user_id;

    public function __construct() {
        global $db;
        $this->db = $db;
        $this->user_id = user::checkAuth()['id'] ?? null;
    }

    /**
     * Create a new notification for a user
     *
     * @param int $user_id User ID to send notification to
     * @param string $type Notification type
     * @param string $title Notification title
     * @param string $message Notification message
     * @param string|null $link Optional link to include with notification
     * @return int|bool The notification ID if successful, false otherwise
     */
    public function create($user_id, $type, $title, $message, $link = null) {
        $query = "INSERT INTO autobooks_notifications
                 (user_id, type, title, message, link)
                 VALUES (:user_id, :type, :title, :message, :link)";

        $params = [
            ':user_id' => $user_id,
            ':type' => $type,
            ':title' => $title,
            ':message' => $message,
            ':link' => $link
        ];

        $result = tep_db_query($query, null, $params);

        if ($result) {
            return tep_db_insert_id();
        }

        return false;
    }

    /**
     * Get notifications for the current user
     *
     * @param bool $unread_only Whether to get only unread notifications
     * @param int $limit Maximum number of notifications to return
     * @param int $offset Offset for pagination
     * @return array Notifications
     */
    public function getForCurrentUser($unread_only = false, $limit = 10, $offset = 0) {
        if (!$this->user_id) {
            return [];
        }

        $query = "SELECT * FROM autobooks_notifications
                 WHERE user_id = :user_id";

        if ($unread_only) {
            $query .= " AND is_read = 0";
        }

        $query .= " ORDER BY created_at DESC LIMIT :limit OFFSET :offset";

        $params = [
            ':user_id' => $this->user_id,
            ':limit' => $limit,
            ':offset' => $offset
        ];

        $result = tep_db_query($query, null, $params);

        $notifications = [];
        while ($row = tep_db_fetch_array($result)) {
            $notifications[] = $row;
        }

        return $notifications;
    }

    /**
     * Get the count of unread notifications for the current user
     *
     * @return int Count of unread notifications
     */
    public function getUnreadCount() {
        if (!$this->user_id) {
            return 0;
        }

        $query = "SELECT COUNT(*) as count FROM autobooks_notifications
                 WHERE user_id = :user_id AND is_read = 0";

        $params = [':user_id' => $this->user_id];

        $result = tep_db_query($query, null, $params);

        if ($row = tep_db_fetch_array($result)) {
            return (int)$row['count'];
        }

        return 0;
    }

    /**
     * Mark a notification as read
     *
     * @param int $notification_id Notification ID
     * @return bool Whether the operation was successful
     */
    public function markAsRead($notification_id) {
        if (!$this->user_id) {
            return false;
        }

        $query = "UPDATE autobooks_notifications
                 SET is_read = 1, read_at = NOW()
                 WHERE id = :notification_id AND user_id = :user_id";

        $params = [
            ':notification_id' => $notification_id,
            ':user_id' => $this->user_id
        ];

        $result = tep_db_query($query, null, $params);

        return $result !== false;
    }

    /**
     * Mark all notifications as read for the current user
     *
     * @return bool Whether the operation was successful
     */
    public function markAllAsRead() {
        if (!$this->user_id) {
            return false;
        }

        $query = "UPDATE autobooks_notifications
                 SET is_read = 1, read_at = NOW()
                 WHERE user_id = :user_id AND is_read = 0";

        $params = [':user_id' => $this->user_id];

        $result = tep_db_query($query, null, $params);

        return $result !== false;
    }

    /**
     * Get notification preferences for the current user
     *
     * @return array Notification preferences
     */
    public function getPreferences() {
        if (!$this->user_id) {
            return [];
        }

        $query = "SELECT * FROM autobooks_notification_preferences
                 WHERE user_id = :user_id";

        $params = [':user_id' => $this->user_id];

        $result = tep_db_query($query, null, $params);

        $preferences = [];
        while ($row = tep_db_fetch_array($result)) {
            $preferences[$row['type']] = (bool)$row['enabled'];
        }

        return $preferences;
    }

    /**
     * Update notification preferences for the current user
     *
     * @param string $type Notification type
     * @param bool $enabled Whether notifications of this type are enabled
     * @return bool Whether the operation was successful
     */
    public function updatePreference($type, $enabled) {
        if (!$this->user_id) {
            return false;
        }

        $query = "INSERT INTO autobooks_notification_preferences
                 (user_id, type, enabled)
                 VALUES (:user_id, :type, :enabled)
                 ON DUPLICATE KEY UPDATE enabled = :enabled";

        $params = [
            ':user_id' => $this->user_id,
            ':type' => $type,
            ':enabled' => $enabled ? 1 : 0
        ];

        $result = tep_db_query($query, null, $params);

        return $result !== false;
    }
}
