<?php
/**
 * <PERSON><PERSON><PERSON> to check and fix logs directory permissions
 */

$logs_dir = __DIR__ . '/logs';
$test_file = $logs_dir . '/permission_test.log';

echo "Checking logs directory permissions...\n";
echo "Logs directory: $logs_dir\n";

// Check if directory exists
if (!is_dir($logs_dir)) {
    echo "ERROR: Logs directory does not exist!\n";
    exit(1);
}

// Check if directory is readable
if (!is_readable($logs_dir)) {
    echo "ERROR: Logs directory is not readable!\n";
    exit(1);
}

// Check if directory is writable
if (!is_writable($logs_dir)) {
    echo "ERROR: Logs directory is not writable!\n";
    echo "Current permissions: " . substr(sprintf('%o', fileperms($logs_dir)), -4) . "\n";
    echo "Owner: " . posix_getpwuid(fileowner($logs_dir))['name'] . "\n";
    echo "Group: " . posix_getgrgid(filegroup($logs_dir))['name'] . "\n";
    echo "Current user: " . get_current_user() . "\n";
    echo "Web server user: " . $_SERVER['USER'] ?? 'unknown' . "\n";
    
    // Try to fix permissions
    echo "Attempting to fix permissions...\n";
    if (chmod($logs_dir, 0755)) {
        echo "Successfully changed directory permissions to 755\n";
    } else {
        echo "Failed to change directory permissions\n";
    }
    
    // Check again
    if (!is_writable($logs_dir)) {
        echo "Directory is still not writable after permission change\n";
        exit(1);
    }
}

// Test file creation
echo "Testing file creation...\n";
$test_content = "Test log entry: " . date('Y-m-d H:i:s') . "\n";
$result = file_put_contents($test_file, $test_content);

if ($result === false) {
    echo "ERROR: Failed to create test file!\n";
    exit(1);
} else {
    echo "SUCCESS: Test file created successfully\n";
    echo "Written $result bytes to $test_file\n";
    
    // Clean up test file
    if (unlink($test_file)) {
        echo "Test file cleaned up successfully\n";
    } else {
        echo "Warning: Could not clean up test file\n";
    }
}

echo "Logs directory permissions check completed successfully!\n";
?>
