<?php
/**
 * Test file to demonstrate the new dependency issues with info buttons
 * 
 * This file shows how the enhanced dependency checking works with detailed
 * explanations and resolution steps for each issue type.
 */

require_once("includes/classes/attributes.class.php");
require_once("includes/functions/tcs_attributes_components.php");

// Get product ID from URL parameter
$product_id = $_GET['product_id'] ?? 1;

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dependency Issues with Info Buttons - Product <?php echo $product_id; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">
    
    <!-- Alpine.js (basic version without plugins) -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Additional Alpine.js configuration -->
    <script>
        // Ensure Alpine.js is properly initialized
        document.addEventListener('alpine:init', () => {
            console.log('Alpine.js initialized successfully');
        });
    </script>
    
    <style>
        body {
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .page-header {
            border-bottom: 2px solid #e5e5e5;
            margin-bottom: 30px;
            padding-bottom: 15px;
        }
        .test-info {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .test-links {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e5e5e5;
        }
        .test-links a {
            margin-right: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="page-header">
            <h1>
                <i class="fa fa-cogs"></i>
                Dependency Issues with Info Buttons
                <small>Product ID: <?php echo $product_id; ?></small>
            </h1>
        </div>
        
        <div class="test-info">
            <h4><i class="fa fa-info-circle text-info"></i> About this test</h4>
            <p>
                This page demonstrates the enhanced dependency issue checking system with detailed explanations.
                Each dependency issue now includes an info button (ℹ️) that provides:
            </p>
            <ul>
                <li><strong>Detailed explanation</strong> of what the issue means</li>
                <li><strong>Step-by-step resolution</strong> instructions</li>
                <li><strong>Visual indicators</strong> for different issue types</li>
                <li><strong>Interactive modals</strong> with Alpine.js transitions</li>
            </ul>
        </div>

        <?php
        try {
            // Create an instance of the attributes class
            $products_attributes = new tcs_product_attributes($product_id);
            
            // Get and display dependency warnings with info buttons
            echo tcs_draw_attributes_dependency_warnings($products_attributes);
            
            // Also show raw issues for debugging
            $issues = $products_attributes->check_dependency_issues();
            
            if (!empty($issues)) {
                echo '<div class="panel panel-info" style="margin-top: 20px;">';
                echo '<div class="panel-header"><div class="panel-body">Raw Issues Data (for debugging)</div></div>';
                echo '<div class="panel-body">';
                echo '<pre style="background: #f8f9fa; padding: 15px; border-radius: 4px; font-size: 12px;">';
                echo htmlspecialchars(print_r($issues, true));
                echo '</pre>';
                echo '</div>';
                echo '</div>';
            } else {
                echo '<div class="alert alert-success">';
                echo '<h4><i class="fa fa-check-circle"></i> No Issues Found!</h4>';
                echo '<p>Product ' . $product_id . ' has no dependency issues. All attributes and variations are properly configured.</p>';
                echo '</div>';
            }
            
        } catch (Exception $e) {
            echo '<div class="alert alert-danger">';
            echo '<h4><i class="fa fa-exclamation-triangle"></i> Error</h4>';
            echo '<p>An error occurred while checking dependencies: ' . htmlspecialchars($e->getMessage()) . '</p>';
            echo '</div>';
        }
        ?>
        
        <div class="test-links">
            <h4>Test with different products:</h4>
            <a href="?product_id=1" class="btn btn-default">Product 1</a>
            <a href="?product_id=2" class="btn btn-default">Product 2</a>
            <a href="?product_id=3" class="btn btn-default">Product 3</a>
            <a href="?product_id=4" class="btn btn-default">Product 4</a>
            <a href="?product_id=5" class="btn btn-default">Product 5</a>
        </div>
        
        <div class="panel panel-default" style="margin-top: 30px;">
            <div class="panel-header">
                <div class="panel-body">Issue Types Supported</div>
            </div>
            <div class="panel-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>Attribute Issues:</h5>
                        <ul>
                            <li>🔗 Missing Dependencies</li>
                            <li>🔄 Circular Dependencies</li>
                            <li>📊 Sort Order Violations</li>
                            <li>🎯 Missing Variations</li>
                            <li>⚠️ Default Conflicts</li>
                            <li>🚫 Self Option Group Dependencies</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>Variation Issues:</h5>
                        <ul>
                            <li>❌ Missing Attributes</li>
                            <li>📋 Duplicate Model Numbers</li>
                            <li>🔗 Missing Autodesk Links</li>
                            <li>❌ Invalid Autodesk Links</li>
                            <li>⚡ Selection Issues</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
