<?php
/**
 * PHP Configuration Check for Local Development
 * This script checks if your PHP environment is properly configured
 * for running the application locally.
 */

echo "<h1>PHP Configuration Check</h1>";
echo "<h2>Environment Information</h2>";

// Basic PHP Information
echo "<h3>PHP Version</h3>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Server API: " . php_sapi_name() . "<br>";
echo "System: " . php_uname() . "<br>";

// Check required extensions
echo "<h3>Required Extensions</h3>";
$required_extensions = [
    'mysqli',
    'pdo',
    'pdo_mysql',
    'curl',
    'gd',
    'mbstring',
    'openssl',
    'zip',
    'xml',
    'json'
];

foreach ($required_extensions as $ext) {
    $status = extension_loaded($ext) ? "✓ Loaded" : "✗ Missing";
    $color = extension_loaded($ext) ? "green" : "red";
    echo "<span style='color: $color'>$ext: $status</span><br>";
}

// Check important PHP settings
echo "<h3>Important PHP Settings</h3>";
$settings = [
    'display_errors' => ini_get('display_errors'),
    'error_reporting' => ini_get('error_reporting'),
    'max_execution_time' => ini_get('max_execution_time'),
    'memory_limit' => ini_get('memory_limit'),
    'upload_max_filesize' => ini_get('upload_max_filesize'),
    'post_max_size' => ini_get('post_max_size'),
    'session.save_path' => ini_get('session.save_path'),
    'date.timezone' => ini_get('date.timezone')
];

foreach ($settings as $setting => $value) {
    echo "$setting: " . ($value ?: 'Not set') . "<br>";
}

// Test database connection
echo "<h3>Database Connection Test</h3>";
try {
    // Try to connect using the local configuration
    $host = '127.0.0.1';
    $username = 'root';
    $password = '';
    $database = 'cadservices_local';
    
    $pdo = new PDO("mysql:host=$host", $username, $password);
    echo "<span style='color: green'>✓ MySQL connection successful</span><br>";
    
    // Check if database exists
    $stmt = $pdo->query("SHOW DATABASES LIKE '$database'");
    if ($stmt->rowCount() > 0) {
        echo "<span style='color: green'>✓ Database '$database' exists</span><br>";
    } else {
        echo "<span style='color: orange'>⚠ Database '$database' does not exist - you need to create it</span><br>";
    }
    
} catch (PDOException $e) {
    echo "<span style='color: red'>✗ Database connection failed: " . $e->getMessage() . "</span><br>";
}

// Check file permissions
echo "<h3>File System Checks</h3>";
$paths_to_check = [
    __DIR__ . '/temp',
    __DIR__ . '/download',
    __DIR__ . '/pub',
    __DIR__ . '/includes'
];

foreach ($paths_to_check as $path) {
    if (is_dir($path)) {
        $writable = is_writable($path) ? "✓ Writable" : "✗ Not writable";
        $color = is_writable($path) ? "green" : "red";
        echo "<span style='color: $color'>$path: $writable</span><br>";
    } else {
        echo "<span style='color: orange'>$path: Directory does not exist</span><br>";
    }
}

// Check Composer dependencies
echo "<h3>Composer Dependencies</h3>";
if (file_exists(__DIR__ . '/vendor/autoload.php')) {
    echo "<span style='color: green'>✓ Composer dependencies installed</span><br>";
    require_once __DIR__ . '/vendor/autoload.php';
    
    // Check specific packages
    $packages = ['guzzlehttp/guzzle', 'codex-team/editor.js'];
    foreach ($packages as $package) {
        // This is a simple check - in a real scenario you'd check composer.lock
        echo "Package $package: Checking...<br>";
    }
} else {
    echo "<span style='color: red'>✗ Composer dependencies not installed</span><br>";
    echo "Run 'composer install' in your project directory<br>";
}

echo "<h3>Recommendations</h3>";
echo "<ul>";
echo "<li>For development, set display_errors = On</li>";
echo "<li>Ensure all required extensions are loaded</li>";
echo "<li>Create the database 'cadservices_local' if it doesn't exist</li>";
echo "<li>Make sure temp, download, and pub directories are writable</li>";
echo "<li>Install Composer dependencies if not already done</li>";
echo "</ul>";

echo "<hr>";
echo "<p><strong>Current time:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<p><strong>Document root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p><strong>Script path:</strong> " . __FILE__ . "</p>";
?>
