<?php
/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2010 osCommerce

  Released under the GNU General Public License
*/

require('includes/application_top.php');

require('includes/languages/' . $language . '/privacy.php');

$breadcrumb->add(NAVBAR_TITLE, tep_href_link('privacy.php'));

require('includes/template_top.php');
?>

<div class="page-header">
  <h1 class="h3">Unsubscribe from subscription notifications</h1>
</div>


<div class="contentContainer">
  <div class="contentText">
    <?php $ref_num = $_GET['ref_num'];
    if ($ref_num) {
      if (!preg_match("/[0-9][0-9][0-9]-[0-9]+/", $ref_num)) echo "$ref_num is not a valid reference number";
    }
    if ($_GET['unsubscribe'] == '1') {

      $query = "UPDATE `autodesk_subscriptions` SET `tcs_unsubscribe`=1 WHERE `subscriptionReferenceNumber` = '{$ref_num}'";
      tep_db_query($query);
      $subs = tep_db_fetch_array(tep_db_query($query));
    ?>
      <H1>You have been unsubscribed. </H1>
    <?php
    } else {
      $query = "SELECT * FROM `autodesk_subscriptions` WHERE `subscriptionReferenceNumber` = '{$ref_num}'";
      tep_db_query($query);
      $subs = tep_db_fetch_array(tep_db_query($query));
    ?>
      <table class="table">
        <thead>
          <tr>
            <td>Subscription Reference Number</td>
            <td>Product</td>
            <td>Seats</td>
            <td>Expires</td>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td><?= $subs['subscriptionReferenceNumber'] ?></td>
            <td><?= $subs['offeringName'] ?></td>
            <td><?= $subs['quantity'] ?></td>
            <td><?= $subs['endDate'] ?></td>
          </tr>
        </tbody>
      </table>

      <p> To unsubscribe from expiring notifications for this subscription, please click the button below.</p>
      <form action="autodesk_unsubscribe.php?action=confirm" method="get">
        <button class="btn btn-primary" type="submit" name="unsubscribe" value="1">Unsubscribe</button>
        <input type="hidden" name="ref_num" value="<?= $ref_num ?>">
      </form>
    <?php } ?>
  </div>
  <div class="buttonSet">
    <div class="text-right"><?php echo tep_draw_button(IMAGE_BUTTON_CONTINUE, 'fa fa-angle-right', tep_href_link('index.php')); ?></div>
  </div>
</div>

<?php
require('includes/template_bottom.php');
require('includes/application_bottom.php');
?>