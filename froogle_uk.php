 <?php
//
// Google Base Feed vX.04 - 2007-03-04
// Original Author unknown.
//

include('includes/application_top.php');
require('baffletrain/autocadlt/includes/functions/googleCategoryfunctions.php');


//  Start TIMER
//  -----------
$stimer           = explode(' ', microtime());
$stimer           = $stimer[1] + $stimer[0];
//  -----------
$catalogURL       = "https://www.cadservices.co.uk/";
$OutFile          = DIR_FS_CATALOG . '/feeds/cadservGooglefeed.txt';
$destination_file = 'cadservGooglefeed.txt'; //"CHANGEME-filename-to-upload-to-froogle.txt" ;
$source_file      = $OutFile;
$imageURL         = 'https://www.cadservices.co.uk/images/';
$productURL       = $catalogURL . 'product_info.php?products_id=';
$already_sent     = array();

$home = DB_SERVER;
$user = DB_SERVER_USERNAME;
$pass = DB_SERVER_PASSWORD;
$base = DB_DATABASE;

$ftp_server = 'uploads.google.com';

$ftp_user_name = 'cadservicesftp';

$ftp_user_pass = '605199haroon';

$ftp_directory = ""; // leave blank for froogle

$taxRate    = 20; //default = 0 (e.g. for 17.5% tax use "$taxRate = 17.5;")
$taxCalc    = ($taxRate / 100) + 1; //Do not edit
$convertCur = false; //default = true
$curType    = "GBP"; // Converts Currency to any defined currency (eg. USD, EUR, GBP)
if ($convertCur) {
    $productURL = $catalogURL . 'product_info.php?currency=' . $curType . '&products_id='; // Product URL if Currency Conversion is enabled
}

// Mod by Ben - Use the Product and Category Descriptions of a specific language
$chooseLanguage = false; //default = false
$lang_code      = "en"; // Language code of the description to use, eg. "en", "fr, "es", "de", etc.  Must match the one used in your shop in Localization -> Languages
if ($chooseLanguage) {
    if ($convertCur) {
        $productURL = $catalogURL . 'product_info.php?language=' . $lang_code . '&currency=' . $curType . '&products_id='; // Product URL if Currency Conversion AND Language Selection is enabled
    } else {
        $productURL = $catalogURL . 'product_info.php?language=' . $lang_code . '&products_id='; // Product URL if Language Selection but NOT Currency Conversion is enabled
    }
}
// End mod by Ben - Use the Product and Category Descriptions of a specific language

// Mod by Ben - Define which localization of Google Base (which country) is this feed used for
$GB_localization = "uk"; // Do not modify! - unless Google Base opens up other localized versions, e.g. Australiia.


//START Advance Optional Values

//(0=False 1=True) (optional_sec must be enabled to use any options)
$optional_sec            = 1; 			// Suggested to leave enabled if you use a specific language for your descriptions
$instock                 = 0;	
$shipping                = 1;			//$lowestShipping = "US:Ground:2.05,CA:Ground:2.05,UK:Ground:1.70,DE:Ground:0,AT:Ground:1.70,IE:Ground:1.70,CH:Ground:1.70,DK:Ground:1.70,PL:Ground:1.70,FR:Ground:1.70";  // Put your shipping costs details (up to 10 options), respecting the format.  See  http://base.google.com/base/attributes.html#shipping
$brand                   = 1;	
$upc                     = 0; 			//Not supported by default osC
$manufacturer_id         = 0; 			//Not supported by default osC
$mpn                     = 1;
$feed_quantity           = 1;
$payment_accepted        = 0;
$default_payment_methods = "Visa,MasterCard,AmericanExpress,Discover,check,WireTransfer,PayPal"; //Acceptable values: cash, check, GoogleCheckout, Visa, MasterCard, AmericanExpress, Discover, wiretransfer
$payment_notes           = 0;
$default_payment_notes   = "We also accept payments by PayPal and by Bank Transfer.";
$tax_percent             = 0; 			// Suggested to use this option only if *all* your products have the same tax rate
$default_tax_percent     = "19"; 		// Does not need to be an integer (e.g. "7.5") - do not put the "%" sign.
$tax_region              = 0; 			// Suggested to use this option only if *all* your products have the same tax region
$default_tax_region      = "Europe"; 	// Change this according to your applicable tax region
$product_type            = 1;
$currency                = 1;
$default_currency        = "GBP";	 	// The currency of the prices of this feed.
$feed_language           = 0; 			// Suggested to leave enabled if you use a specific language for your descriptions
$default_feed_language   = $lang_code; 	//this is not binary.
$ship_to                 = 0;
$default_ship_to         = "ALL";		//this is not binary, not supported by default osC for individual products.
$condition               = 1;
$default_condition       = "new"; 		// Change to suit your store
$feed_exp_date           = date('Y-m-d', time() + 2592000);
$location                = 0;
$default_location        = "Address of the items's location here"; // Addresses should be formatted as: street, city, state, postal code, country. Each location element should be separated by a comma.

//END of Advance Optional Values

  function tep_values_name($values_id) {
    global $languages_id;

    $values = tep_db_query("select products_options_values_name from " . TABLE_PRODUCTS_OPTIONS_VALUES . " where products_options_values_id = '" . (int)$values_id . "' and language_id = '" . (int)$languages_id . "'");
    $values_values = tep_db_fetch_array($values);

    return $values_values['products_options_values_name'];
  }
//End FTP to Froogle
function cleanHTML($text){
		
	$_strip_search  = array(
		"![\t ]+$|^[\t ]+!m", // remove leading/trailing space chars
		'%[\r\n]+%m', // remove CRs and newlines
		'%[,]+%m'
	); // remove CRs and newlines
	$_strip_replace = array(
		'',
		' ',
		' '
	);
	$_cleaner_array = array(
		">" => "> ",
		"&reg;" => "",
		"?" => "",
		"&trade;" => "",
		"?" => "",
		"\t" => "",
		"    " => "",
		"&nbsp;" => " "
	);
	
	//first remove extra CRs and newlines
	$text = preg_replace($_strip_search, $_strip_replace,$text);
	
	//Remove tables
	$text = preg_replace("/<table.+<\/table>/", "", $text);
	
	//now strip html
	$text = strip_tags(strtr($text, $_cleaner_array));
	
	//convert html entities to respective chars
	$text = html_entity_decode($text);
	
	//Remove duplicate spaces
	$text = trim(preg_replace('/\s+/', ' ', $text));
	
	return $text;
}



if (!($link = mysqli_connect($home, $user, $pass))) {
    echo "Error when connecting itself to the data base";
    exit();
}
if (!mysqli_select_db($link, $base)) {
    echo "Error the data base does not exist";
    exit();
}

// Mod by Ben - Use the Product and Category Descriptions of a specific language

    // SQL query if Language Selection is enabled
    $sql = "
        SELECT concat( '" . $productURL . "' ,products.products_id) AS product_url,
            products_gtin,
            products_weight,
            CONCAT( '" . $imageURL . "' ,products.products_image) AS image_url,
            FORMAT( IFNULL(specials.specials_new_products_price, products.products_price) * " . $taxCalc . ",2) AS price,
            manufacturers.manufacturers_id,
            manufacturers.manufacturers_name AS mfgName,
            products.products_google_status AS googprodStatus,
            products.products_id AS id,
            products.products_quantity AS quantity,
            products.products_status AS prodStatus,
            products_description.products_description AS description,
            products_description.products_name AS name,
            products_model AS prodModel, 
            products_shipping.products_ship_flags,
            products_shipping.products_ship_key,
            products_shipping.products_ship_price,
            products_shipping.products_ship_qty,
            d.categories_id AS prodCatID        
        FROM products        
            left join (products_description) on (products.products_id=products_description.products_id)
            left join (manufacturers) on ( manufacturers.manufacturers_id = products.manufacturers_id )
            left join (specials) on ( specials.products_id = products.products_id AND ( ( (specials.expires_date > CURRENT_DATE) OR (IFNULL(specials.expires_date, 0) = 0) ) AND ( specials.status = 1 ) ) )
            left join (products_shipping) on (products.products_id=products_shipping.products_id)
            left join (SELECT * FROM products_to_categories GROUP BY products_id) d on (products.products_id=d.products_id)
        
        WHERE products.products_status != 0
            OR products.products_price != 0
            OR products.products_price != ''
            OR products.products_google_status != 0
    ";

	
	
	
// Mod by Ben - Use the Product and Category Descriptions of a specific language

//
//echo "SQL --------------------------------------------------------------------------------------------<br><pre>" . $sql . "</pre><br>";

$catInfo = "
SELECT
categories.categories_id AS curCatID,
categories.parent_id AS parentCatID,
categories_description.categories_name AS catName,
categories.google_category AS googleCategory,
categories.google_category_baseline AS googleCategoryBase
FROM
categories,
categories_description
WHERE categories.categories_id = categories_description.categories_id
";

$catIndex         = array();
$catTempDes       = array();
$catParentIDArray = array();
$catBaselineArray = array();
//echo '<pre>' . $catInfo . '</pre>';
$processCat       = tep_db_query($catInfo);

while ($catRow = tep_db_fetch_array($processCat)) {
    /*
        if($catRow['curCatID']==663 || $catRow['curCatID']==473 ){
        
        echo '
        $catKey = ' . $catRow['curCatID'] . '<br>
        $catName = ' . $catRow['catName'] . '<br>
        $catparentID = ' . $catRow['parentCatID'] . '<br> 
        $googleCategoryTxt = ' . $catRow['googleCategory'] . '<br>
        $catBaseLine = ' . $catRow['googleCategoryBase'];
        }
    */
    $catKey            = $catRow['curCatID'];
    $catName           = $catRow['catName'];
    $catparentID       = $catRow['parentCatID'];
    $googleCategoryTxt = $catRow['googleCategory'];
    $catBaseLine       = $catRow['googleCategoryBase'];
    if ($catName != "") {
        $catTempDes[$catKey] = $catName;
        /*if($catRow['curCatID'] == 663 || $catRow['curCatID'] == 473 ){
        echo '$catParentIDArray[' . $catKey . ']=' . $catParentIDArray[$catKey] . '<br>';
        }*/
        
        $catParentIDArray[$catKey]       = $catparentID;
        $catBaselineArray[$catKey]       = $catBaseLine;
        $googleCategoryTxtArray[$catKey] = $googleCategoryTxt;
        /*
        if($catRow['curCatID']==663 || $catRow['curCatID']==473 ){
            echo '$catParentIDArray[' . $catKey . ']=' . $catParentIDArray[$catKey] . '<br>';
        }*/
    }
    
}

if (file_exists($OutFile))
    unlink($OutFile);



//echo $output;
$result = mysqli_query($link,$sql . " LIMIT 0, 2047;") or die($result . "SQL error: " . mysqli_error() . "| sql = " . htmlentities($sql));
echo '<pre>' . $sql . '</pre>';
//Currency Information
if ($convertCur) {
    $sql3 = "
    SELECT currencies.value AS curUSD
    FROM
    currencies
    WHERE currencies.code = '$curType'
    ";
    
    $result3 = mysqli_query($link,$sql3) or die($FunctionName . ": SQL error " . mysqli_error() . "| sql3 = " . htmlentities($sql3));
    $row3 = mysqli_fetch_object($result3);
}

$loop_counter = 0;
$total_loop_counter = 0;
$output = "id \t mpn \t brand \t title \t price \t shipping \t gtin \t Google product category \t expiration_date \t availability \t identifier exists \t quantity \t product_type \t condition  \t description \t link \t image_link \n";

$result = mysqli_query($link,$sql . " LIMIT 0, 2047;") or die($result2 . "SQL 2 error: " . mysqli_error() . "| sql = " . htmlentities($sql));
processProducts($result);
echo "<p>Total written " . $total_loop_counter . " of " . mysqli_num_rows($result) . " in part 1</p>";
$total_loop_counter = 0;
$result = mysqli_query($link,$sql . " LIMIT 2048, 4095;") or die($result2 . "SQL 2 error: " . mysqli_error() . "| sql = " . htmlentities($sql));
processProducts($result);

echo "<p>Total written " . $total_loop_counter . " of " . mysqli_num_rows($result) . " in part 2</p>";
Function processProducts($inputProductList) {
    GLOBAL $total_loop_counter, $output, $OutFile, $GB_localization, $taxCalc, $default_condition, $imageURL;
    $loop_counter = 0;
    echo "<p>Todo: " . mysqli_num_rows($inputProductList) . "</p>";
    while ($row = mysqli_fetch_object($inputProductList)) {
        $echoCount = 0;
        $product_id = '';
        $product_mpn = '';
        $product_brand = '';
        $product_name = '';
        $product_price = '';
        $product_shipping = '';
        $product_GTIN = '';
        $product_googleCategory = '';
        $product_Exp_date = '';
        $product_avail = '';
        $product_ident = '';
        $product_quantity = '';
        $product_type = '';
        $product_condition = '';
        $product_desc = '';
        $product_url = '';
        $product_image_url = '';
        if (isset($already_sent[$row->id])) {
            echo "<br> Already done: " . $row->id;
            continue; // if we've sent this one, skip the rest of the while loop            
        };
        $manufacturer = $row->mfgName;
        $rawPrice = preg_replace('/[^0-9.]/', '', $row->price);
        if ($rawPrice <= 0.00) continue; // no price skip
        if ($row->prodStatus == 1 && $row->googprodStatus == 1 && $row->prodCatID != 999999999) {            
            if ($imageURL == $row->image_url) {
                $image = "";
            } else {
                $image = $row->image_url;
            }
            $product_url = tep_href_link('product_info.php', 'products_id=' . $row->id);
            $product_name = substr(cleanHTML($row->name), 0, 70);
            $product_desc = substr(cleanHTML($row->description), 0, 4999);
            $product_Exp_date = $feed_exp_date;
            $product_price = $row->price;
            $product_image_url = $image;
            $product_id = $row->id . "" . $GB_localization;
            $product_avail = "in stock";
            $shipService = '';
            // -------------- Shipping
            $rawPrice = preg_replace('/[^0-9.]/', '', $row->price);
            $sPrice = $row->products_ship_price; //get shipping data
            $sFlags = $row->products_ship_flags;
            $sKey = $row->products_ship_key;
            $sFlagsA = preg_split("/[:,]/", $sFlags);
            $digiPurchase = $sFlagsA[1];
            $excemptFree = $sFlagsA[0];
            if ($digiPurchase == '1') {
                $shipService = 'Digital Purchase delivery by email';
                $shipCost = 0;
            } elseif ($rawPrice > 200 && $excemptFree != '1') {
                $shipService = 'Free Delivery on Orders over ' . 200;
                $shipCost = 0;
            } elseif (@tep_not_null($sPrice) && ($sPrice == 0)) {
                $shipCost = 0;
                $shipService = 'Free Delivery';
            } elseif (@tep_not_null($sPrice) && ($sPrice > 0)) {
                $shipService = 'Standard Delivery to UK Mainland Ground floor Goods-in';
                $shipCost = $sPrice;
            } else {
                $shipCost = MODULE_SHIPPING_TCSSHIP_FLATRATE; //set defaults
                $shipService = 'Standard Delivery to UK Mainland Ground floor Goods-in';
            }
            $theCost = $shipCost * $taxCalc;
            $product_shipping = "UK::" . $shipService . ":" . $theCost . " GBP";
            //------------------------------------- google category  --------------------------------------
            $googleCategory = generateGoogleCategory($row->prodCatID, null, null, null, 0);
            //echo 'generateGoogleCategory: ' . $row->prodCatID . ' ' . $googleCategory . '<br>';
            //if ($catBaselineArray[$row->prodCatID] == 1) {
            //   //    echo $googleCategoryTxtArray . '<br>';
            //}
            $product_googleCategory = stripslashes($googleCategory); //clean output and post
            // --------------------------------- GTIN ------------------------------
            $product_ident = "TRUE";
            if (@tep_not_null($row->products_gtin) && ($row->products_gtin != 0)) {
                $product_GTIN = $row->products_gtin;
            } else {
                if (substr($row->mfgName, 0, 3) == "TCS") { // if no GTIN set identfier exists field to false on these brands only
                    $row->prodModel = '';
                    $product_GTIN = "";
                    $product_ident = "FALSE";
                }
            }
            if ($row->prodStatus == 1 && $row->googprodStatus == 1) {
                $prodStatusOut = "Y";
            } else {
                $prodStatusOut = "N";
            }
            $product_instock = $prodStatusOut;
            $manufacturer = $row->mfgName;
            $product_brand = $manufacturer;
            $product_mpn = $row->prodModel;
            $qty = $row->quantity + rand(5, 30);
            $product_quantity = $qty;
            $catNameTemp = strtolower($catName);
            $product_type = $row->catName;
            $hay = $row->name;
            if (striPos($hay, 'used') || striPos($hay, 'refurbished') || striPos($hay, 'hp renew')) {
                $product_condition = 'refurbished';
            } else {
                $product_condition = $default_condition;
            }
            
            $variations_sql = "select * from products_variations pv WHERE pv.products_id = '" . $row->id . "' order by pv.sort_order";
		$variations_query = tep_db_query($variations_sql);
		//$attributesNameString = "";
		if(tep_db_num_rows($variations_query)){		    
			while ($variations_values = tep_db_fetch_array($variations_query)) {
				$attributes = explode('{', substr($variations_values['attributes'], strpos($variations_values['attributes'], '{')+1));
			  	//$attributesNameString = "";
			  	$product_name_attribed = $product_name;
			  	for ($i=0, $n=sizeof($attributes); $i<$n; $i++) {			           
			            $pair = explode('}', $attributes[$i]);
			            $product_name_attribed .= ', ' . tep_values_name($pair[1]);
				    //$attributesNameString .= '<span style="font-weight:bold">' . tep_options_name($pair[0]) . ':</span> ';
				    //$attributesNameString .= tep_values_name($pair[1]) . ' ';		    
				    
			        }  
			        $product_url = tep_href_link('product_info.php', 'products_id=' . $row->id . $variations_values['attributes']);
			        
				
			        $product_var_url = tep_href_link('product_info.php', 'products_id=' . $row->id . $variations_values['attributes']);
			        $product_var_id = $product_id . '_' . $variations_values['products_variations_id'];
			        $product_var_mpn = $variations_values['model'];
			        $product_var_name = $product_name_attribed;
			        $product_var_price = $variations_values['price'];
			        if (@tep_not_null($variations_values['gtin'])){
			       		$product_var_GTIN = $variations_values['gtin'];
			  	} else{
			  		$product_var_GTIN = $product_GTIN;
			  	}
			        if (@tep_not_null($variations_values['image_id']) && $variations_values['image_id'] != 0){
			            $values = tep_db_query("select image from products_images where id = '" . $variations_values['image_id'] . "'");
    				    $values_values = tep_db_fetch_array($values);
                        echo "select image from products_images where id = '" . $variations_values['image_id'] . "'" . PHP_EOL;
                        echo "https://www.cadservices.co.uk/images/" . $values_values["image"] . PHP_EOL;
			            $product_image_url = "https://www.cadservices.co.uk/images/" . $values_values["image"];
			        } 
				$output.= $product_var_id . "\t" . $product_var_mpn . "\t" . $product_brand . "\t" . $product_var_name . "\t" . $product_var_price . "\t" . $product_shipping . "\t" . $product_var_GTIN . "\t" . $product_googleCategory . "\t" . $product_Exp_date . "\t" . $product_avail . "\t" . $product_ident . "\t" . $product_quantity . "\t" . $product_type . "\t" . $product_condition . "\t" . $product_desc . "\t" . $product_var_url . "\t" . $product_image_url . "\n";
	   
		    	}
	    	} else {	    	  
	      		$output.= $product_id . "\t" . $product_mpn . "\t" . $product_brand . "\t" . $product_name . "\t" . $product_price . "\t" . $product_shipping . "\t" . $product_GTIN . "\t" . $product_googleCategory . "\t" . $product_Exp_date . "\t" . $product_avail . "\t" . $product_ident . "\t" . $product_quantity . "\t" . $product_type . "\t" . $product_condition . "\t" . $product_desc . "\t" . $product_url . "\t" . $product_image_url . "\n";
	        }
            $already_sent[$row->id] = 1;
            $loop_counter++;
            $total_loop_counter++;            
        }
    }    
	$fp   = fopen($OutFile, "a");
	$fout = fwrite($fp, $output);
	fclose($fp);
	chmod($OutFile, 0777);
	echo "<p>Written " . $total_loop_counter . "</p>";
	$output = "";
   	 return True;
}

//Start FTP to Froogle


function ftp_file($ftpservername, $ftpusername, $ftppassword, $ftpsourcefile, $ftpdirectory, $ftpdestinationfile)
{
    // set up basic connection
    $conn_id = ftp_connect($ftpservername);
    if ($conn_id == false) {
        echo "FTP open connection failed to $ftpservername <br />\n";
        return false;
    }
    
    // login with username and password
    $login_result = ftp_login($conn_id, $ftpusername, $ftppassword);
    
    // check connection
    if ((!$conn_id) || (!$login_result)) {
        echo "FTP connection has failed!<br />\n";
        //echo "Attempted to connect to " . $ftpservername . " for user " . $ftpusername . "<br />\n";
        return false;
    } else {
        //echo "Connected to " . $ftpservername . ", for user " . $ftpusername . "<br />\n";
    }
    
    if (strlen($ftpdirectory) > 0) {
        if (ftp_chdir($conn_id, $ftpdirectory)) {
            //echo "Current directory is now: " . ftp_pwd($conn_id) . "<br />\n";
        } else {
            //echo "Couldn't change directory on $ftpservername<br />\n";
            return false;
        }
    }
    
    ftp_pasv($conn_id, true);
    // upload the file
    $upload = ftp_put($conn_id, $ftpdestinationfile, $ftpsourcefile, FTP_ASCII);
    echo $upload;
    // check upload status
    if (!$upload) {
        //echo "$ftpservername: FTP upload has failed!<br />\n";
        return false;
    } else {
        //echo "Uploaded " . $ftpsourcefile . " to " . $ftpservername . " as " . $ftpdestinationfile . "<br />\n";
    }
    
    // close the FTP stream
    ftp_close($conn_id);
    return true;
}
//echo ftp_file($ftp_server, $ftp_user_name, $ftp_user_pass, $source_file, $ftp_directory, $destination_file);

//  End TIMER
//  ---------
$etimer = explode(' ', microtime());
$etimer = $etimer[1] + $etimer[0];
echo '<p style="margin:auto; text-align:center">';
printf("Script timer: <strong>%f</strong> seconds.", ($etimer - $stimer));
echo '</p>Script Completed';
//  ---------
?> 