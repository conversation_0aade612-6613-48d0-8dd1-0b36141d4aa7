# Virtual Host Configuration for Local Development
# Add this to your XAMPP Apache configuration

<VirtualHost *:80>
    ServerName localhost.cadservices
    DocumentRoot "E:/Build/httpdocs"
    DirectoryIndex index.php index.html
    
    <Directory "E:/Build/httpdocs">
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
    </Directory>
    
    # Enable mod_rewrite
    RewriteEngine On
    
    # Redirect HTTP to HTTPS
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
    
    ErrorLog "E:/tools/xampp/apache/logs/cadservices_error.log"
    CustomLog "E:/tools/xampp/apache/logs/cadservices_access.log" combined
</VirtualHost>

<VirtualHost *:443>
    ServerName localhost.cadservices
    DocumentRoot "E:/Build/httpdocs"
    DirectoryIndex index.php index.html
    
    <Directory "E:/Build/httpdocs">
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
    </Directory>
    
    # SSL Configuration
    SSLEngine on
    SSLCertificateFile "E:/tools/xampp/apache/conf/ssl.crt/localhost.crt"
    SSLCertificateKeyFile "E:/tools/xampp/apache/conf/ssl.key/localhost.key"
    
    # Enable mod_rewrite
    RewriteEngine On
    
    ErrorLog "E:/tools/xampp/apache/logs/cadservices_ssl_error.log"
    CustomLog "E:/tools/xampp/apache/logs/cadservices_ssl_access.log" combined
</VirtualHost>
