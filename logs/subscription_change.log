Starting subscription change processing at 2025-04-29 00:00:26Subscription column mapping retreivedIncoming payload: Array
(
    [message] => Catalog is updated.
    [modifiedAt] => 2025-04-29T00:00:23Z
)
Got subscription: Array
(
    [status] => fail
    [failing] => api_call
    [response] => Client error: `GET https://enterprise-api.autodesk.com/v2/subscriptions` resulted in a `400 Bad Request` response:
{"errors":[{"code":43002,"message":"Enter required filter(s).Please select subscriptionId or subscriptionReferenceNumber (truncated...)

    [debug] => Array
        (
            [log] => Array
                (
                    [v2_subscriptions] => Array
                        (
                            [Headers] => Array
                                (
                                    [Authorization] => Bearer R5UDGNbRhygyBueihsKkFhGy4EKU
                                    [timestamp] => 1745884826
                                    [signature] => EXkiDjmJM/Lqv+mAV+4YB+KpdrORxTWgibfMO7qnJP4=
                                    [CSN] => 5103159758
                                )

                            [method] => GET
                            [endpoint] => https://enterprise-api.autodesk.com/v2/subscriptions
                            [data] => Array
                                (
                                    [headers] => Array
                                        (
                                            [Authorization] => Bearer R5UDGNbRhygyBueihsKkFhGy4EKU
                                            [timestamp] => 1745884826
                                            [signature] => EXkiDjmJM/Lqv+mAV+4YB+KpdrORxTWgibfMO7qnJP4=
                                            [CSN] => 5103159758
                                        )

                                    [query] => Array
                                        (
                                            [filter[subscriptionId]] => 
                                        )

                                )

                            [error] => {"errors":[{"code":43002,"message":"Enter required filter(s).Please select subscriptionId or subscriptionReferenceNumber"}]}
                        )

                )

        )

)
Subscription change processing completed successfully
mapped:Array
(
)

Skipped:Starting subscription change processing at 2025-05-02 00:00:44Subscription column mapping retreivedIncoming payload: Array
(
    [message] => Catalog is updated.
    [changeType] => Product_Catalog_Change
    [modifiedAt] => 2025-05-02T00:00:34Z
)
Got subscription: Array
(
    [status] => fail
    [failing] => api_call
    [response] => Client error: `GET https://enterprise-api.autodesk.com/v2/subscriptions` resulted in a `400 Bad Request` response:
{"errors":[{"code":43002,"message":"Enter required filter(s).Please select subscriptionId or subscriptionReferenceNumber (truncated...)

    [debug] => Array
        (
            [log] => Array
                (
                    [v2_subscriptions] => Array
                        (
                            [Headers] => Array
                                (
                                    [Authorization] => Bearer aPbgrJznIicEPqfeTREE9vlncL9A
                                    [timestamp] => 1746144044
                                    [signature] => Mr2y1QgdPZLwlEqpcMAcDT8tTb9rxbt80ehmgOWbGxo=
                                    [CSN] => 5103159758
                                )

                            [method] => GET
                            [endpoint] => https://enterprise-api.autodesk.com/v2/subscriptions
                            [data] => Array
                                (
                                    [headers] => Array
                                        (
                                            [Authorization] => Bearer aPbgrJznIicEPqfeTREE9vlncL9A
                                            [timestamp] => 1746144044
                                            [signature] => Mr2y1QgdPZLwlEqpcMAcDT8tTb9rxbt80ehmgOWbGxo=
                                            [CSN] => 5103159758
                                        )

                                    [query] => Array
                                        (
                                            [filter[subscriptionId]] => 
                                        )

                                )

                            [error] => {"errors":[{"code":43002,"message":"Enter required filter(s).Please select subscriptionId or subscriptionReferenceNumber"}]}
                        )

                )

        )

)
Subscription change processing completed successfully
mapped:Array
(
)

Skipped:Starting subscription change processing at 2025-05-07 00:01:13Subscription column mapping retreivedIncoming payload: Array
(
    [message] => Catalog is updated.
    [changeType] => Product_Catalog_Change
    [modifiedAt] => 2025-05-07T00:01:09Z
)
Got subscription: Array
(
    [status] => fail
    [failing] => api_call
    [response] => Client error: `GET https://enterprise-api.autodesk.com/v2/subscriptions` resulted in a `400 Bad Request` response:
{"errors":[{"code":43002,"message":"Enter required filter(s).Please select subscriptionId or subscriptionReferenceNumber (truncated...)

    [debug] => Array
        (
            [log] => Array
                (
                    [v2_subscriptions] => Array
                        (
                            [Headers] => Array
                                (
                                    [Authorization] => Bearer NCG7fq1J9vO9CIebUgGZyLGLK35c
                                    [timestamp] => 1746576073
                                    [signature] => 0q7KjQ1scCTl2+plAUs8sgJom8RRW7E9lvOh0RREIH4=
                                    [CSN] => 5103159758
                                )

                            [method] => GET
                            [endpoint] => https://enterprise-api.autodesk.com/v2/subscriptions
                            [data] => Array
                                (
                                    [headers] => Array
                                        (
                                            [Authorization] => Bearer NCG7fq1J9vO9CIebUgGZyLGLK35c
                                            [timestamp] => 1746576073
                                            [signature] => 0q7KjQ1scCTl2+plAUs8sgJom8RRW7E9lvOh0RREIH4=
                                            [CSN] => 5103159758
                                        )

                                    [query] => Array
                                        (
                                            [filter[subscriptionId]] => 
                                        )

                                )

                            [error] => {"errors":[{"code":43002,"message":"Enter required filter(s).Please select subscriptionId or subscriptionReferenceNumber"}]}
                        )

                )

        )

)
Subscription change processing completed successfully
mapped:Array
(
)

Skipped:Starting subscription change processing at 2025-05-08 00:02:06Subscription column mapping retreivedIncoming payload: Array
(
    [message] => Catalog is updated.
    [changeType] => Product_Catalog_Change
    [modifiedAt] => 2025-05-08T00:02:02Z
)
Got subscription: Array
(
    [status] => fail
    [failing] => api_call
    [response] => Client error: `GET https://enterprise-api.autodesk.com/v2/subscriptions` resulted in a `400 Bad Request` response:
{"errors":[{"code":43002,"message":"Enter required filter(s).Please select subscriptionId or subscriptionReferenceNumber (truncated...)

    [debug] => Array
        (
            [log] => Array
                (
                    [v2_subscriptions] => Array
                        (
                            [Headers] => Array
                                (
                                    [Authorization] => Bearer rBxuH9GZLGpEXhFSs6k9UHonLFVG
                                    [timestamp] => 1746662526
                                    [signature] => VkLffM8k5SGr55OReLUqvmBIQ6S+D+8W3yi3o5ExX4Q=
                                    [CSN] => 5103159758
                                )

                            [method] => GET
                            [endpoint] => https://enterprise-api.autodesk.com/v2/subscriptions
                            [data] => Array
                                (
                                    [headers] => Array
                                        (
                                            [Authorization] => Bearer rBxuH9GZLGpEXhFSs6k9UHonLFVG
                                            [timestamp] => 1746662526
                                            [signature] => VkLffM8k5SGr55OReLUqvmBIQ6S+D+8W3yi3o5ExX4Q=
                                            [CSN] => 5103159758
                                        )

                                    [query] => Array
                                        (
                                            [filter[subscriptionId]] => 
                                        )

                                )

                            [error] => {"errors":[{"code":43002,"message":"Enter required filter(s).Please select subscriptionId or subscriptionReferenceNumber"}]}
                        )

                )

        )

)
Subscription change processing completed successfully
mapped:Array
(
)

Skipped:Starting subscription change processing at 2025-05-10 00:00:35Subscription column mapping retreivedIncoming payload: Array
(
    [message] => Catalog is updated.
    [changeType] => Product_Catalog_Change
    [modifiedAt] => 2025-05-10T00:00:32Z
)
Got subscription: Array
(
    [status] => fail
    [failing] => api_call
    [response] => Client error: `GET https://enterprise-api.autodesk.com/v2/subscriptions` resulted in a `400 Bad Request` response:
{"errors":[{"code":43002,"message":"Enter required filter(s).Please select subscriptionId or subscriptionReferenceNumber (truncated...)

    [debug] => Array
        (
            [log] => Array
                (
                    [v2_subscriptions] => Array
                        (
                            [Headers] => Array
                                (
                                    [Authorization] => Bearer vb7bKdP3Sw2386RegsFj9A0kNcj6
                                    [timestamp] => 1746835235
                                    [signature] => EzcOaG3N80bV670D5vtbYsqvRxj4WkcQvcVfIW3C7GU=
                                    [CSN] => 5103159758
                                )

                            [method] => GET
                            [endpoint] => https://enterprise-api.autodesk.com/v2/subscriptions
                            [data] => Array
                                (
                                    [headers] => Array
                                        (
                                            [Authorization] => Bearer vb7bKdP3Sw2386RegsFj9A0kNcj6
                                            [timestamp] => 1746835235
                                            [signature] => EzcOaG3N80bV670D5vtbYsqvRxj4WkcQvcVfIW3C7GU=
                                            [CSN] => 5103159758
                                        )

                                    [query] => Array
                                        (
                                            [filter[subscriptionId]] => 
                                        )

                                )

                            [error] => {"errors":[{"code":43002,"message":"Enter required filter(s).Please select subscriptionId or subscriptionReferenceNumber"}]}
                        )

                )

        )

)
Subscription change processing completed successfully
mapped:Array
(
)

Skipped:Starting subscription change processing at 2025-05-15 00:00:33Subscription column mapping retreivedIncoming payload: Array
(
    [message] => Catalog is updated.
    [changeType] => Product_Catalog_Change
    [modifiedAt] => 2025-05-15T00:00:30Z
)
Got subscription: Array
(
    [status] => fail
    [failing] => api_call
    [response] => Client error: `GET https://enterprise-api.autodesk.com/v2/subscriptions` resulted in a `400 Bad Request` response:
{"errors":[{"code":43002,"message":"Enter required filter(s).Please select subscriptionId or subscriptionReferenceNumber (truncated...)

    [debug] => Array
        (
            [log] => Array
                (
                    [v2_subscriptions] => Array
                        (
                            [Headers] => Array
                                (
                                    [Authorization] => Bearer oS4JWhsVGYRvIGLmToDg9bkKnXat
                                    [timestamp] => 1747267233
                                    [signature] => SADe7p7k84iZdBI9to1x4mEbrKp5GilAwH5/GXtWeqQ=
                                    [CSN] => 5103159758
                                )

                            [method] => GET
                            [endpoint] => https://enterprise-api.autodesk.com/v2/subscriptions
                            [data] => Array
                                (
                                    [headers] => Array
                                        (
                                            [Authorization] => Bearer oS4JWhsVGYRvIGLmToDg9bkKnXat
                                            [timestamp] => 1747267233
                                            [signature] => SADe7p7k84iZdBI9to1x4mEbrKp5GilAwH5/GXtWeqQ=
                                            [CSN] => 5103159758
                                        )

                                    [query] => Array
                                        (
                                            [filter[subscriptionId]] => 
                                        )

                                )

                            [error] => {"errors":[{"code":43002,"message":"Enter required filter(s).Please select subscriptionId or subscriptionReferenceNumber"}]}
                        )

                )

        )

)
Subscription change processing completed successfully
mapped:Array
(
)

Skipped:Starting subscription change processing at 2025-05-16 00:00:32Subscription column mapping retreivedIncoming payload: Array
(
    [message] => Catalog is updated.
    [changeType] => Product_Catalog_Change
    [modifiedAt] => 2025-05-16T00:00:28Z
)
Got subscription: Array
(
    [status] => fail
    [failing] => api_call
    [response] => Client error: `GET https://enterprise-api.autodesk.com/v2/subscriptions` resulted in a `400 Bad Request` response:
{"errors":[{"code":43002,"message":"Enter required filter(s).Please select subscriptionId or subscriptionReferenceNumber (truncated...)

    [debug] => Array
        (
            [log] => Array
                (
                    [v2_subscriptions] => Array
                        (
                            [Headers] => Array
                                (
                                    [Authorization] => Bearer 4IRCxliaCxlA70557FIeCVB7dVqP
                                    [timestamp] => 1747353632
                                    [signature] => N0WrVaTrKkZzDKVE3eqRBGxw1Qojo6GByyWP4Vqg7CI=
                                    [CSN] => 5103159758
                                )

                            [method] => GET
                            [endpoint] => https://enterprise-api.autodesk.com/v2/subscriptions
                            [data] => Array
                                (
                                    [headers] => Array
                                        (
                                            [Authorization] => Bearer 4IRCxliaCxlA70557FIeCVB7dVqP
                                            [timestamp] => 1747353632
                                            [signature] => N0WrVaTrKkZzDKVE3eqRBGxw1Qojo6GByyWP4Vqg7CI=
                                            [CSN] => 5103159758
                                        )

                                    [query] => Array
                                        (
                                            [filter[subscriptionId]] => 
                                        )

                                )

                            [error] => {"errors":[{"code":43002,"message":"Enter required filter(s).Please select subscriptionId or subscriptionReferenceNumber"}]}
                        )

                )

        )

)
Subscription change processing completed successfully
mapped:Array
(
)

Skipped:Starting subscription change processing at 2025-05-16 16:17:26Subscription column mapping retreivedIncoming payload: Array
(
    [message] => Catalog is updated.
    [changeType] => Product_Catalog_Change
    [modifiedAt] => 2025-05-16T16:17:19Z
)
Got subscription: Array
(
    [status] => fail
    [failing] => api_call
    [response] => Client error: `GET https://enterprise-api.autodesk.com/v2/subscriptions` resulted in a `400 Bad Request` response:
{"errors":[{"code":43002,"message":"Enter required filter(s).Please select subscriptionId or subscriptionReferenceNumber (truncated...)

    [debug] => Array
        (
            [log] => Array
                (
                    [v2_subscriptions] => Array
                        (
                            [Headers] => Array
                                (
                                    [Authorization] => Bearer K5c9GS4lcTZxMwhJ1A5PTFPAscHy
                                    [timestamp] => 1747412246
                                    [signature] => tOObX5gG6G7gWxLi4aeQRjXsL5vdXQvFtalgh8BonG8=
                                    [CSN] => 5103159758
                                )

                            [method] => GET
                            [endpoint] => https://enterprise-api.autodesk.com/v2/subscriptions
                            [data] => Array
                                (
                                    [headers] => Array
                                        (
                                            [Authorization] => Bearer K5c9GS4lcTZxMwhJ1A5PTFPAscHy
                                            [timestamp] => 1747412246
                                            [signature] => tOObX5gG6G7gWxLi4aeQRjXsL5vdXQvFtalgh8BonG8=
                                            [CSN] => 5103159758
                                        )

                                    [query] => Array
                                        (
                                            [filter[subscriptionId]] => 
                                        )

                                )

                            [error] => {"errors":[{"code":43002,"message":"Enter required filter(s).Please select subscriptionId or subscriptionReferenceNumber"}]}
                        )

                )

        )

)
Subscription change processing completed successfully
mapped:Array
(
)

Skipped:Starting subscription change processing at 2025-05-20 00:00:29Subscription column mapping retreivedIncoming payload: Array
(
    [message] => Catalog is updated.
    [changeType] => Product_Catalog_Change
    [modifiedAt] => 2025-05-20T00:00:26Z
)
Got subscription: Array
(
    [status] => fail
    [failing] => api_call
    [response] => Client error: `GET https://enterprise-api.autodesk.com/v2/subscriptions` resulted in a `400 Bad Request` response:
{"errors":[{"code":43002,"message":"Enter required filter(s).Please select subscriptionId or subscriptionReferenceNumber (truncated...)

    [debug] => Array
        (
            [log] => Array
                (
                    [v2_subscriptions] => Array
                        (
                            [Headers] => Array
                                (
                                    [Authorization] => Bearer GDCAtg4wBERdMDbZFFqfLZyIii1n
                                    [timestamp] => 1747699229
                                    [signature] => tKVHHizNxxZvai15Ld48XNy9OEyRJ1XoIuME+waeuxs=
                                    [CSN] => 5103159758
                                )

                            [method] => GET
                            [endpoint] => https://enterprise-api.autodesk.com/v2/subscriptions
                            [data] => Array
                                (
                                    [headers] => Array
                                        (
                                            [Authorization] => Bearer GDCAtg4wBERdMDbZFFqfLZyIii1n
                                            [timestamp] => 1747699229
                                            [signature] => tKVHHizNxxZvai15Ld48XNy9OEyRJ1XoIuME+waeuxs=
                                            [CSN] => 5103159758
                                        )

                                    [query] => Array
                                        (
                                            [filter[subscriptionId]] => 
                                        )

                                )

                            [error] => {"errors":[{"code":43002,"message":"Enter required filter(s).Please select subscriptionId or subscriptionReferenceNumber"}]}
                        )

                )

        )

)
Subscription change processing completed successfully
mapped:Array
(
)

Skipped: