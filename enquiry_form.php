<?php


  require('includes/application_top.php');

  require('includes/languages/' . $language . '/contact_us.php');
  
function check_email_address($email) {
  // First, we check that there's one @ symbol, 
  // and that the lengths are right.
  
  if (!preg_match("[^[^@]{1,64}@[^@]{1,255}$]", $email)) {
    // Email invalid because wrong number of characters 
    // in one section or wrong number of @ symbols.
    return false;
  }
  // Split it into sections to make life easier
  $email_array = explode("@", $email);
  $local_array = explode(".", $email_array[0]);
	  for ($i = 0; $i < sizeof($local_array); $i++) {
		if
	(!preg_match("[^(([A-Za-z0-9!#$%&'*+/=?^_`{|}~-][A-Za-z0-9!#$%&?'*+/=?^_`{|}~\.-]{0,63})|(\"[^(\\|\")]{0,62}\"))$]",
	$local_array[$i])) {
		  return false;
		}
	  }
	  // Check if domain is IP. If not, 
	  // it should be valid domain name
	  if (!preg_match("^\[?[0-9\.]+\]?$", $email_array[1])) {
		$domain_array = explode(".", $email_array[1]);
		if (sizeof($domain_array) < 2) {
			return false; // Not enough parts to domain
		}
		for ($i = 0; $i < sizeof($domain_array); $i++) {
		  if
	(!preg_match("[^(([A-Za-z0-9][A-Za-z0-9-]{0,61}[A-Za-z0-9])|?([A-Za-z0-9]+))$]",
	$domain_array[$i])) {
			return false;
		  }
		}
	  }
	  $emailNotEntered = 1;
	}

 if (isset($_GET['action']) && ($_GET['action'] == 'quote') ) {
    $error = false;

    $name = tep_db_prepare_input($_POST['name']);
    $email_address = tep_db_prepare_input($_POST['email']);
    $enquiry = tep_db_prepare_input($_POST['enquiry']);
	$product = tep_db_prepare_input($_POST['product']);
	$phone = tep_db_prepare_input($_POST['phone']);
	$enquiry = 'Phone: ' . $phone . '
	Product: ' . $product . '
	
	' . $enquiry;
	
	
    if (!tep_validate_email($email_address)) {
      $error = true;

      $messageStack->add('contact', ENTRY_EMAIL_ADDRESS_CHECK_ERROR);
    }

    $actionRecorder = new actionRecorder('ar_contact_us', (tep_session_is_registered('customer_id') ? $customer_id : null), $name);
    if (!$actionRecorder->canPerform()) {
      $error = true;

      $actionRecorder->record(false);

      $messageStack->add('contact', sprintf(ERROR_ACTION_RECORDER, (defined('MODULE_ACTION_RECORDER_CONTACT_US_EMAIL_MINUTES') ? (int)MODULE_ACTION_RECORDER_CONTACT_US_EMAIL_MINUTES : 15)));
    }
	
   // if ($error == false) {
      tep_mail(STORE_OWNER, '<EMAIL>', 'Quote Request', $enquiry, $name, $email_address);

      $actionRecorder->record();
      tep_redirect("https://www.cadservices.co.uk/response.php");
      //tep_redirect(tep_href_link('contact_us.php', 'action=success'));
   // }
  }
    
    
if ($_POST['name']){
	$firstEqryRun=0;
	$name= $_POST['name'];
	$company = $_POST['company'];
	$address = $_POST['address'];
	$telephone = $_POST['telephone'];
	$fax = $_POST['fax'];
	$email = $_POST['email'];
	$extra= $_POST['extra'];
	
	if ($name== ''){
		$nameNotEntered = 1;
	} else {
		$nameNotEntered = 0;
	}
	$message = $_POST['message'];
	
	if (strtolower(str_replace(' ','',$_POST['extra'])) == "ybzq"){
		$extraCorrect = 1;
	} else {
		$extraCorrect = 0;
	}
	
	if ($email == "") {
		$emailInvalid = 1;
	} else {
		$emailInvalid = 0;
	}
	
	if ($extraCorrect == 1 && $emailInvalid == 0){
            $form_details =  "Name: " . $name . "
            Company: " . $company . "
            Address: " . $address . "
            Phone: " . $telephone . "
            Fax: " . $fax . "
            E-Mail: " .  $email . " 
            Message: 
            " . $message;
		$recipient = '<EMAIL>'; // Email address for sending the information
		  if (isset($_POST['formSubject'])) {		
			 $subject = $_POST['formSubject'] . ' Enquiry from cadservices.co.uk'; // Displays the subject in Outlook Express
		  } else { 
			$subject = 'Enquiry from cadservices.co.uk'; // Displays the subject in Outlook Express
		  }
		
		
		$from = $_POST['email']; // Displays the email address in Outlook Express
		mail($recipient, $subject, $form_details, "FROM: $from \n");
		 if (isset($_POST['formLocationURL'])) {					
		    tep_redirect("https://www.cadservices.co.uk/response.php?locationURL=" . $_POST['formLocationURL']);			
		 } else {		
			tep_redirect("https://www.cadservices.co.uk/response.php");
		 }
	}
} else {
	$firstEqryRun=1;
	$name= '';
	$company = '';
	$address = '';
	$telephone = '';
	$fax = '';
	$email = '';
	$message = '';
	$extraCorrect = 0;
}
require('includes/template_top.php');
?>  <!--<b>Mobile Number
        <br />        <input name="fax" type="text" id="fax" size="20" value="<?php echo htmlspecialchars($fax) ?>" class="form-control">
        <br />-->
<h1>Enquiry Form</h1>
<div>
    <form name="eqform" action="https://www.cadservices.co.uk/enquiry_form.php" method="POST">
        <div class="form-group col-sm-6">
            <label for="inputName">Name</label>
            <input name="name" type="text" id="inputName" size="20" value="<?php echo htmlspecialchars($name)?>" class="form-control">
        </div>
        <div class="form-group col-sm-6">
            <label for="inputCompany">Company Name(if any)</label>
            <input name="company" type="text" id="inputCompany" size="20" value="<?php echo htmlspecialchars($company)?>" class="form-control">
        </div>
        <div class="form-group col-sm-12">
            <label for="textAreaAddress">Address</label>
            <textarea class="form-control" name="address" cols="20" rows="6" id="textAreaAddress">
                <?php echo htmlspecialchars($address) ?>
            </textarea>
        </div>
        <div class="form-group col-sm-6">
            <label for="inputTelephone">Telephone</label>
            <input name="telephone" type="text" id="inputTelephone" size="20" value="<?php echo htmlspecialchars($telephone) ?>" class="form-control">
        </div>
        <div class="form-group col-sm-6">
            <label for="inputEmail">Email Address</label>
            <input name="email" type="text" id="inputEmail" size="20" value="<?php echo htmlspecialchars($email) ?>" class="form-control">
        </div>
        <div class="form-group  col-sm-12">
            <label for="textareaMessage">Message/Request</label>
            <textarea name="message" cols="56" rows="7" id="textareaMessage" class="form-control">
                <?php echo htmlspecialchars($message)?>
            </textarea>
		</div>
		<div class="form-group col-sm-12"> 
			<div class="media ">
				<div class="media-left  media-middle">
					<img class="media-object" src="images/extra_gdrt.png">
				</div>
				<div class="media-body"><label for="textareaMessage">Please input the text in the image.</label>
					<input name="extra" type="text" id="inputExtra" size="50" value="<?php echo htmlspecialchars($extra) ?>" class="form-control">
				</div>
			</div>  
		</div>
        <?php if ($firstEqryRun == 0 && $extraCorrect == 0){ ?>
            <div class="alert alert-danger col-sm-12" role="alert">
                Text entered does not match text displayed, (Note: case and spacing ignored).
            </div>
        <?php 	} 
		if ($firstEqryRun == 0 && $emailInvalid == 1) { ?>
			<div class="alert alert-danger col-sm-12" role="alert">
			E-Mail address not supplied.
            </div>
		<?php	
			}
		?>
		<div>
			<div class="form-group text-left col-sm-6">
				<input type="reset" value="Clear" name="B2" class="btn btn-danger">
			</div>
			<div class="form-group text-right col-sm-6">
				<input type="submit" value="Send" name="B1" class="btn btn-success">
			</div>
		</div>
		
    </form>
</div>

<?php
  require('includes/template_bottom.php');
  require('includes/application_bottom.php');
?>

