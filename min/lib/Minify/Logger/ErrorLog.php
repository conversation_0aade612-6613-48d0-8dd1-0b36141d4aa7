<?php
/**
 * Class Minify_Logger_ErrorLog
 * @package Minify
 */

/**
 * Apache Error Log logger for Minify
 * 
 * This logger writes messages to the Apache error log using PHP's error_log() function.
 * Messages are formatted with timestamps and prefixed with the application name.
 * 
 * @package Minify
 * <AUTHOR> Implementation
 */
class Minify_Logger_ErrorLog {
    
    /**
     * @var string Prefix for log messages
     */
    private $_prefix;
    
    /**
     * @var int Log level (for future use)
     */
    private $_logLevel;
    
    /**
     * Constructor
     * 
     * @param string $prefix Optional prefix for log messages (default: 'Minify')
     * @param int $logLevel Optional log level (default: 0 - all messages)
     */
    public function __construct($prefix = 'Minify', $logLevel = 0) {
        $this->_prefix = $prefix;
        $this->_logLevel = $logLevel;
    }
    
    /**
     * Log a message to the Apache error log
     * 
     * @param string $message The message to log
     * @param string $label Optional label/category for the message
     * @return bool True on success, false on failure
     */
    public function log($message, $label = null) {
        // Format the message with timestamp and prefix
        $timestamp = date('Y-m-d H:i:s');
        $logPrefix = $this->_prefix;
        
        if ($label && $label !== $this->_prefix) {
            $logPrefix .= "[$label]";
        }
        
        $formattedMessage = "[$timestamp] $logPrefix: $message";
        
        // Write to Apache error log
        return error_log($formattedMessage);
    }
    
    /**
     * Log an error message
     * 
     * @param string $message The error message to log
     * @return bool True on success, false on failure
     */
    public function error($message) {
        return $this->log($message, 'ERROR');
    }
    
    /**
     * Log a warning message
     * 
     * @param string $message The warning message to log
     * @return bool True on success, false on failure
     */
    public function warning($message) {
        return $this->log($message, 'WARNING');
    }
    
    /**
     * Log an info message
     * 
     * @param string $message The info message to log
     * @return bool True on success, false on failure
     */
    public function info($message) {
        return $this->log($message, 'INFO');
    }
    
    /**
     * Log a debug message
     * 
     * @param string $message The debug message to log
     * @return bool True on success, false on failure
     */
    public function debug($message) {
        return $this->log($message, 'DEBUG');
    }
    
    /**
     * Set the log prefix
     * 
     * @param string $prefix The new prefix
     */
    public function setPrefix($prefix) {
        $this->_prefix = $prefix;
    }
    
    /**
     * Get the current log prefix
     * 
     * @return string The current prefix
     */
    public function getPrefix() {
        return $this->_prefix;
    }
    
    /**
     * Set the log level
     * 
     * @param int $level The new log level
     */
    public function setLogLevel($level) {
        $this->_logLevel = $level;
    }
    
    /**
     * Get the current log level
     * 
     * @return int The current log level
     */
    public function getLogLevel() {
        return $this->_logLevel;
    }
}
