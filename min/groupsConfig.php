<?php
/**
 * Groups configuration for default Minify implementation
 * @package Minify
 */

/** 
 * You may wish to use the Minify URI Builder app to suggest
 * changes. http://yourdomain/min/builder/
 *
 * See http://code.google.com/p/minify/wiki/CustomSource for other ideas
 **/

$prefix = dirname(__DIR__);
return array(
    // 'js' => array($prefix . '/js/file1.js', $prefix . '/js/file2.js'),
    // 'css' => array($prefix . '/css/file1.css', $prefix . '/css/file2.css'),
	  'js.js' => array(
				$prefix . '/ext/jquery/jquery-3.1.1.min.js',
				$prefix . '/ext/jquery/ui/jquery-ui.min.js',
                // $prefix . '/ext/jquery/superfish/js/jquery.hoverIntent.minified.js', // File not found - commented out
                $prefix . '/ext/jquery/superfish/js/superfish.js',
                $prefix . '/ext/jquery/superfish/js/supersubs.js',
                $prefix . '/ext/bootstrap/js/bootstrap.min.js',
                $prefix . '/ext/jquery/cookie.js',
				$prefix . '/includes/misc.js',
				$prefix . '/ext/liteyoutubejsModule/lite-yt-embed.js'
      ),
      'css.css' => array(
				$prefix . '/ext/bootstrap/css/bootstrap.min.css',
				$prefix . '/ext/css/offCanvas.css',
                $prefix . '/ext/font-awesome/4.6.1/css/font-awesome.min.css', 
                $prefix . '/ext/jquery/superfish/css/superfish.css', 
                $prefix . '/ext/jquery/superfish/css/superfish-vertical.css', 
                $prefix . '/custom.css', 
                $prefix . '/user.css',
				$prefix . '/ext/liteyoutubejsModule/lite-yt-embed.css'
      ),
      'admin.js' => array( 
                $prefix . '/ext/jquery/jquery-2.2.3.min.js',
                $prefix . '/ext/jquery/ui/jquery-ui-1.10.4.min.js',
                $prefix . '/ext/flot/jquery.flot.min.js',
                $prefix . '/ext/flot/jquery.flot.time.min.js',
                $prefix . '/ext/bootstrap/js/bootstrap.min.js',
                $prefix . '/baffletrain/autocadlt/includes/general.js',				
                // $prefix . '/baffletrain/autocadlt/includes/misc.js', // File not found - commented out
                $prefix . '/baffletrain/autocadlt/ext/clipboard.min.js'
				
      ),
        'admin.css' => array(  
				$prefix . '/ext/jquery/ui/redmond/jquery-ui-1.10.4.min.css',				
				$prefix . '/ext/bootstrap/css/bootstrap.min.css',
                $prefix . '/baffletrain/autocadlt/includes/stylesheet.css'
                
      )           
     );