<?php
/**
 * Local Database Import Script
 * 
 * This script helps import your database export into the local development environment.
 * Run this from command line: php database_import_local.php filename.sql
 */

// Check if running from command line
if (php_sapi_name() !== 'cli') {
    die('This script must be run from command line for security reasons.');
}

// Check if SQL file is provided
if ($argc < 2) {
    echo "Usage: php database_import_local.php <sql_file>\n";
    echo "Example: php database_import_local.php database_export_2024-01-15_10-30-00.sql\n";
    exit(1);
}

$sql_file = $argv[1];

// Check if file exists
if (!file_exists($sql_file)) {
    echo "Error: SQL file '$sql_file' not found.\n";
    exit(1);
}

// Local database configuration
$host = '127.0.0.1';
$username = 'root';
$password = ''; // XAMPP default
$database = 'cadservices_local';

echo "Local Database Import Script\n";
echo "============================\n";
echo "Host: $host\n";
echo "Database: $database\n";
echo "SQL File: $sql_file\n";
echo "File Size: " . formatBytes(filesize($sql_file)) . "\n\n";

try {
    // Connect to MySQL server (not specific database first)
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Connected to MySQL server.\n";
    
    // Create database if it doesn't exist
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "Database '$database' created/verified.\n";
    
    // Select the database
    $pdo->exec("USE `$database`");
    echo "Using database '$database'.\n\n";
    
    // Read and execute SQL file
    echo "Reading SQL file...\n";
    $sql_content = file_get_contents($sql_file);
    
    if ($sql_content === false) {
        throw new Exception("Could not read SQL file.");
    }
    
    // Split SQL into individual statements
    $statements = explode(';', $sql_content);
    $executed = 0;
    $errors = 0;
    
    echo "Executing SQL statements...\n";
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        
        // Skip empty statements and comments
        if (empty($statement) || substr($statement, 0, 2) === '--' || substr($statement, 0, 2) === '/*') {
            continue;
        }
        
        try {
            $pdo->exec($statement);
            $executed++;
            
            // Show progress every 100 statements
            if ($executed % 100 === 0) {
                echo "Executed $executed statements...\n";
            }
            
        } catch (PDOException $e) {
            $errors++;
            echo "Warning: Error executing statement (continuing): " . $e->getMessage() . "\n";
            
            // Stop if too many errors
            if ($errors > 10) {
                throw new Exception("Too many errors encountered. Stopping import.");
            }
        }
    }
    
    echo "\nImport completed!\n";
    echo "Statements executed: $executed\n";
    echo "Errors encountered: $errors\n\n";
    
    // Show table count
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    echo "Tables in database: " . count($tables) . "\n";
    
    if (count($tables) > 0) {
        echo "Table list:\n";
        foreach ($tables as $table) {
            $count = $pdo->query("SELECT COUNT(*) FROM `$table`")->fetchColumn();
            echo "  - $table ($count rows)\n";
        }
    }
    
    echo "\nDatabase import successful!\n";
    echo "You can now test your local application at: https://localhost.cadservices\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}

function formatBytes($size, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    
    return round($size, $precision) . ' ' . $units[$i];
}
?>
