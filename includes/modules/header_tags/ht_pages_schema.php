<?php
/*
  $Id$

  osCommerce,
 Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2015 osCommerce

  Released under the GNU General Public License
*/

  class ht_pages_schema {
    var $code = 'ht_pages_schema';
    var $group = 'header_tags';
    var $title;
    var $description;
    var $sort_order;
    var $enabled = false;

    function __construct() {
      $this->title = MODULE_HEADER_TAGS_PAGES_SCHEMA_TITLE;
      $this->description = MODULE_HEADER_TAGS_PAGES_SCHEMA_DESCRIPTION;
      $this->description .= '<div class="secWarning">' . MODULE_HEADER_TAGS_PAGES_SCHEMA_HELPER . '</div>';

      if ( defined('MODULE_HEADER_TAGS_PAGES_SCHEMA_STATUS') ) {
        $this->sort_order = MODULE_HEADER_TAGS_PAGES_SCHEMA_SORT_ORDER;
        $this->enabled = (MODULE_HEADER_TAGS_PAGES_SCHEMA_STATUS == 'True');
      }
    }

    function execute() {
      global $oscTemplate;

    $output = '<script type="application/ld+json">{"@context":"http://schema.org","@type":"WebSite","@id":"#website","url":"' . MODULE_HEADER_TAGS_PAGES_SCHEMA_URL . '","name":"' . MODULE_HEADER_TAGS_PAGES_SCHEMA_COMPANY_NAME . '","potentialAction":{"@type":"SearchAction","target":"' . MODULE_HEADER_TAGS_PAGES_SCHEMA_SEARCH_URL . '","query-input":"required name=search_term_string"}}</script><script type="application/ld+json">{"@context":"http://schema.org","@type":"Organization","url":"' . MODULE_HEADER_TAGS_PAGES_SCHEMA_URL . '","sameAs":["' . MODULE_HEADER_TAGS_PAGES_SCHEMA_FACEBOOK . '","' . MODULE_HEADER_TAGS_PAGES_SCHEMA_TWITTER . '","' . MODULE_HEADER_TAGS_PAGES_SCHEMA_LINKEDIN . '","' . MODULE_HEADER_TAGS_PAGES_SCHEMA_GOOGLEPLUS . '"],"@id":"#organization","name":"' . MODULE_HEADER_TAGS_PAGES_SCHEMA_COMPANY_NAME . '","logo":"' . MODULE_HEADER_TAGS_PAGES_SCHEMA_LOGO . '","contactPoint": [{"@type": "ContactPoint","telephone": "'. MODULE_HEADER_TAGS_PAGES_SCHEMA_PHONE .'","contactType": ["sales","customer support"]}]}</script>';
		  $oscTemplate->addBlock($output, $this->group);
	}
  
 

    function isEnabled() {
      return $this->enabled;
    }

    function check() {
      return defined('MODULE_HEADER_TAGS_PAGES_SCHEMA_STATUS');
    }

    function install() {
       tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Enable Pages SEO Module', 'MODULE_HEADER_TAGS_PAGES_SCHEMA_STATUS', 'True', 'Do you want to allow this module to write SEO to your Pages?', '6', '1', 'tep_cfg_select_option(array(\'True\', \'False\'), ', now())");
       tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, date_added) values ('Sort Order', 'MODULE_HEADER_TAGS_PAGES_SCHEMA_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', now())");
	   tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, date_added) values ('Company Name', 'MODULE_HEADER_TAGS_PAGES_SCHEMA_COMPANY_NAME', 'TCS CAD and BIM Solutions Ltd', 'Company Name', '6', '0', now())");
       tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, date_added) values ('Site URL', 'MODULE_HEADER_TAGS_PAGES_SCHEMA_URL', 'http://www.cadservices.co.uk', 'Site URL', '6', '0', now())");
	   tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, date_added) values ('Search URL', 'MODULE_HEADER_TAGS_PAGES_SCHEMA_SEARCH_URL', 'https://www.cadservices.co.uk/advanced_search_result.php?keywords=\{search_term_string\}', 'Search url with \{search_term_string\} in place of keywords (search the site with the search term {search_term_string} and post the URL here making sure the chars havent been escaped)', '6', '0', now())");
	   tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, date_added) values ('Facebook URL', 'MODULE_HEADER_TAGS_PAGES_SCHEMA_FACEBOOK', 'https://www.facebook.com/TCSCAD', 'Facebook URL', '6', '0', now())");
	   tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, date_added) values ('Twitter URL', 'MODULE_HEADER_TAGS_PAGES_SCHEMA_TWITTER', 'https://twitter.com/tcscad', 'Twitter URL', '6', '0', now())");
	   tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, date_added) values ('Linkedin URL', 'MODULE_HEADER_TAGS_PAGES_SCHEMA_LINKEDIN', 'https://www.linkedin.com/company/18335061', 'Linkedin URL', '6', '0', now())");
	   tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, date_added) values ('GooglePlus URL', 'MODULE_HEADER_TAGS_PAGES_SCHEMA_GOOGLEPLUS', 'https://plus.google.com/+CadservicesCoUk', 'GooglePlus URL', '6', '0', now())");
	   tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, date_added) values ('Site Logo', 'MODULE_HEADER_TAGS_PAGES_SCHEMA_LOGO', 'http://www.cadservices.co.uk/images/TCS_dark.jpg', 'Path to company LOGO', '6', '0', now())");
	   tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, date_added) values ('Site Logo', 'MODULE_HEADER_TAGS_PAGES_SCHEMA_PHONE', '+44-01642-677582', 'Phone Number', '6', '0', now())");

	}
	
    function remove() {
      tep_db_query("delete from configuration where configuration_key in ('" . implode("', '", $this->keys()) . "')");
    }

    function keys() {
      return array('MODULE_HEADER_TAGS_PAGES_SCHEMA_STATUS', 'MODULE_HEADER_TAGS_PAGES_SCHEMA_SORT_ORDER', 'MODULE_HEADER_TAGS_PAGES_SCHEMA_URL', 'MODULE_HEADER_TAGS_PAGES_SCHEMA_COMPANY_NAME', 'MODULE_HEADER_TAGS_PAGES_SCHEMA_SEARCH_URL', 'MODULE_HEADER_TAGS_PAGES_SCHEMA_URL', 'MODULE_HEADER_TAGS_PAGES_SCHEMA_FACEBOOK', 'MODULE_HEADER_TAGS_PAGES_SCHEMA_TWITTER','MODULE_HEADER_TAGS_PAGES_SCHEMA_LINKEDIN', 'MODULE_HEADER_TAGS_PAGES_SCHEMA_GOOGLEPLUS', 'MODULE_HEADER_TAGS_PAGES_SCHEMA_COMPANY_NAME', 'MODULE_HEADER_TAGS_PAGES_SCHEMA_LOGO','MODULE_HEADER_TAGS_PAGES_SCHEMA_PHONE');
    }
  }
  
