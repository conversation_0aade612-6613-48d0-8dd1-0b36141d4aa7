<?php
/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2016 osCommerce

  Released under the GNU General Public License
*/

  class nb_shopping_cart {
    var $code = 'nb_shopping_cart';
    var $group = 'navbar_modules_right';
    var $title;
    var $description;
    var $sort_order;
    var $enabled = false;    
    
    function __construct() {
      $this->title = MODULE_NAVBAR_SHOPPING_CART_TITLE;
      $this->description = MODULE_NAVBAR_SHOPPING_CART_DESCRIPTION;

      if ( defined('MODULE_NAVBAR_SHOPPING_CART_STATUS') ) {
        $this->sort_order = MODULE_NAVBAR_SHOPPING_CART_SORT_ORDER;
        $this->enabled = (MODULE_NAVBAR_SHOPPING_CART_STATUS == 'True');
        
        switch (MODULE_NAVBAR_SHOPPING_CART_CONTENT_PLACEMENT) {
          case 'Home':
          $this->group = 'navbar_modules_home';
          break;
          case 'Left':
          $this->group = 'navbar_modules_left';
          break;
          case 'Right':
          $this->group = 'navbar_modules_right';
          break;
        } 
      }
    }
    public static function build_cart_dropdown(  ) {
    global $cart, $currencies;

      $products = $cart->get_products();
      $cart_entries = '';
      foreach ($products as $k => $v) {
        $products_attributes = new tcs_product_attributes($v['id'],1);
        $attribute_string = "{" . explode("{",$v['id'],2)[1];
        $cart_entries .= '<li><a href="' . tep_href_link('shopping_cart.php') . '">' . $v['quantity'] . ' x ' . $v['name'] . $products_attributes->generate_product_suffix($attribute_string) .'</a></li>';      }
      ob_start();
      require('includes/modules/navbar_modules/templates/shopping_cart.php');
      return ob_get_clean();
    }

    function getOutput() {
      global $oscTemplate;

        $data = self::build_cart_dropdown();

      $oscTemplate->addBlock($data, $this->group);
    }

    function isEnabled() {
      return $this->enabled;
    }

    function check() {
      return defined('MODULE_NAVBAR_SHOPPING_CART_STATUS');
    }

    function install() {
      tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Enable Shopping Cart Module', 'MODULE_NAVBAR_SHOPPING_CART_STATUS', 'True', 'Do you want to add the module to your Navbar?', '6', '1', 'tep_cfg_select_option(array(\'True\', \'False\'), ', now())");
      tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Content Placement', 'MODULE_NAVBAR_SHOPPING_CART_CONTENT_PLACEMENT', 'Right', 'Should the module be loaded in the Left or Right or the Home area of the Navbar?', '6', '1', 'tep_cfg_select_option(array(\'Left\', \'Right\', \'Home\'), ', now())");
      tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, date_added) values ('Sort Order', 'MODULE_NAVBAR_SHOPPING_CART_SORT_ORDER', '550', 'Sort order of display. Lowest is displayed first.', '6', '0', now())");
    }

    function remove() {
      tep_db_query("delete from configuration where configuration_key in ('" . implode("', '", $this->keys()) . "')");
    }

    function keys() {
      return array('MODULE_NAVBAR_SHOPPING_CART_STATUS', 'MODULE_NAVBAR_SHOPPING_CART_CONTENT_PLACEMENT', 'MODULE_NAVBAR_SHOPPING_CART_SORT_ORDER');
    }
  }
  