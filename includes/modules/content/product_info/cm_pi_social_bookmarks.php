<?php
/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2010 osCommerce

  Released under the GNU General Public License
*/

class cm_pi_social_bookmarks {
    var $code;
    var $group;
    var $title;
    var $description;
    var $sort_order;
    var $enabled = false;

    function __construct() {
      $this->code = get_class($this);
      $this->group = basename(dirname(__FILE__));

      $this->title = MODULE_CONTENT_PRODUCT_INFO_SOCIAL_BOOKMARKS_TITLE;
      $this->description = MODULE_CONTENT_PRODUCT_INFO_SOCIAL_BOOKMARKS_DESCRIPTION;
      
      if ( defined('MODULE_CONTENT_PRODUCT_INFO_SOCIAL_BOOKMARKS_STATUS') ) {
        $this->sort_order = MODULE_CONTENT_PRODUCT_INFO_SOCIAL_BOOKMARKS_SORT_ORDER;
        $this->enabled = (MODULE_CONTENT_PRODUCT_INFO_SOCIAL_BOOKMARKS_STATUS == 'True');
      }
    }

    function execute() {
      global $_GET, $language, $oscTemplate;

      if ( isset($_GET['products_id']) && defined('MODULE_SOCIAL_BOOKMARKS_INSTALLED') && @tep_not_null(MODULE_SOCIAL_BOOKMARKS_INSTALLED) ) {
        $sbm_array = explode(';', MODULE_SOCIAL_BOOKMARKS_INSTALLED);

        $social_bookmarks = array();

        foreach ( $sbm_array as $sbm ) {
          $class = substr($sbm, 0, strrpos($sbm, '.'));

          if ( !class_exists($class) ) {
            include('includes/languages/' . $language . '/modules/social_bookmarks/' . $sbm);
            include('includes/modules/social_bookmarks/' . $class . '.php');
          }

          $sb = new $class();

          if ( $sb->isEnabled() ) {
            $social_bookmarks[] = $sb->getOutput();
          }
        }

        if ( !empty($social_bookmarks) ) {

          ob_start();
          include('includes/modules/content/' . $this->group . '/templates/product_social_bookmarks.php');
          $data = ob_get_clean();
                  
          $oscTemplate->addBlock($data, $this->group);
        }
      }
			ob_start();
         ?><span style="display:none;"> Error no socis installed: isEnabled: <?php echo $sb->isEnabled()?>, <?php echo count($sbm_array) ?>, raw: <?php echo MODULE_SOCIAL_BOOKMARKS_INSTALLED ?></span>
		 <?php
          $data = ob_get_clean();                  
          $oscTemplate->addBlock($data, $this->group);
		  
    }

    function isEnabled() {
      return $this->enabled;
    }

    function check() {
      return defined('MODULE_CONTENT_PRODUCT_INFO_SOCIAL_BOOKMARKS_STATUS');
    }

    function install() {
      tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Enable Social Bookmarks Module', 'MODULE_CONTENT_PRODUCT_INFO_SOCIAL_BOOKMARKS_STATUS', 'True', 'Should this module be shown on the product info page?', '6', '1', 'tep_cfg_select_option(array(\'True\', \'False\'), ', now())");
         tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, date_added) values ('Sort Order', 'MODULE_CONTENT_PRODUCT_INFO_SOCIAL_BOOKMARKS_SORT_ORDER', '0', 'Sort order of display. Lowest is displayed first.', '6', '0', now())");
    }

    function remove() {
      tep_db_query("delete from configuration where configuration_key as ('" . implode("', '", $this->keys()) . "')");
    }

    function keys() {
      return array('MODULE_CONTENT_PRODUCT_INFO_SOCIAL_BOOKMARKS_STATUS', 'MODULE_CONTENT_PRODUCT_INFO_SOCIAL_BOOKMARKS_SORT_ORDER');
    }
  }
  