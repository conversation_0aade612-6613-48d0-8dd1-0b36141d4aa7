<?php
/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2017 osCommerce

  Released under the GNU General Public License
<h3>Variations available</h3>
*/
?>
<div class="variationsBottomWrapper">
	<div class="variationsWrapper">
		<table id="variationModule-<?php echo $variation['variations_table_id'] ?>" cellspacing="3" cellpadding="3" class="table table-striped table-hover table-condensed small" width="100%">
			<thead class="thead-dark">
				<tr>
				  <th>Model</th>
				  <th>Product</th>
				  <th class="text-center">Price</th>
				 
				</tr>
			 </thead>
				<tbody id="variationTableBody-<?php echo $variation['products_variations_id'] ?>">
				<?php
			foreach ($variations as $i => $variation) { ?>
				<tr>
				   <td nowrap><?php echo $variation['model'] ?></td>
				   <td><a itemprop="url" href="<?php echo tep_href_link('product_info.php', 'products_id=' . (int)$_GET['products_id'] . $variation['attribute_string'] ); ?>"><span itemprop="name"><?php echo substr($variation['product_name_suffix'],2) ?></span></a><meta itemprop="position" content="<?php echo (int)$position; ?>" /></td>
				   <td class="text-center"><?php
			  if ($variation['price'] > 0) {
				if (@tep_not_null($variation['specials_new_price'])) {?>
				  <div class="" itemprop="offers" itemscope itemtype="http://schema.org/Offer"><meta itemprop="priceCurrency" content="<?php echo tep_output_string($currency)?>/><del><?php echo $currencies->display_price($variation['price'], tep_get_tax_rate($variation['products_tax_class_id']));?></del>&nbsp;&nbsp;<span class="productSpecialPrice" itemprop="price" content="'<?php echo $currencies->display_raw($variation['price'], tep_get_tax_rate($variation['products_tax_class_id']))?>"><?php echo $currencies->display_price($variation['specials_new_price'], tep_get_tax_rate($variation['products_tax_class_id']))?></span></div>
				<?php } else { ?>
				  <div class="" itemprop="offers" itemscope itemtype="http://schema.org/Offer"><meta itemprop="priceCurrency" content="<?php echo tep_output_string($currency)?>" /><span itemprop="price" content="<?php echo  $currencies->display_raw($variation['price'], tep_get_tax_rate($variation['products_tax_class_id']))?>"><?php echo $currencies->display_price($variation['price'], tep_get_tax_rate($variation['products_tax_class_id']))?></span></div>
					<?php }
					}
				   ?>
				   </td>
				</tr>      
			  <?php
			}
			?>    
			</tbody>
		</table> 
	</div>
</div>