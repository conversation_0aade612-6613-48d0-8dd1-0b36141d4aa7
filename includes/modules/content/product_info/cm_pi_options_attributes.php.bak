<?php
/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2018 osCommerce

  Released under the GNU General Public License
*/

class cm_pi_options_attributes
{
    var $code;
    var $group;
    var $title;
    var $description;
    var $sort_order;
    var $enabled = false;

    function __construct()
    {
        $this->code = get_class($this);
        $this->group = basename(dirname(__FILE__));

        $this->title = MODULE_CONTENT_PI_OA_TITLE;
        $this->description = MODULE_CONTENT_PI_OA_DESCRIPTION;
        $this->description .= '<div class="secWarning">' . MODULE_CONTENT_BOOTSTRAP_ROW_DESCRIPTION . '</div>';

        if (defined('MODULE_CONTENT_PI_OA_STATUS')) {
            $this->sort_order = MODULE_CONTENT_PI_OA_SORT_ORDER;
            $this->enabled = (MODULE_CONTENT_PI_OA_STATUS == 'True');
        }
    }




    public static function build_attribute_selector($languages_id, $currencies, $product_info, $products_attributes)  {

     //   print_rr($products_attributes);
           if ($products_attributes->has_attributes) {
            $content_width = (int)MODULE_CONTENT_PI_OA_CONTENT_WIDTH;
            $options_output = null;
            $attributes = $products_attributes->attributes;
            $selected_attributes = [];
            foreach ($attributes as $aKey => $products_options) {
                $products_options_array = array();
                $fr_input = $fr_required = $fr_feedback = null;
                if (is_string($_GET['products_id']) && isset($cart->contents[$_GET['products_id']]['attributes'][$aKey])) {
                    $selected_attribute = $cart->contents[$_GET['products_id']]['attributes'][$aKey];
                } else {
                    $selected_attribute = false;
                }
                $select_output_select = '<div class="form-group prod_attribute_group ' . $fr_feedback . '">
                                            <label for="input_' . $aKey . '" class="control-label col-sm-3">' . $products_options['products_options_name'] . '</label>
                                                <div class="col-sm-9 attributeWrapper">';
                $buttons_output = '<div id="attributes_values_id_btns_container" class="attributes_values_id_btns_container btn-group" data-optionid="' . $aKey . '" >';

                $option_selected = false;
                $select_output_options = "";
                $count = 0;
                $selected_item_hidden = false;
                $default_hidden = false;
                $calculated_dependencies = self::check_dependencies($products_attributes);
                $selected_attributes = $calculated_dependencies['selected_attributes'];
                foreach ($calculated_dependencies['options'][$aKey] as $vKey => $values) {
                    $selected_item  = false;
                    if (isset($selected_attributes[$aKey][$vKey])) {
                        $selected_item = true;
                    }
                    if ($selected_item) {
                        $selected_button = 'active btn-success';
                        $select_output_options .= '
                            <input type="hidden" name="attributes[' . $aKey . ']" value="' . $vKey . '">';

                    } else {
                        $selected_button = '';
                    }

                    if (count($products_options['values']) > 1) {
                        $buttons_output .= '
                        <button type="button" onclick=\'this.classList.add("active", "btn-success");\'  hx-post="api.php?getNewData='. $product_info['products_id'] . '" hx-target="#cm-pi-options-attributes" hx-swap="outerHTML" hx-vals=\'' . json_encode(["selected_attribute" => [$aKey => $vKey]]) . '\' id="product_attribute_btns_' . $aKey . '_' . $vKey . '" class="btn btn-default product_attribute_btns ' . $selected_button . '">' . $values['products_options_values_name'] .'</button>';
                    } else {
                        $buttons_output .= '
                        <button type="button" onclick=\'this.classList.add("active", "btn-success");\'  hx-post="api.php?getNewData='. $product_info['products_id'] . '" hx-target="#cm-pi-options-attributes"  hx-swap="outerHTML"  hx-vals=\'' . json_encode(["selected_attribute" => [$aKey => $vKey]]) . '\' id="product_attribute_btns_' . $aKey . '_' . $vKey . '" class="btn_debuttoned">' . $values['products_options_values_name'] . '</button>';
                    }
                } $select_output = $select_output_select . $select_output_options;
                $buttons_output .= "</div>
					</div>
				</div>";
                $buttons_output .= "";

                $options_output .= $select_output . $buttons_output;
            }

               print_rr($options_output, 'oppyouy');
            ob_start();
            include('includes/modules/content/product_info/templates/tpl_cm_pi_options_attributes.php');

            return ['html' => ob_get_clean(), 'selected_attributes' => $selected_attributes,'last_count' => $calculated_dependencies['count']];
        }
        return ['html' => '', 'selected_attributes' => []];
    }

    function execute() {
        global $oscTemplate, $languages_id, $currencies, $product_info, $products_attributes;
        //print_rr($products_attributes);
        $selector = $this->build_attribute_selector($languages_id, $currencies, $product_info, $products_attributes);
        $template = $selector['html'];
        // Update the products_attributes object with the selected attributes from the selector
        $products_attributes->selected_attributes = $selector['selected_attributes'];
        $oscTemplate->addContent($template, $this->group);
    }

    function isEnabled()
    {
        return $this->enabled;
    }

    function check()
    {
        return defined('MODULE_CONTENT_PI_OA_STATUS');
    }

    function install()
    {
        tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Enable Options & Attributes', 'MODULE_CONTENT_PI_OA_STATUS', 'True', 'Should this module be shown on the product info page?', '6', '1', 'tep_cfg_select_option(array(\'True\', \'False\'), ', now())");
        tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Content Width', 'MODULE_CONTENT_PI_OA_CONTENT_WIDTH', '12', 'What width container should the content be shown in?', '6', '1', 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), ', now())");
        tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Add Helper Text', 'MODULE_CONTENT_PI_OA_HELPER', 'True', 'Should first option in dropdown be Helper Text?', '6', '1', 'tep_cfg_select_option(array(\'True\', \'False\'), ', now())");
        tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Enforce Selection', 'MODULE_CONTENT_PI_OA_ENFORCE', 'True', 'Should customer be forced to select option(s)?', '6', '1', 'tep_cfg_select_option(array(\'True\', \'False\'), ', now())");
        tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, date_added) values ('Sort Order', 'MODULE_CONTENT_PI_OA_SORT_ORDER', '80', 'Sort order of display. Lowest is displayed first.', '6', '0', now())");
    }

    function remove()
    {
        tep_db_query("delete from configuration where configuration_key in ('" . implode("', '", $this->keys()) . "')");
    }

    function keys()
    {
        return array('MODULE_CONTENT_PI_OA_STATUS', 'MODULE_CONTENT_PI_OA_CONTENT_WIDTH', 'MODULE_CONTENT_PI_OA_HELPER', 'MODULE_CONTENT_PI_OA_ENFORCE', 'MODULE_CONTENT_PI_OA_SORT_ORDER');
    }

    private static function check_dependencies($products_attributes) {
        $attributes = $products_attributes->attributes;
        $selected_attributes = $products_attributes->selected_attributes;
        $result = [];
        $changes_made = true;
        $issues = []; // Track dependency issues

        // Initial copy of all values
        foreach ($attributes as $aKey => $option) {
            $result[$aKey] = $option['values'];
        }
        
        // Keep processing until no more changes are made
        $count = 0;
        $final_pass = false;
        while ($changes_made && $count < 10) { // Add safety limit to prevent infinite loops
            $changes_made = false;
            $count++;
            
            // First pass: Hide buttons that depend on unselected buttons
            foreach ($attributes as $aKey => $option) {
                foreach ($option['values'] as $vKey => $value) {
                    //if ($aKey == 13 && $vKey == 242) print_rr($value, 'value');

                    $dependson_options_id = $value['dependson_options_id'];
                    $dependson_values_ids = explode(',', $value['dependson_options_values_id']);

                    // if there are partial or no dependencies, skip
                    if (!is_numeric($dependson_options_id) ||
                        $value['dependson_options_id'] < 1 ||
                        !is_numeric($dependson_values_ids[0]) ||
                        $value['dependson_options_values_id'] < 1
                    ) continue;

                    // Skip if already removed
                    if (!isset($result[$aKey][$vKey])) continue;
                    
                    // Check if the dependency option exists
                    if (!isset($attributes[$dependson_options_id])) {
                        $issues[] = "Attribute {$option['products_options_name']}: {$value['products_options_values_name']} depends on missing option ID: $dependson_options_id";
                        continue;
                    }
                    
                    // Check if all dependency values exist
                    $all_values_exist = true;
                    foreach ($dependson_values_ids as $dep_value_id) {
                        $dep_value_id = trim($dep_value_id);
                        if (!isset($attributes[$dependson_options_id]['values'][$dep_value_id])) {
                            $all_values_exist = false;
                            $issues[] = "Attribute {$option['products_options_name']}: {$value['products_options_values_name']} depends on missing value ID: $dep_value_id";
                        }
                    }
                    
                    // Check if any of the dependencies are satisfied
                    $dependency_satisfied = false;

                    // Check if the dependent option is selected
                    if (isset($selected_attributes[$dependson_options_id])) {
                        // Check if any of the dependent values are selected
                        foreach ($dependson_values_ids as $dependson_value_id) {
                            $dependson_value_id = trim($dependson_value_id);
                            if (isset($selected_attributes[$dependson_options_id][$dependson_value_id])) {
                                $dependency_satisfied = true;
                                break;
                            }
                        }
                    }

                    // If dependency not satisfied, remove this value
                    if (!$dependency_satisfied) {
                        unset($result[$aKey][$vKey]);

                        // If this was a selected value, we need to select another one
                        if (isset($selected_attributes[$aKey][$vKey])) {
                            unset($selected_attributes[$aKey][$vKey]);
                            $changes_made = true;
                        }
                    }
                }
            }

            // Second pass: Check for circular dependencies
            $dependency_graph = [];
            foreach ($attributes as $aKey => $option) {
                foreach ($option['values'] as $vKey => $value) {
                    if (is_numeric($value['dependson_options_id']) && $value['dependson_options_id'] > 0) {
                        $dependency_graph["$aKey-$vKey"] = [];
                        $dependson_options_id = $value['dependson_options_id'];
                        $dependson_values_ids = explode(',', $value['dependson_options_values_id']);
                        
                        foreach ($dependson_values_ids as $dep_value_id) {
                            $dep_value_id = trim($dep_value_id);
                            $dependency_graph["$aKey-$vKey"][] = "$dependson_options_id-$dep_value_id";
                        }
                    }
                }
            }
            
            // Detect cycles in the dependency graph
            foreach ($dependency_graph as $start_node => $deps) {
                $visited = [];
                $path = [$start_node];
                if (self::has_cycle($dependency_graph, $start_node, $visited, $path)) {
                    list($aKey, $vKey) = explode('-', $start_node);
                    if (isset($result[$aKey][$vKey])) {
                        $issues[] = "Circular dependency detected for {$attributes[$aKey]['products_options_name']}: {$attributes[$aKey]['values'][$vKey]['products_options_values_name']}";
                        unset($result[$aKey][$vKey]);
                        
                        // If this was a selected value, we need to select another one
                        if (isset($selected_attributes[$aKey][$vKey])) {
                            unset($selected_attributes[$aKey][$vKey]);
                            $changes_made = true;
                        }
                    }
                }
            }

            // Third pass: If a hidden button was selected, select a new one
            foreach ($attributes as $aKey => $option) {
                // If this option had a selection but it's now gone
                if (isset($selected_attributes[$aKey]) && count($selected_attributes[$aKey]) > 0) {
                    $has_valid_selection = false;
                    foreach ($selected_attributes[$aKey] as $vKey => $value) {
                        if (isset($result[$aKey][$vKey])) {
                            $has_valid_selection = true;
                            break;
                        }
                    }

                    // If no valid selection, find a new one
                    if (!$has_valid_selection) {
                        // Clear current selection
                        $selected_attributes[$aKey] = [];

                        // Try to select default if available
                        $default_selected = false;
                        foreach ($result[$aKey] as $vKey => $value) {
                            if ($value['attribute_default'] == 1) {
                                $selected_attributes[$aKey][$vKey] = $value;
                                $default_selected = true;
                                $changes_made = true;
                                break;
                            }
                        }

                        // If no default, select first available
                        if (!$default_selected && !empty($result[$aKey])) {
                            $first_key = array_key_first($result[$aKey]);
                            $selected_attributes[$aKey][$first_key] = $result[$aKey][$first_key];
                            $changes_made = true;
                        }
                    }
                }
                // If this option has no selection but should have one
                else if (empty($selected_attributes[$aKey]) && !empty($result[$aKey])) {
                    // Try to select default if available
                    $default_selected = false;
                    foreach ($result[$aKey] as $vKey => $value) {
                        if ($value['attribute_default'] == 1) {
                            $selected_attributes[$aKey][$vKey] = $value;
                            $default_selected = true;
                            $changes_made = true;
                            break;
                        }
                    }

                    // If no default, select first available
                    if (!$default_selected) {
                        $first_key = array_key_first($result[$aKey]);
                        $selected_attributes[$aKey][$first_key] = $result[$aKey][$first_key];
                        $changes_made = true;
                    }
                }
            }
        }

        if (count($issues) > 0) {
            // Log issues for admin or display in debug mode
            if (defined('DEBUG_MODE') && DEBUG_MODE == 'True') {
                print_rr($issues, 'Dependency Issues');
            }
        }

        return ['options' => $result, 'selected_attributes' => $selected_attributes, 'issues' => $issues, 'count' => $count];
    }

    /**
     * Helper method to detect cycles in a dependency graph
     *
     * @param array $graph The dependency graph
     * @param string $current Current node being checked
     * @param array $visited Reference to visited nodes array
     * @param array $path Reference to current path array
     * @return bool True if cycle is detected
     */
    private static function has_cycle($graph, $current, &$visited, &$path) {
        $visited[$current] = true;

        if (isset($graph[$current])) {
            foreach ($graph[$current] as $neighbor) {
                if (!isset($visited[$neighbor])) {
                    $path[] = $neighbor;
                    if (self::has_cycle($graph, $neighbor, $visited, $path)) {
                        return true;
                    }
                    array_pop($path);
                } else if (in_array($neighbor, $path)) {
                    $path[] = $neighbor;
                    return true;
                }
            }
        }

        return false;
    }
}
