<?php
/*
  $Id: $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2014 osCommerce

  Released under the GNU General Public License
*/

  define('MODULE_PAYMENT_SAGE_PAY_FORM_TEXT_TITLE', 'Sage Pay Form');
  define('MODULE_PAYMENT_SAGE_PAY_FORM_TEXT_PUBLIC_TITLE', 'Credit Card or Bank Card (Processed by Sage Pay)');
  define('MODULE_PAYMENT_SAGE_PAY_FORM_TEXT_DESCRIPTION', '<img src="images/icon_info.gif" border="0" />&nbsp;<a href="http://library.oscommerce.com/Package&en&sage_pay&oscom23&form" target="_blank" style="text-decoration: underline; font-weight: bold;">View Online Documentation</a><br /><br /><img src="images/icon_popup.gif" border="0">&nbsp;<a href="https://support.sagepay.com/apply/default.aspx?PartnerID=E194E079-84A9-493C-AB9A-91CB362D3238&PromotionCode=osc3MF" target="_blank" style="text-decoration: underline; font-weight: bold;">Visit Sage Pay Website</a>&nbsp;<a href="javascript:toggleDivBlock(\'sagePayInfo\');">(info)</a><span id="sagePayInfo" style="display: none;"><br /><i>Using the above link to signup at Sage Pay grants osCommerce a small financial bonus for referring a customer.</i></span>');

  define('MODULE_PAYMENT_SAGE_PAY_FORM_ERROR_ADMIN_MCRYPT', 'This module requires Mcrypt to be enabled in PHP and will not load until it has been enabled on this webserver.');
  define('MODULE_PAYMENT_SAGE_PAY_FORM_ERROR_ADMIN_CONFIGURATION', 'This module will not load until the Vendor Login Name and Encryption Password parameters have been configured. Please edit and configure the settings of this module.');

  define('MODULE_PAYMENT_SAGE_PAY_FORM_ERROR_TITLE', 'There has been an error processing your order transaction');
  define('MODULE_PAYMENT_SAGE_PAY_FORM_ERROR_GENERAL', 'Please try again and if problems persist, please try another payment method.');
?>
