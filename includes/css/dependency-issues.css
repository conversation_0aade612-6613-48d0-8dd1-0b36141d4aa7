/**
 * CSS styles for dependency issue display and info modals
 */

/* Info button styling */
.dependency-info-btn {
    margin-left: 8px;
    padding: 2px 6px;
    font-size: 11px;
    line-height: 1.2;
    border-radius: 3px;
    vertical-align: middle;
    cursor: pointer;
    transition: all 0.2s ease;
}

.dependency-info-btn:hover {
    background-color: #31708f;
    border-color: #245269;
    transform: scale(1.05);
}

.dependency-info-btn:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(49, 112, 143, 0.3);
}

/* Modal overlay improvements */
.dependency-modal-overlay {
    backdrop-filter: blur(2px);
    -webkit-backdrop-filter: blur(2px);
}

/* Modal content styling */
.dependency-modal-content {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border: 1px solid #e5e7eb;
}

/* Modal header styling */
.dependency-modal-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 2px solid #dee2e6;
}

.dependency-modal-title {
    color: #2c3e50;
    font-weight: 600;
}

.dependency-modal-title i {
    color: #3498db;
}

/* Modal body styling */
.dependency-modal-body h4 {
    color: #34495e;
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 14px;
}

.dependency-modal-body p,
.dependency-modal-body div {
    line-height: 1.6;
    color: #555;
}

/* Resolution steps styling */
.dependency-modal-body .resolution-steps {
    background-color: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    border-left: 4px solid #28a745;
    margin-top: 8px;
}

/* Alert improvements */
.alert-warning .dependency-info-btn {
    background-color: #337ab7;
    border-color: #2e6da4;
    color: white;
}

/* Issue list styling */
.dependency-issues-list {
    margin-bottom: 0;
}

.dependency-issues-list li {
    margin-bottom: 8px;
    padding: 4px 0;
    border-bottom: 1px solid rgba(0,0,0,0.05);
}

.dependency-issues-list li:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.dependency-issues-list ul {
    margin-top: 8px;
    margin-bottom: 0;
}

.dependency-issues-list ul li {
    margin-bottom: 4px;
    padding: 2px 0;
    border-bottom: none;
}

/* Issue type indicators */
.issue-type-missing_dependency::before {
    content: "🔗 ";
    margin-right: 4px;
}

.issue-type-circular_dependency::before {
    content: "🔄 ";
    margin-right: 4px;
}

.issue-type-sort_order_violation::before {
    content: "📊 ";
    margin-right: 4px;
}

.issue-type-missing_variation::before {
    content: "🎯 ";
    margin-right: 4px;
}

.issue-type-default_conflict::before {
    content: "⚠️ ";
    margin-right: 4px;
}

.issue-type-self_option_group_dependency::before {
    content: "🚫 ";
    margin-right: 4px;
}

.issue-type-variation_missing_attribute::before {
    content: "❌ ";
    margin-right: 4px;
}

.issue-type-variation_duplicate_model::before {
    content: "📋 ";
    margin-right: 4px;
}

.issue-type-variation_missing_autodesk_link::before {
    content: "🔗 ";
    margin-right: 4px;
}

.issue-type-variation_invalid_autodesk_link::before {
    content: "❌ ";
    margin-right: 4px;
}

.issue-type-variation_selection_issue::before {
    content: "⚡ ";
    margin-right: 4px;
}

/* Responsive modal */
@media (max-width: 768px) {
    .dependency-modal-content {
        margin: 20px;
        max-width: calc(100vw - 40px);
    }
    
    .dependency-modal-header,
    .dependency-modal-body,
    .dependency-modal-footer {
        padding: 12px;
    }
    
    .dependency-modal-title {
        font-size: 16px;
    }
}

/* Animation improvements */
.dependency-modal-enter {
    animation: dependencyModalFadeIn 0.3s ease-out;
}

.dependency-modal-leave {
    animation: dependencyModalFadeOut 0.2s ease-in;
}

@keyframes dependencyModalFadeIn {
    from {
        opacity: 0;
        transform: scale(0.95) translateY(-10px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

@keyframes dependencyModalFadeOut {
    from {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
    to {
        opacity: 0;
        transform: scale(0.95) translateY(-10px);
    }
}

/* Focus management */
.dependency-modal-content:focus {
    outline: none;
}

/* Close button styling */
.dependency-modal-close {
    transition: all 0.2s ease;
}

.dependency-modal-close:hover {
    color: #dc3545;
    transform: scale(1.1);
}

/* Footer button styling */
.dependency-modal-footer .btn {
    transition: all 0.2s ease;
}

.dependency-modal-footer .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
