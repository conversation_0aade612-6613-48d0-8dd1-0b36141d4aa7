<?php



/**
 * Generates a tailwind ui card html element with a title, text and optional class and name.
 * @param string $id The id of the card element.
 * @param string $title The title of the card.
 * @param string $text The text content of the card.
 * @param string $class The class of the card element.
 * @param string $name The name of the card element.
 * @return string The generated html.
 */
function tcs_draw_card_html(string $content, string $id = "", string $class = "", string|null $header = "", string|null $footer = "", ) {

    if ($header) {
        $header = "<!-- header --><div id='{$id}_card_header' class='px-4 py-5 sm:px-6'>{$header}</div><!-- header end -->";
    }
    if ($footer){
        $footer = "<!-- footer --><div id='{$id}_card_footer' class='px-4 py-4 sm:px-6 {$class}'>{$footer}</div><!-- footer end -->";
    }
   
    $content = "<!-- card content --><div id='{$id}_card_body'class='overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg'>{$content}</div><!-- card content end -->";
    return "<!-- card --><div id='{$id}_card' class='divide-y divide-gray-200 overflow-hidden rounded-lg bg-white shadow {$class}'>{$header}{$content}{$footer}</div><!-- card end -->";
}

function tcs_draw_card_bootstrap_html(string $content, string|null $class = "", string|null $header = "", string|null $footer = "", ) {
    
    if ($header) $header = "<div class='panel-header'>{$header}</div>";
    if ($footer) $footer = "<div class='panel-footer {$class}'>{$footer}</div>";
    
    $content = $content;
    return "<div class='col-xs-12 col-sm-10'> <div class='panel panel-default {$class}'> {$header}{$content}{$footer}</div></div>";
}

function tcs_draw_panel_html(string $id, string $title, string $text, string $class = '', string $name = '') {
    return " 
        <div class='panel {$class}' id='{$id}'>
          <div class='panel-header'>
            <h3 class='panel-title'>{$title}</h3>
          </div>          
          <div class='panel-body'>
            <p>{$text}</p>
          </div>
          <!-- /.panel-body -->
        </div>
        <!-- /.panel -->";
}

function tcs_draw_well_html(string $text, string $class = '', string $id = '', string $title = '', string $name = '') {
    return " 
        <div class='well {$class}'>
          $text
          <!-- /.well-body -->
        </div>
        <!-- /.well -->";
}

/**
 * Generates an HTML form element with the given parameters.
 * @param string $method The HTTP method to use for the form submission.
 * @param string $endpoint The endpoint to submit the form to.
 * @param string|null $name The name of the form.
 * @param string|null $id The ID of the form.
 * @param string|null $class The class of the form.
 * @param string|null $swap The swap strategy to use for the form submission.
 * @param string|null $target The target element to swap.
 * @param array $vals The values to include in the form submission.
 * @return string The generated HTML form element.
 */
function tcs_draw_form_html(
        string $name = null, 
        string $id = null, 
        string $class = null, 
        array $extra_params = []
    ) {
    $id = ($id != null) ?  ' id="' . $id . '"' : '';
    $name = ($name != null) ?  ' name="' . $name . '"' : '';
    $class = ($class != null) ?  ' class="' . $class . '"' : ''; 
    $extra_params_out = "";
    foreach ($extra_params as $key => $value) {
        $extra_params_out .= is_array($value) ? " {$key}='" . json_encode($value) . "'" : " {$key}='{$value}'";
    }
    return '<form ' . $id . $name . $extra_params_out . '>';

}
function tcs_draw_form_element(array $cfg = [], string|null $lib = null) {
    print_rr($cfg);
    $default = [
            "class" => "",
            "label" => "",
            "type" => "",
            "content" => "",
            "sub_type" => "",
            "name" => "",
            "id" => "",
            "icon" => "",
            "extra_params" => []
    ];
    $extra_params = array_diff_assoc($cfg,$default);
    $cfg = array_merge($default, $cfg);
    return tcs_draw_form_ctrl(
        class: $cfg['class'],
        label: $cfg['label'],
        type: $cfg['type'] ?? '',
        content: $cfg['content'],
        sub_type: $cfg['sub_type'] ?? 'text',
        name: $cfg['name'],
        id: $cfg['id'],
        icon: $cfg['icon'],
        extra_params: $extra_params);
}


function tcs_draw_form_ctrl(
       string $type = 'input',
       string $content = '',
       string $name = '',
       string $id = '',
       string $class = '',
       string $label = '',
       string $sub_type = 'text',
       array $extra_params = [],
       bool $container = true,
       bool $required = false,
       string $icon = '',
       string $placeholder = '',
       string|null $lib = null
       
    ) {
    // set defaults
    
   
    // Set framework-specific classes
    if ($lib === "bootstrap") {
        $container_class = "form-group";
        $label_class = "control-label";
        $classes = [
            "text" => "form-control",
            "input" => "form-control",
            "checkbox" => "form-control",
            "radio" => "form-control",
            "button" => "form-control",
            "select" => "form-control"
        ];
    } else {
        $container_class = "flex items-center";
        $label_class = "block text-sm font-medium leading-6 text-gray-900";
        $classes = [
            "text" => "w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
            "input" => "w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
            "checkbox" => "h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600",
            "radio" => "h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-600",
            "button" => "pointer-events-auto rounded-md px-4 py-2 text-center font-medium shadow-sm ring-1 ring-slate-700/10 hover:bg-slate-50",
            "select" => "mt-2 block w-full rounded-md border-0 py-1.5 pl-3 pr-10 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-indigo-600 sm:text-sm sm:leading-6",
            "textarea" => "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm/6"
         ];
    }
   
    $out['extra_params'] = "";
    $out['icon'] = $icon;
    $out['content'] = "";
    // Handle specific type-based configurations
    switch ($type) {
        case 'button':
            $out['content'] = $content;
            $out['label'] = $label;
            $out['type'] = isset($href) ? 'a' : 'button';
            break;

        case 'select':
            if (isset($content) && is_array($content)) {
                $out['content'] = "";
                foreach ($content as $key => $value) {
                    $selected = $key == ($selected ?? '') ? ' selected' : '';
                    $out['content'] .= "<option value='{$key}'{$selected}>{$value}</option>";
                }
            }
            break;
        case 'input':
            if ($sub_type === "hidden") {
                $container_class = "hidden";
                $out['class'] = " class='hidden'";
            } else {
                $out['type'] = 'input';
                $out['extra_params'] .= " type='{$out['type']}'";
            }
            break;
        default:
            $out['type'] = $type;        
    }
 // Set element class and additional class if provided
    $out['class'] = $classes[$out['type']] ?? $classes[$out['sub_type']];
    $out['class'] .= isset($class) ? " {$class}" : '';
    $out['class'] = " class='{$out['class']}'";

    $out['name'] = isset($name) ? " name='{$name}'" : '';
    $out['id'] = isset($id) ? " id='{$id}'" : " id='{$name}_{$out['type']}'";
    
    // Set label if provided
    if (isset($label)) {
        $out['label'] = "<label class='{$label_class}' for='{$id}'>{$label}</label>";
    }
    // Set name and id attributes
    $out['alpineDispatch'] = "";
    if (isset($alpineDispatch)) {
        $out['alpineDispatch'] = ' @click="$dispatch(\'' . $cfg["alpineDispatch"] . '\')"';
    }

    // Add extra parameters that aren't in $out by default
    foreach ($extra_params as $key => $value) {
        $out['extra_params'] .= is_array($value) ? " {$key}='" . json_encode($value) . "'" : " {$key}='{$value}'";
    }
    $output = "{$out['label']}
                <{$out['type']}{$out['id']}{$out['class']}{$out['name']}{$out['extra_params']}{$out['alpineDispatch']}>
                    {$out['icon']}{$out['content']}
                </{$out['type']}>";

    if ($container){
        $output = "<div class='{$container_class}'>{$output}</div>";
    }
    // Return constructed HTML
    return $output;
}




/**
 * Output a table for administrative purposes with a title, header, body, and footer
 *
 * @param string $title Table title
 * @param string $id Table ID
 * @param string $class Table classes
 * @param array $columns Table column definitions. Each column is an associative array with keys 'name' and 'class'.
 * @param array $dataset Table row data. Each row is an associative array with keys 'id', 'class', and 'content'.
 * @param string $footer Table footer content
 * @return string The rendered table
 */
function tcs_draw_admin_table($title, $id = "", $class = "col", $columns, $rows, $footer = "") {

    $svg_col_sort = "" . 
    "<span class='invisible ml-2 flex-none rounded text-gray-400 group-hover:visible group-focus:visible'>
        <svg class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor' aria-hidden='true'>
        <path fill-rule='evenodd' d='M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z' clip-rule='evenodd' />
        </svg>
    </span>";
    $table_header = "<!-- table -->
    <table id='{$id}_table' class='min-w-full divide-y divide-gray-300 htmx-swapping:opacity-0 transition-opacity duration-1000 {$class}'>
         <thead scope='col' class='bg-gray-50'>
            <tr>";
     
    $default_col_class = "px-3 py-3.5 text-left text-sm font-semibold text-gray-900";
    $default_column = ["name" => "Name", "class" => "", "id" => "", "params" => []];
    foreach ($columns as $column) {
        $column = array_merge($default_column, $column);
        $col_class_override = false;
        extract($column, EXTR_PREFIX_ALL, "col"); // Extract columns into $col_name, $col_class
        $attributes = "";
        if (!empty($col_params) && is_array($col_params)) {
            foreach ($col_params as $key => $value) {
                $attributes .= " {$key}='{$value}'";
            }
        }
        if (!$col_class_override) $col_class = $default_col_class . $col_class;
        $table_header .= "<th class='$col_class' id='{$col_id}' {$attributes}>
        <a href='#' class='group inline-flex'> {$col_name} $svg_col_sort</a></th>";
    }
    $table_header .=  "</tr></thead>";
    $table_body = "<tbody  <tbody class='divide-y divide-gray-200 bg-white' id='{$id}_body'>";
    $table_rows = $rows;
    $table_body .= $table_rows;
    $table_body .= "</tbody>";
    $table_footer = "</table><!-- /table -->";
    $table = $table_header . $table_body . $table_footer;
    return tcs_draw_card_html($table, $title, $footer);
}
function tcs_draw_admin_bootstrap_table($title, $id = "", $class = "", $columns, $rows, $footer = "", $body_only = false) {
    $table_body = "<tbody id='{$id}_body'>";
    $table_rows = $rows;
    $table_body .= $table_rows;
    $table_body .= "</tbody>";

    // If body_only is true, return just the tbody content
    if ($body_only) {
        return $table_body;
    }

    $table_header = "<table class='table table-striped table-draggable' id='variationsTable'><thead scope='col'><tr>";

    $default_col_class = "";
    foreach ($columns as $column) {
        $col_class_override = false;
        extract($column, EXTR_PREFIX_ALL, "col"); // Extract columns into $col_name, $col_class
        if (!empty($col_params) && is_array($col_params)) {
            foreach ($params as $key => $value) {
                $attributes .= " data-{$key}='{$value}'";
            }
        }
        if (!$col_class_override) $col_class = $default_col_class . $col_class;
        $table_header .= "<th class='$col_class' id='{$col_id}' {$col_params}>
        <a href='#' class='group inline-flex'> {$col_name} $svg_col_sort</a></th>";
    }
    $table_header .=  "</tr></thead>";
    $table_footer = "</table>";
    $table = $table_header . $table_body . $table_footer;
    return tcs_draw_card_bootstrap_html($table,null, $title, $footer);
}
/**
 * Outputs a table row for use in an admin table.
 *
 * @param string $row_id The id of the row
 * @param string $row_class The class of the row
 * @param array $row_content The content of the row. Each cell is an associative array with keys 'class', 'id', 'text', 'data', and 'content'.
 * @return string The rendered row
 */
function tcs_draw_admin_bootstrap_table_row($row_id = "", $row_class = "", $row_content = [], $params = []) {

    $params_string = '';
    foreach ($params as $key => $param) {
        $params_string .= " {$key}='{$param}' ";
    }
    $row = "<tr id='{$row_id}' class='{$row_class}' {$params_string}>";


    $info_class = "";
    $title_class = "";
    $action_class = "attributesOptionsDelete listsOptionsDelete";

    foreach ($row_content as $cell) {
        $cell_attributes = "";
        if (!empty($cell['params']) && is_array($cell['params'])) {
            foreach ($params as $key => $value) {
                $cell_attributes .= " {$key}='{$value}'";
            }
        }
        $cell_content = $cell['content'];
        $cell_class = $info_class;
        switch ($cell['type']) {
            case 'info':
                $cell_class = $info_class;
                break;
            case 'title':
                $cell_class = $title_class;
                break;
            case 'action':
                $cell_class = $action_class;
                break;
        }


        if (isset($cell['class_override']) && $cell['class_override']) {
            $cell_class = $cell['class'];
        } else {
            $cell_class .= " {$cell['class']}";
        }
        $row .= "<td class='{$cell_class}' id='{$cell['id']}' {$cell_attributes}>{$cell_content}</td>";
    }
    $row .= '</tr>';
    return $row;
}


// Function to generate a text input
function tcs_autogen_datadisplay_textinput($label, $name, $value) {

    //  <label for="name" class="absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900">Name</label>
    //  <input type="text" name="name" id="name" class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" placeholder="Jane Smith">
  
      return "
      <div class='relative '>
          <label class='absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900' for='{$name}'>{$label}</label>
          <input type='text' id='{$name}' name='{$name}' value='{$value}' readonly
                 class='block w-full rounded-md border-0 pl-1.5 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6'>
      </div>";
  }
  
  // Function to generate a card with collapsible sections for arrays
  function tcs_autogen_datadisplay_card($title, $content, $open = false) {
      return "
      <div x-data='{ open: $open }' class='bg-white shadow-md rounded-lg col-span-4 '>
          <button @click='open = !open' class='w-full px-4 py-2 text-left text-lg font-semibold text-gray-700 hover:bg-gray-100 focus:outline-none'>
              {$title}
              <span x-show='!open' class='ml-2'>+</span>
              <span x-show='open' class='ml-2'>-</span>
          </button>
          <div x-show='open' class='px-4 py-2 gap-4 border-t border-gray-200 grid grid-cols-4'>
              {$content}
          </div>
      </div>";
  }
  
  // Function to generate a form section for associative arrays
  function tcs_autogen_datadisplay_section($data, $sectionTitle, $open = false) {
      $content = '';
      $hasContent = false;
      //print_rr($data, "generate section");
      foreach ($data as $key => $value) {
          if (is_array($value)) {
              $temp_content = tcs_autogen_datadisplay_card(ucwords(str_replace('_', ' ', $key)), tcs_autogen_datadisplay_section($value, $key));
              if (!$temp_content) continue;
              $content .= $temp_content;
              $hasContent = true;
          } else {
              if (empty($value)) continue;            
              $content .= tcs_autogen_datadisplay_textinput(ucwords(str_replace('_', ' ', $key)), $key, $value);            
              $hasContent = true;
          }
      }
      if ($hasContent) return $content; else return false;
  }
  


function tcs_draw_admin_table_row($row_id = "", $row_class = "", $row_content = [], $params = []) {
    $params_string = '';
    if (!empty($params) && is_array($params)) {
        foreach ($params as $key => $param) {
            $params_string .= " {$key}='{$param}' ";
        }
    }
    $row = "<tr id='{$row_id}' class='{$row_class}' {$params_string}>";

    $fst_title_class =      "py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6'";
    $title_class =          "px-3 py-3.5 text-left text-sm font-semibold text-gray-900";
    $fst_info_class =       "whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6";
    $info_class =           "whitespace-nowrap px-3 py-4 text-sm text-gray-500";    
    $action_class =         "relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6";

    $count = 0;
   // print_rr($row_content);
    foreach ($row_content as $cell) {

        $cell_type = $cell['type'] ?? "info";
        $cell_id = $cell['id'] ?? "";
        if ($count == 0)  $cell_type = "fst_{$cell_type}";   
        $count++;

        $cell_attributes = "";
        if (!empty($cell['params']) && is_array($cell['params'])) {
            foreach ($params as $key => $value) {
                $cell_attributes .= " {$key}='{$value}'";
            }
        }
        $cell_content = $cell['content'];
        
        if (isset($cell['class_override']) && $cell['class_override']) {
            $cell_class = $cell['class'];
        } else {
            $cell_class = match($cell_type) {
                'info' => $info_class,
                'title' => $title_class,
                'action' => $action_class,
                'fst_info' => $fst_info_class,
                'fst_title' => $fst_title_class,
                'fst_action' => $action_class,
                'fst_' => $fst_info_class,
                default => $info_class
            };
            $cell_class .= " {$cell['class']}";
        }
        $row .= "<td class='{$cell_class}' id='{$cell_id}' {$cell_attributes}>{$cell_content}</td>";
    }
    $row .= '</tr>';
    return $row;
}
// specific components

function tcs_draw_product_options_select($products_attributes, $products_id) {
    $products_options_name_query = tep_db_query("SELECT DISTINCT popt.products_options_id, popt.products_options_name FROM products_options popt, products_attributes patrib WHERE patrib.products_id='{$products_id}' AND patrib.options_id = popt.products_options_id ORDER BY popt.products_options_name");

    if (tep_db_num_rows($products_options_name_query)) {
        while ($products_options_name = tep_db_fetch_array($products_options_name_query)) {
            $select_output = '<div class="form-group attribute_group form-inline">
                            <label for="input_' . $products_options_name['products_options_id'] . '" class="control-label"> ' . $products_options_name['products_options_name'] . ': </label>
                            <select name="id[' . $products_options_name['products_options_id'] . ']" class="variations_form variationsAttributeSelect">';

            $products_options_query = tep_db_query("SELECT pov.products_options_values_id, pov.products_options_values_name FROM products_options_values pov, products_attributes pa WHERE pa.products_id='{$products_id}' AND pa.options_id='{$products_options_name['products_options_id']}' AND pa.options_values_id=pov.products_options_values_id");

            $select_output_options = '<option disabled selected value>None</option>';
            while ($products_options = tep_db_fetch_array($products_options_query)) {
                $select_output_options .= '<option value="' . $products_options['products_options_values_id'] . '">' . $products_options['products_options_values_name'] . '</option>';
            }
            $select_output .= $select_output_options . '</select></div>';
            return $select_output;
        }
    }
}

function tcs_draw_variations_form($id, $products_id, $values = []) {
    $products_options_name_query = tep_db_query("select distinct popt.products_options_id, popt.products_options_name from products_options popt, products_attributes patrib where patrib.products_id='" . $products_id . "' and patrib.options_id = popt.products_options_id order by popt.products_options_name");
    $select_output_selects = [
        "<common>" => [
            "class" => "variations_form"
        ]
    ];
    if (tep_db_num_rows($products_options_name_query)) {
        while ($products_options_name = tep_db_fetch_array($products_options_name_query)) {
            $products_options_query = tep_db_query("select pov.products_options_values_id, pov.products_options_values_name, pa.options_values_price, pa.price_prefix, pa.attribute_default, pa.dependson_options_id, pa.dependson_options_values_id from products_attributes pa, products_options_values pov where pa.products_id = '" . $products_id . "' and pa.options_id = '" . (int)$products_options_name['products_options_id'] . "' and pa.options_values_id = pov.products_options_values_id order by pa.products_attributes_sort_order");
            $select_id = "attribute[{$products_options_name['products_options_id']}]";
            $select_output_selects[$select_id] = [
                "type" => "select",
                "label" => $products_options_name['products_options_name'],
                "content" => []
            ];
            while ($products_options = tep_db_fetch_array($products_options_query)) {
                $select_output_selects[$select_id]['content'][$products_options['products_options_values_id']] = $products_options['products_options_values_name'];
            }
        }
    }



    $array = [
        "form" => [
            "type"=>"group",            
            "<common>" => [
                "name" => "name_<type>",
                "id" => "<name>_<type>",
                "label" => "<name>",
                "value" => "<values_list>",
                "lib" => "bootstrap"
            ],
            "<common_group>" => [
                    "class" => "col-md-5",
            ],
            "<common_label>" => [
                "class" => "control-label"
            ],         
            "noTag" => true,
            "id" => "variations_form",
            "class" => "variations_form",
            "inputs" => [
                "type" => "group",             
                "elements" => [
                    "<common>" => [
                        "type" => "input"
                    ],
                    "model" => [],
                    "GTIN" => [],
                    "image_id" => [],
                    "price" => [],
                    "products_id" => [
                        "sub_type" => "hidden",
                        "value" => $products_id
                    ]
                ]
            ],
            "right_elements" => [
                "type" => "group",
                "elements" => [
                    "attributes" => [
                        "type" => "group",
                        "class" => "col-md-6",
                        "elements" => $select_output_selects
                    ],
                    "autodesk_linking" => [
                        "type" => "group",
                        "class" => "col-md-6",
                        "elements" => [
                            "<common>" => [
                            ],
                            "terms" => [
                                "type" => "input",
                                "label" => ""
                            ],
                            "search" => [
                                "type" => "button",
                                "hx-trigger" => "click",
                                "hx-get" => "api/product_autodesk_link_search",
                                "hx-target" => "#autodesk_link_select",
                                "hx-include" => "#terms_input",
                                "hx-swap" => "innerHTML"
                            ],
                            "autodesk_link" => [
                                "type" => "select"
                            ]
                        ]
                    ]
                ]
            ],
            "controls" => [
                "type" => "group",
                "class" => "col-md-1 form-inline space-x-2 space-y-2 align-items-right",
                "elements" => [
                    "submit" => [
                        "type" => "button",
                        "hx-trigger" => "click",
                        "hx-get" => "api/product_save",
                        "hx-indicator" => "#indicatorLines",
                        "hx-include" => ".variations_form",
                        "hx-target" => "#variations_table_body",
                        "hx-swap" => "beforeend"
                    ],
                    "cancel" => [
                        "type" => "button",
                        "hx-trigger" => "click",
                        "hx-get" => "api/product_cancel",
                        "hx-indicator" => "#indicatorLines",
                        "hx-include" => ".variations_form",
                        "hx-target" => "#variations_form",
                        "hx-swap" => "outerHTML"
                    ]
                ]
            ]
        ]
    ];
    //print_rr($array,"renderarray");
    return renderForm($array, $values);
}



function renderForm($config, $values = []) {
    $formContent = render_node($config['form'], $config['form']['common'], $values);
    if ($config['form']['noTag']) return "<div class='\"row {$config['form']['class']}' id='{$config['form']['id']}'>"  .  $formContent . "</DIV>";
    return "<form class='{$config['form']['class']}' id='{$config['form']['id']}'>
                {$formContent}
            </form>";
}

function render_node($nodes, $common = [], $values = []) {
    $formContent = '';

    // Initialize $common array to include 'all' and type-specific keys
    if (is_array($nodes['<common>'])) {
        $common['all'] = array_merge($common['all'] ?? [], $nodes['<common>']);
    } 
      
    foreach ($nodes as $node_name => $node) {
        //print_rr(is_array($node)  . " != 0 OR " . strpos($node_name, "<common") . " >= 0 ", "node_name: " . $node_name);
         if (!is_array($node) || strpos($node_name, "<common") !== false ) continue;
           $type = $node['type'];
        
        // Add type-specific common if present
        if (isset($nodes["<common_{$type}>"])) {
            $common[$type] = array_merge($nodes["<common_{$type}>"], $common[$type] ?? []);
        }

        // Select common configuration based on type
       

        switch ($type) {
            case 'group':
                $formContent .= renderGroup($node, $common, $values);
                break;
            default:
                if (is_array($node)) $node['name'] = $node_name;
                $formContent .= renderElement($node, $common, $values);
        }
    }
    return $formContent;
}

function renderGroup($group, $common = [], $values = []) {
    
    $group = array_merge($common['all'] ?? [], $group);
    if ($common['group']) $group = array_merge($common['group'], $group);
    $groupHtml = render_node($group['elements'], $common, $values);
    if ($group['lib'] == 'bootstrap') return "<div class='form-group {$group['class']}'>$groupHtml</div>";
    return tcs_draw_card_html($groupHtml, $group['class']);
}

function renderElement($elementConfig, $common = [], $values = []) {
    $nodeCommon = array_merge($common['all'] ?? [], $common[$elementConfig['type']] ?? []);


    $elementConfig = array_merge($nodeCommon, $elementConfig);
    foreach ($elementConfig as $key => $value) {
        $value = str_replace('<name>', $elementConfig['name'], $value);
        $value = str_replace('<id>', $elementConfig['id'], $value);
        $value = str_replace('<label>', $elementConfig['label'], $value);
        $value = str_replace('<type>', $elementConfig['type'], $value);

        switch ($key) {
            case 'label':
                $value = mb_convert_case(str_replace('_', ' ', $value), MB_CASE_TITLE, "UTF-8");
                break;
            case 'name':
                $value = strtolower($value);
                break;
            case 'value':
                if ($value == "<values_list>") {
                    $value = '';
                    $theName = $elementConfig['name'];
                    if (isset($values[$theName])) $value = $values[$theName];
                }
                $elementConfig["values_name"] = $values[$elementConfig['name'] . '_value_name'] ?? '';
                break;
        }
        $elementConfig[$key] = $value;
    }

    return tcs_draw_form_element($elementConfig);
}









function tcs_draw_variations_table($products_attributes, $products_id, $body_only = false, $params = []) {
    // Define columns for the table
    $columns = [
        ['name' => '&nbsp;', 'class' => '', 'params' => ''],
        ['name' => 'Model', 'class' => '', 'params' => ''],
        ['name' => 'GTIN', 'class' => '', 'params' => ''],
        ['name' => 'Image ID', 'class' => 'text-center', 'params' => ''],
        ['name' => 'Price', 'class' => 'text-center', 'params' => ''],
        ['name' => 'Options', 'class' => 'text-center', 'params' => ''],
        ['name' => 'Sort Order', 'class' => 'text-center', 'params' => ''],
        ['name' => 'Autodesk Link', 'class' => 'text-center', 'params' => ''],
        ['name' => 'Actions', 'class' => 'text-center', 'params' => ['colspan' => '2']],
    ];

    // Get the variations
    $variations = $products_attributes->get_variations();
    //print_rr($variations,'prodad');
    $rows = '';
    foreach ($variations as $key_a => $variation) {
        $rows .= tcs_draw_variations_table_row($variation);
    }

    // If body_only is true, return just the rows
    if ($body_only) {
        return $rows;
    }

    $footer = "<div class='varitionsFooter'>
        <div id='variations_options_container' class=''>
            <div class='row'>";
    $footer .= tcs_draw_variations_form($products_attributes, $products_id);
    $footer .= "</div></div></div>";

    // Output the table with HTMX attributes for sort order updates
    return tcs_draw_admin_bootstrap_table(
        'Variations Table',
        'variationsTable',
        'table table-striped table-draggable',
        $columns,
        $rows,
        $footer,
        ['body' => ['hx-get' => 'api_h.php', 'hx-include' => '.variationsSOinput', 'hx-target' => '#variations_error_message','hx-vals' => '{"action": "product_variations_updateSortOrder", "products_id": "' . $products_id . '"}', 'hx-trigger' => 'deactivate'] ],
        $body_only
    );
}
function tcs_draw_variations_table_row($variation, $params = []) {
    //print_rr($variation, 'variations');
    $attributes = explode('{', substr($variation['attribute_string'], strpos($variation['attribute_string'], '{') + 1));
    $attributesNameString = "";
    for ($i = 0, $n = sizeof($attributes); $i < $n; $i++) {
        $pair = explode('}', $attributes[$i]);
        $attributesNameString .= '<span style="font-weight:bold">' . tep_options_name($pair[0]) . ':</span> ';
        $attributesNameString .= tep_values_name($pair[1]) . ' ';
    }

    $rowId = $variation['products_variations_id'];
    $rowClass = ($variation['enabled'] == "0") ? "table-danger danger" : "variation_row";
    $autodesk_link = "";
    $autodesk_link = $variation['autodesk_link_name'];
    if ($variation['autodesk_link'] != "") {
        $autodesk_link = $autodesk_link;
    }
    // Add this row's data to the dataset
    $id = "variations_table_row_{$rowId}";

    $row_content = [
        ['class' => 'portletTD',          'id' => '',                                     'content' => '<div class="portlet">&nbsp;</div>'],
        ['class' => '',                   'id' => "variations_table_model_{$rowId}",      'content' => $variation['model']],
        ['class' => '',                   'id' => "variations_table_gtin_{$rowId}",       'content' => $variation['gtin']],
        ['class' => 'text-center',        'id' => "variations_table_image_id_{$rowId}",   'content' => $variation['image_id']],
        ['class' => 'text-center',        'id' => "variations_table_Price_{$rowId}",      'content' => $variation['price']],
        ['class' => '',                   'id' => "variations_table_attributes_{$rowId}", 'content' => $attributesNameString],
        ['class' => '',                   'id' => '',                                     'content' => '<input class="indexSO variationsSOinput" type="text" size="1" maxlength="4" value="' . $variation['sort_order'] . '" name="variationsTableSO[' . (int)$variation['products_variations_id'] . ']">'],
        ['class' => '',                   'id' => '',                                     'content' => $autodesk_link],
        [
            'class' => 'listsOptionsEdit',
            'id' => '',
            'text' => 'e',
            'hx-target' => '#variations_form',
            'hx-get' => 'api/product_variations_product_edit',
            'hx-vals' => '{"products_id":"' . $variation['products_id'] . '", "products_variations_id":"' . $variation['products_variations_id'] . '"}'
        ],
        [
            'class' => 'listsOptionsDelete',
            'id' => '',
            'text' => 'x',
            'hx-target' => "#{$id}",
            'hx-get' => 'api/product_variations_removeVariation',
            'hx-vals' => '{"products_variations_id":"' . $variation['products_variations_id'] . '"}'
        ],
    ];
    //print_rr($params, 'paramamaamama');
    $params['data-variationsid'] = $rowId;
    // Output the table
    return tcs_draw_admin_bootstrap_table_row($id, $rowClass, $row_content, $params);
}



function tcs_draw_attributes_table_row($attributes_options_id, $attributes_values_id, $products_attributes_id = '', $products_id = '', $options_name = '', $values_name = '', $value_price = '', $price_prefix = '', $attribute_default = 'No', $dependson_options_name = '', $dependson_values_name = '', $dependson_options_id = '', $dependson_values_id = '') {
    // Construct the row ID using the provided parameters
    $rowId = $attributes_options_id . '_' . $attributes_values_id;

    // Generate the HTML table row with proper IDs
    $html = "<tr id='attributesTableRow_{$rowId}' class='' hx-swap-oob='true' data-attributesid='{$products_attributes_id}' data-rowNum='{$rowId}' hx-target='this'>";
    $html .= "<td class='portletTD' id=''><div class=\"portlet\">&nbsp;</div></td>";
    $html .= "<td class='' id='attributesTable_optionsName_{$rowId}' data-options_id='{$attributes_options_id}'>{$options_name}</td>";
    $html .= "<td class='' id='attributesTable_optionsValueName_{$rowId}' data-values_id='{$attributes_values_id}'>{$values_name}</td>";
    $html .= "<td class='text-center' id='attributesTable_optionsValuePriceDefault_{$rowId}'>{$attribute_default}</td>";
    $html .= "<td class='' id='attributesTable_DependsOn_{$rowId}' data-dependson_options_id='{$dependson_options_id}' data-dependson_values_id='{$dependson_values_id}'>";

    if (!empty($dependson_options_name) && !empty($dependson_values_name)) {
        $html .= "{$dependson_options_name}: {$dependson_values_name}";
    } else {
        $html .= "None";
    }

    $html .= "</td>";
    $html .= "<td class=''><input class=\"indexSO attributesSOinput\" type=\"text\" name=\"\" size=\"1\" maxlength=\"4\" value=\"\"></td>";
    $html .= "<td class='attributesOptionsEdit listsOptionsEdit' id='' hx-get='api_h.php?action=product_attributes_product_edit' hx-target='#attributes_form' hx-vals='{\"products_id\": \"{$products_id}\", \"products_attributes_id\": \"{$products_attributes_id}\"}'>e</td>";
    $html .= "<td class='attributesOptionsDelete listsOptionsDelete' id='' hx-get='api_h.php?action=product_attributes_removeAttribute' hx-prompt='Are you sure you want to delete this attribute?' hx-vals='{\"products_id\": \"{$products_id}\", \"products_attributes_id\": \"{$products_attributes_id}\"}'>x</td>";
    $html .= "</tr>";

    return $html;
}

function tcs_draw_attributes_form($products_id, $products_attributes = null, $selected_attributes = null,) {
    global $currencies;
    // //print_rr($products_attributes, 'products_attributes');
    $options_output = "<div id='attributes_form'>";
    if (!is_object($products_attributes)) {
        $products_attributes = new tcs_product_attributes($products_id);
    }
    $attributes = $products_attributes->get_attributes();

    //print_rr($products_attributes);
    $fr_input = $fr_required = $fr_feedback = null;
    $fr_input    = FORM_REQUIRED_INPUT;
    $fr_required = 'required aria-required="true" ';
    $fr_feedback = ' has-feedback';

    foreach ($attributes as $aKey => $products_options) {
        $select_output_select = '<div class="form-group attribute_group ' . $fr_feedback . '"><label for="input_' . $aKey . '" class="control-label col-sm-3">' . $products_options['products_options_name'] . '</label><div class="col-sm-9 attributeWrapper"><select name="attributes[' . $aKey . ']" required="" aria-required="true" id="input_' . $aKey . '" class="form-control attributes_selects" style="display:none">';
        $buttons_output = '<div id="attributes_values_id_btns_container" class="attributes_values_id_btns_container btn-group" data-optionid="' . $aKey . '" >';
        $option_selected = false;
        $select_output_options = "";
        foreach ($products_options['values'] as $vKey => $products_options_values) {
            $selected_option = false;
            if (is_array($selected_attributes) && isset($selected_attributes[$aKey][$vKey])) {
                $selected_option = true;
            } elseif (isset($products_attributes->selected_attributes[$aKey][$vKey])) {
                $selected_option = true;
            }
            if ($selected_option) {
                $selected_option = 'selected';
                $selected_button = 'active btn-success';
            } else {
                $selected_option = '';
                $selected_button = '';
            }
            $optionsPrice = $currencies->display_price($products_options_values['options_values_price'], tep_get_tax_rate(1));
            $select_output_options .= '<option value="' . $vKey . '" data-productId="' . tep_get_prid($products_id) . '" data-priceIncrease="' . $products_options_values['options_values_price'] . '" ' . $selected_option . ' data-dependson_optionsid="' . $products_options_values['dependson_options_id'] . '" data-dependsOn_valuesid="' . $products_options_values['dependson_options_values_id'] . '">' . $products_options_values['products_options_values_name'];
            if ($products_options_values['options_values_price'] != '0') {
                $select_output_options .= ' (' . $products_options_values['price_prefix'] . $optionsPrice . ') ';
            }
            $select_output_options .= '</option>';
            $vals = "{\"action\": \"update_attributes_form\",\"options_id\": \"{$aKey}\",\"values_id\": \"{$vKey}\",\"products_id\": \"{$products_id}\"}";
            $buttons_output .= "<button type='button' hx-post='api_h.php' hx-target='#attributes_form' hx-swap='outerHTML' hx-vals='{$vals}' hx-include='.attributes_selects' id='valuesSelectBtns_{$aKey}_{$vKey}'   class='btn btn-default valuesSelectBtns {$selected_button}' >{$products_options_values['products_options_values_name']} </button>";
        }
        if (!$option_selected) {
            $select_output = $select_output_select . '<option disabled selected value>None</option>' . $select_output_options;
        }
        $select_output .=   "</select>";
        $buttons_output .= "</div></div></div>";
        $buttons_output .= "";
        $options_output .= $select_output . $buttons_output;
    }
    $options_output .= "</div>";

    return $options_output;
}

function tcs_draw_cart_dropdown($cart, $currencies) {
    include("includes/languages/english/modules/navbar_modules/nb_shopping_cart.php");
    $cart_link_url = tep_href_link('shopping_cart.php');
    $cart_link_a = "<a href='{$cart_link_url}'>";
    $cart_link_text = sprintf(MODULE_NAVBAR_SHOPPING_CART_HAS_CONTENTS, $cart->count_contents(), $currencies->format($cart->show_total()));
    $cart_link_html = "{$cart_link_a}{$cart_link_text}</a>";
    $dropdown = [
        "id" => "shopping_cart_dropdown",
        "class" => "shopping_cart",
        "toggle_text" => sprintf(MODULE_NAVBAR_SHOPPING_CART_CONTENTS, $cart->count_contents()),
        "entries" => [
            "$cart_link_html",
            "<divider>"
        ]
    ];
    $products = $cart->get_products();
    foreach ($products as $k => $v) {
        $products_attributes = new tcs_product_attributes($v['id'], 1);
        $attribute_string = "{" . explode("{", $v['id'], 2)[1];
        $dropdown['entries'][] = sprintf("<a href='{$cart_link_url}'>%s x %s</a>", $v['quantity'], $v['name'] . $products_attributes->generate_product_suffix($attribute_string));
    }
    $dropdown['entries'][] = "<divider>";
    $dropdown['entries'][] = "{$cart_link_a}" . MODULE_NAVBAR_SHOPPING_CART_VIEW_CART . "</a>";
    return tcs_draw_dropdown($dropdown, "li");
}

function tcs_draw_dropdown($config, $element = "div") {
    $output = "<{$element} class='dropdown' id='{$config['id']}'>";
    $output .= "<a class='dropdown-toggle' id='{$config['id']}_toggle' data-toggle='dropdown'  data-toggle='popover' title='' href='#'>{$config['toggle_text']}</a>
               <ul class='dropdown-menu' id='{$config['id']}_menu'>";
    foreach ($config['entries'] as $key => $entry) {
        if ($entry == "<divider>") {
            $output .= "<li role='separator' class='divider'></li>";
            continue;
        }
        $output .= "<li>{$entry}</li>";
    }
    $output .= "</ul></{$element}>";

    return $output;
}

function tcs_draw_products_price_widget($product_info, $products_attributes, $currencies, $content_width = "6") {

    $products_price = $currencies->display_price($product_info['products_price'], tep_get_tax_rate($product_info['products_tax_class_id']));
    $specials_price = null;

    $products_price_raw = $product_info['products_price'];
    if ($products_attributes->has_variations) {
        $products_price_raw = $products_attributes->get_current_selected_variation()['price'];
    } else if ($products_attributes->has_attributes) {
        $attributes = $products_attributes->get_current_selected_attributes();
        if ($attributes['price_prefix'] == '+') {
            $products_price_raw += $attributes['attributes_values_price'];
        } else if ($attributes['price_prefix'] == '*') {
            $products_price_raw = $attributes['attributes_values_price'] > 0 ? $products_price_raw * $attributes['attributes_values_price'] : $products_price_raw;
        } else if ($attributes['price_prefix'] == '-') {
            $products_price_raw -= $attributes['attributes_values_price'];
        }
    }

    if ($thePrice = tep_get_products_special_price($product_info['products_id'])) {
        $VAT = $currencies->display_price($thePrice + tep_calculate_tax($thePrice, tep_get_tax_rate($product_info['products_tax_class_id'])), tep_get_tax_rate($product_info['products_tax_class_id'])) . ' Inc. VAT';
    } else {
        $VAT = $currencies->display_price($products_price_raw + tep_calculate_tax($products_price_raw, tep_get_tax_rate($product_info['products_tax_class_id'])), tep_get_tax_rate($product_info['products_tax_class_id'])) . ' Inc. VAT';
    }
    if ($new_price = tep_get_products_special_price($product_info['products_id'])) {
        $specials_price = $currencies->display_price($new_price, tep_get_tax_rate($product_info['products_tax_class_id']));
    }
    if (@tep_not_null($specials_price)) {
        $VAT = '<br> <span id=productsPriceIncTax>(ex VAT ' . $currencies->display_price($thePrice + tep_calculate_tax($thePrice, tep_get_tax_rate($product_info['products_tax_class_id'])), tep_get_tax_rate($product_info['products_tax_class_id'])) . ' Inc. VAT' . ')</span>';
        $products_price = '<del>' . $currencies->display_price($products_price_raw, tep_get_tax_rate($product_info['products_tax_class_id'])) . '</del> <span class="productSpecialPrice" itemprop="price" id="productInfoPrice" content="' . $currencies->display_raw($new_price, tep_get_tax_rate($product_info['products_tax_class_id'])) . '">' . $currencies->display_price($new_price, tep_get_tax_rate($product_info['products_tax_class_id'])) . '</span>';
    } else {
        $products_price = '<span id="productInfoPrice">' . $currencies->display_price($products_price_raw, tep_get_tax_rate($product_info['products_tax_class_id'])) . '</span>';
        $VAT = '<br> <span id="productsPriceIncTax">(ex VAT ' . $currencies->display_price($products_price_raw + tep_calculate_tax($products_price_raw, tep_get_tax_rate($product_info['products_tax_class_id'])), tep_get_tax_rate($product_info['products_tax_class_id'])) . ' Inc. VAT' . ')</span>';
    }
    $output = "<div class='col-sm-{$content_width} cm-pi-price' hx-swap-oob='true' id='cm-pi-price'><div class='productsPrice text-right-not-xs'><div class='text-right-not-xs productsPrice'>{$products_price}{$VAT}</div></div></div>";
    return $output;
}

function tcs_draw_products_info_widget($product_info, $products_attributes, $content_width = "6") {
    if (@tep_not_null($product_info['products_model'])) {
        $products_model = $product_info['products_model'];
        $products_gtin = $product_info['products_gtin'];
    }

    if ($products_attributes->has_variations) {
        $variations = $products_attributes->get_current_selected_variation();
        //print_rr($variations);
        //echo 'mpn: ' . $variations['mpn'];
        if (@tep_not_null($variations['model'])) $products_model = $variations['model'];
        if (@tep_not_null($variations['gtin'])) $products_gtin = $variations['gtin'];
    }
    $products_model_out = !empty($products_model) ? "<dt>Product Code</dt><dd id='products_model'>{$products_model}</dd>" : "";
    $products_gtin_out = !empty($products_gtin) ? "<dt>GTIN</dt><dd id='products_gtin'>{$products_gtin}</dd>" : "";
    $products_manufacturer_out = !empty($products_manufacturer) ? "<dt>Manufacturer</dt><dd id='products_manufacturer'>{$products_manufacturer}</dd>" : "";
    return "<div  hx-swap-oob='true' id='cm-pi-model' class='col-sm-{$content_width} cm-pi-model'><dl class='dl-horizontal list-group-item-text small'>{$products_manufacturer_out}{$products_model_out}{$products_gtin_out}</dl></div>";
}


function tcs_draw_products_name_widget($product_info, $products_attributes, $content_width = "12") {
    if (strpos($product_info['products_id'], '{')) {
        $variation_info = $products_attributes->get_variation_from_attrib($product_info['products_id']);
        $products_name = $product_info['products_name'] . $variation_info['product_name_suffix'];
    } else {
        $variation_info = $products_attributes->get_current_selected_variation();
        if (@tep_not_null($variation_info)) {
            $products_name = $product_info['products_name'] . $variation_info['product_name_suffix'];
        } else {
            $products_name = $product_info['products_name'];
        }
        $products_url = tep_href_link('product_info.php', 'products_id=' . $product_info['products_id'], 'SSL', false);
        $products_name = "<a href='{$products_url}' itemprop='url'><span id='products_name' itemprop='name'>{$products_name}</span></a>";
        // $products_name = sprintf(MODULE_CONTENT_PI_NAME_DISPLAY_NAME, $products_name);
        return "<div hx-swap-oob='true' class='col-sm-{$content_width} cm-pi-name' id='cm-pi-name' style='min-height:70px'><div class='page-header'><h1 class='h1'>{$products_name}</h1></div></div>";
    }
}

function tcs_subscriptions_searchBox(){
    return tcs_draw_form_element([
        "type" => "input",
        "sub_type" => "search",
        "name" => "subscription_search",
        "placeholder"=>"Begin Typing To Search ...", 
        "hx-post"=>"api/autodesk_search_subscriptions_table",
        "hx-trigger"=>"input changed delay:500ms, search",
        "hx-target"=>"#subscriptions_body",
        "hx-indicator"=>"#indicatorLines",
        "hx-vals" => [
            "action" => "autodesk_search_subscriptions_table"
        ]
    ]);
}