<?php

  class tcs_product_attributes {
	  	public $products_id;
		public $languages_id;
        public $dependency_issues = array();
		public $attributes = array();		
		public $options_values = array();
		public $variations = array();
		public $has_attributes = false;
		public $has_variations = false;
		public $default_attributes = array();
		public $selected_attributes = array();
		public $selected_variation = array();
		public $input_attributes;
		public $isPreset = false;
		private $cacheKey;
		private $cacheTime;
		private $dirty = false;		
    private $building = false;     // is true when class is being constructed
    public $interlinked = false;  // is true when the class is out of sync with db
		
		
    function __construct($products_id = null,$languages_id = 1,$input_attributes = null) {
		global $attribute_cache;
		$this->isPreset = true;			
		$this->input_attributes = $this->init_input_attributes($products_id, $input_attributes);


		$this->cacheKey = 'product_attributes_' . $this->products_id;
		$this->cacheTime = 3600; // 1 hour	

		if (!empty($attribute_cache[$this->products_id])){
			$this->init_cache();
	  	}
		//if (!isset($input_attributes)){			
		$this->languages_id = $languages_id;	
		$this->has_attributes = $this->init_has_attributes();		
		$this->has_variations = $this->init_has_variations();
       // //print_rr($this,'attirbits');
	}	
	
	
	private function init_input_attributes($products_id, $input_attributes){		
		// sanatize the strings these regexs allows digits and curly braces only
		
		$products_id = preg_replace('/[^0-9{}]/', '', urldecode($products_id));
		$exploded_id =  explode('{', $products_id,2);		
		$this->products_id = (int)$exploded_id[0];
		if (is_array($input_attributes)){
            if (is_array(($input_attributes[array_key_first($input_attributes)]))){
                foreach($input_attributes as $key => $value){
                    $input_attributes[$key] = array_key_first($value);
                }
            }
			return $input_attributes;			
		} else if (is_string($input_attributes)){			
			$input_attributes = preg_replace('/[^0-9{}]/', '', urldecode($input_attributes));
			return $this->parse_attribute_string($input_attributes);		
		} else if (is_numeric(strpos($products_id,'{'))) {			
			return $this->parse_attribute_string('{' . $exploded_id[1]);			
		} else {
			$this->isPreset = false;
		}
        return false;
	}
    private function init_has_attributes(){
		$q = tep_db_query("SELECT * FROM `products_attributes` where `products_id` = " . (int)$this->products_id);
		return tep_db_num_rows($q) > 0 ? true : false;
	}
	
	private function init_has_variations(){
		$q = tep_db_query("SELECT * FROM `products_variations` where `products_id` = " . (int)$this->products_id);
		return tep_db_num_rows($q) > 0 ? true : false;
	}
	
	private function init_cache(){
		if (!empty($attribute_cache[$this->products_id])){
			$temp_attribute_cache = $attribute_cache[$this->products_id];
		} else {			
			$redis = new Redis();
			$redis->connect('127.0.0.1', 6379);			
			$redis->set($this->cacheKey, json_encode($this), $this->cacheTime);	
			
			if ($redis->exists($this->cacheKey)) {
				// Read from cache
				$temp_attribute_cache = json_decode($redis->get($this->cacheKey), true);				
			} else {
				return false;				
			}				
		}	
		$this->attributes = 		 $temp_attribute_cache->attributes;
		$this->variations = 		 $temp_attribute_cache->variations;
		$this->options_values = 	 $temp_attribute_cache->options_values;
		$this->default_attributes =  $temp_attribute_cache->default_attributes;
		$this->selected_attributes = $this->get_current_selected_attributes();
		
		unset($redis);
		unset($temp_attribute_cache);
        return true;
	}


	private function set_cache(){
		$redis = new Redis();
		$redis->connect('127.0.0.1', 6379);
		$redis->set($this->cacheKey, json_encode($this), $this->cacheTime);
	}
    
    public static function get_attributes_from_id($products_attributes_id){
          $attribute_query = tep_db_query("select * from products_attributes where products_attributes_id = '" . (int)$products_attributes_id . "'");
          if (tep_db_num_rows($attribute_query) > 0){
              $attribute = tep_db_fetch_array($attribute_query);
              return $attribute;
          } else {
              return false;
          }
    }

	private function init_attributes(){
        //if (!empty($this->attributes)) return $this->attributes;
        $this->building = true;
		$output = array();
		$options_values=array();
		$products_options_sql= "  
			select distinct 
				pa.attribute_default,
				popt.products_options_id,
				popt.products_options_name
			from
				products_attributes pa
				JOIN products_options popt on pa.options_id = popt.products_options_id
			where 
				pa.products_id='" . (int)$this->products_id . "' and 
				popt.language_id = '" . (int)$this->languages_id . "' 
			order by 
				pa.products_attributes_sort_order";
		$products_options_query = tep_db_query($products_options_sql);
		print_rr($products_options_sql,"FIRST SQL");
		$count = 0;
		while ( $products_options = tep_db_fetch_array( $products_options_query ) ) {
			$attribute_default = $products_options["attribute_default"] == 1 ? true : false;
			$initalData = Array(
				"products_options_name" => $products_options['products_options_name'],
				"attribute_default" => $attribute_default,
				"values" => array()
			);
			$output[$products_options['products_options_id']] = $initalData;
			//print_rr($output,'inital data');
			$products_options_values_sql = "
				select 
					pov.products_options_values_id, 
					pov.products_options_values_name,
					pa.options_values_price, 
					pa.price_prefix, 
					pa.attribute_default,
					pa.products_attributes_id,
					pa.dependson_options_id, 
					pa.dependson_options_values_id,
					pa.products_attributes_sort_order
				from
					products_attributes pa, 
					products_options_values pov 
				where 
					pa.products_id = '" . (int)$this->products_id . "' 
					and pa.options_id = '" . (int)$products_options['products_options_id'] . "' 
					and pa.options_values_id = pov.products_options_values_id and pov.language_id = '" . (int)$this->languages_id . "' 
					order by pa.products_attributes_sort_order";
					
		
			print_rr($products_options_values_sql, "SECOND SQL");
			$products_options_values_query = tep_db_query($products_options_values_sql);
			$i = 0;	
			print_rr($products_options_values_sql, "thiurd SQL");
			while ($products_options_values = tep_db_fetch_array($products_options_values_query)) {	
				
				$value = array(
					"enabled" => 0,
					"products_attributes_id" => $products_options_values['products_attributes_id'],
                    "products_options_name" => $products_options['products_options_name'],
                    "products_options_id" => $products_options['products_options_id'],
					"products_options_values_name" => $products_options_values['products_options_values_name'],
                    "products_options_values_id" => $products_options_values['products_options_values_id'],
					"options_values_price" =>  $products_options_values['options_values_price'], 
					"price_prefix" =>  $products_options_values['price_prefix'],  
					"attribute_default" =>  $products_options_values['attribute_default'], 
					"dependson_options_id" =>  $products_options_values['dependson_options_id'], 
					"dependson_options_values_id" =>  $products_options_values['dependson_options_values_id'], 
					"products_attributes_sort_order" => $products_options_values['products_attributes_sort_order']
				);
				//print_rr($value, "BUILT VALUE");
				$output[$products_options['products_options_id']]['values'][$products_options_values['products_options_values_id']] = $value;
				$options_values[$products_options_values['products_options_values_id']] = $value;
				if ($products_options_values['attribute_default'] == 1 || $i == 0){ 
					$this->default_attributes[$products_options['products_options_id']] = $products_options_values['products_options_values_id'];
				}
				$i++;
			}
			//set default attrib
			$output[$products_options['products_options_id']]['values'][$this->default_attributes[$products_options['products_options_id']]]["attribute_default"] = 1;
		}
		$this->attributes = $output;
		$this->options_values = $options_values;
		$this->selected_attributes = $this->get_current_selected_attributes();
		$attribute_cache[$this->products_id] = $this;
        $this->building = false;
        $this->interlinked = false;
		$this->set_cache();
		//print_rr($this,"final object");
		return $this->attributes;
	}
    public function init($variations = true):void{
        if ($this->interlinked || empty($this->attributes)) $this->init_attributes();
        if (!$this->building && $variations && empty($this->variations)) $this->init_variations();
        $this->dependency_issues = $this->check_dependency_issues();
    }
	public function get_attributes($check_for_variations = true){
        if ($this->interlinked || empty($this->attributes)) $this->init_attributes();
        if (!$this->building && empty($this->variations)) $this->init_variations();
		foreach ($this->attributes as $keya=>$attributes){
			foreach ($attributes['values'] as $keyb=>$options){
				if ($check_for_variations){
					if ($options['enabled'] == 0){
						unset($this->attributes[$keya]['values'][$keyb]);
					}
				}			}
			if (empty($this->attributes[$keya]['values'])){
				unset($this->attributes[$keya]);
			}
		}
		return $this->attributes;
	}
 
	private function init_variations(){	
        if ($this->interlinked || empty($this->attributes) ) $this->init_attributes();
		if (empty($this->attributes)) return false;
        $this->building = true;
		$output = array();
		$variations_sql = "select * 
			from products_variations pv
			join products p on p.products_id = pv.products_id 
		 WHERE pv.products_id = '" . $this->products_id . "'
		 order by sort_order";
		 $variations_query = tep_db_query($variations_sql);
		if(tep_db_num_rows($variations_query)){
			while ($variations_values = tep_db_fetch_array($variations_query)) {
				$attributes = $this->parse_attribute_string($variations_values['attributes']);
				$product_name_suffix = $this->generate_product_suffix($attributes);
				//$product_url = tep_href_link('product_info.php', 'products_id=' . $this->products_id . $variations_values['attributes']);
				$product_image_url = "";
				if (@tep_not_null($variations_values['image_id'])){
					$values = tep_db_query("select image from products_images where id = '" . $variations_values['image_id'] . "'");
					
					if (tep_db_num_rows($values) > 0){
						$values_values = tep_db_fetch_array($values);
						$product_image_url = "https://www.cadservices.co.uk/images/" . $values_values["image"];
					}
				} 
				$autodesk_link = '';
                $autodesk_link_name = '';
                if ($variations_values['manufacturers_id'] == 15) {
					if (empty($variations_values['autodesk_catalog_unique_hash'])){
						$autodesk_link =  'NULL';
						$autodesk_link_name =  'Autodesk product: Not linked';
					} else {
                    	$autodesk_link =  $variations_values['autodesk_catalog_unique_hash'];
                    	$autodesk_link_name =  $this->get_autodesk_product_name($variations_values['autodesk_catalog_unique_hash']);
                	}
                }				
                $output[] = array(
                    'products_variations_id' => $variations_values['products_variations_id'],
					'autodesk_catalog_unique_hash' => $variations_values['autodesk_catalog_unique_hash'],
                    'products_id' => $variations_values['products_id'],
                    'attribute_string' => $variations_values['attributes'],
                    'product_name_suffix' => $product_name_suffix,
					'manufacturers_id' => $variations_values['manufacturers_id'],
                    'model' => $variations_values['model'],
                    'price' => $variations_values['price'],
                    'gtin' => $variations_values['gtin'],
                    'image_url' => $product_image_url,
                    'image_id' => $variations_values['image_id'],
                    'attributes' => $attributes,
                    'autodesk_link' =>  $autodesk_link,
                    'autodesk_link_name' =>  $autodesk_link_name,
                    'enabled' => 1
                );
			}
		}		
		$this->variations = $output;
		// enable attributes that have variations
		foreach ($output as $key_a=>$variation){
			foreach ($variation['attributes'] as $key_b=>$attrib){
				if (empty($this->attributes[$key_b]) || empty($this->attributes[$key_b]['values'][$attrib])){
					$this->variations[$key_a]['enabled'] = 0;
				} else {
					$this->attributes[$key_b]['values'][$attrib]['enabled'] = 1;
				}
			}			
		}
		$this->set_cache();
        $this->building = false;
		return $this->variations;
	}
	
	private function get_autodesk_product_name($hash){
		if (empty($hash)) return false;
		$q = tep_db_query("SELECT id, orderAction, offeringName,accessModel_description, term_description, specialProgramDiscount_code 				
							FROM products_autodesk_catalog
							where unique_hash = '" . $hash ."'
							limit 1;");	
			  
		$qa = tep_db_fetch_array($q);		
		$link_string = $qa['id'] . ': ' . $qa['orderAction'] . ' ' . $qa['offeringName'] . ' ' .  $qa['accessModel_description'] . ' ' . $qa['term_description'] . ' ' . $qa['specialProgramDiscount_code'];
		 
		return tep_db_num_rows($q) > 0 ? $link_string : false;
	}

	public function get_variations(){
		if ($this->has_variations){
			$this->init_variations();
		}
		return $this->variations;
	}

	
	
	function get_current_selected_attributes(){
		//print_r($this);
		if 	($this->isPreset){
			$attribs = $this->get_attributes_from_array($this->input_attributes);
		} else {
			$attribs = $this->get_attributes_from_array($this->default_attributes);
		}
		return $attribs;
	}

	function get_current_selected_variation(){
		if 	($this->isPreset){
			$attribs = $this->input_attributes;			
		}else{
			$attribs = $this->default_attributes;
		}
        //print_rr($attribs,'attribsvars');
		$variation = $this->get_variation_from_array($attribs);
        if (is_array($variation)) return $variation; else return false;
	}

	function get_price(){		
		$attribs = $this->input_attributes;			
		$variation = $this->get_variation_from_array($attribs);	
		if (!empty($variation)) {
			$specials = tep_db_query("select * FROM specials where variations_id = " . $variation['products_variations_id'] . " AND status = 1 LIMIT 1");

			if (tep_db_num_rows($specials) > 0){
				$special = tep_db_fetch_array($specials);
				$thePrice = $special['specials_new_products_price'];
			
			} else {
				$thePrice = $variation['price'];
			}
		}
    //   print_rr(['q' => "select * FROM specials where variations_id = " . $variation['products_variations_id'] ,'numrows' => tep_db_num_rows($specials),'variation' => $variation,'special' => $special,'price' => $thePrice],'specials');
		//echo '<!--m:' . $thePrice  . '-->';
		if (isset($thePrice) && $thePrice > 0 )
			return $thePrice;
		else 
			return false;
	}
	function get_model(){		
		$attribs = $this->input_attributes;			
		$variation = $this->get_variation_from_array($attribs);		
		$the_model = $variation['model'];
		if (isset($the_model) && $the_model > 0 )
			return $the_model;
		else 
			return false;
	}
	function get_image(){		
		$attribs = $this->input_attributes;			
		$variation = $this->get_variation_from_array($attribs);
		if (isset($variation['image']) && $variation['image'] > 0 )
			return $variation['image'];
		else 
			return false;
	}
function find_missing_variations() {
        if ($this->interlinked || empty($this->attributes)) $this->init_attributes();
        if (!$this->building && empty($this->variations)) $this->init_variations();

	$available_attributes = array_keys($this->attributes);
	$missing_variations = array();

	foreach ($available_attributes as $attribute) {
		$variations_with_attribute = array_filter($this->variations, function($variation) use ($attribute) {
			return isset($variation['attributes'][$attribute]);
		});

		if (count($variations_with_attribute) == count($available_attributes)) {
			$missing_variations[] = $variations_with_attribute;
		}
	}

	return $missing_variations;
}


	function get_attributes_from_array($attribArray){
		$output = array();		
	//print_rr( $this->attributes,'this->attributes ');
	//print_rr($attribArray,'this attribs: ');
		foreach($attribArray as $aKey => $attrib){
            if (is_array($attrib)) $attrib = array_key_first($attrib);
			$output[$aKey][$attrib] = $this->attributes[$aKey]['values'][$attrib];
		}

		return $output;
	}

	function get_variation_from_attrib($a){
		if(!is_array($a)){
			$array = $this->parse_attribute_string($a);
		} else {
			$array = $a;
		}
		return $this->get_variation_from_array($array);
	}
		

	function get_variation_from_array($attribs){
		if($this->has_variations){
            if (!$this->building && empty($this->variations)) $this->init_variations();
			foreach ($this->variations as $variation) {
				$dbAttrib = $this->parse_attribute_string($variation['attribute_string']);
				if (!$this->compare_attributes($attribs,$dbAttrib)){
                    //print_rr(['attribs' => $attribs,'dbAttrib' => $dbAttrib,'match' => 'false'],'get_variation_from_arraysss');
                    continue;
				} else {
                    //print_rr(['attribs' => $attribs,'dbAttrib' => $dbAttrib,'match' => 'true'],'get_variation_from_arraysss');
					return $variation;	
				}
			} 
		}
		return false;
	}
	function get_variation_from_id($id){
		if($this->has_variations){
			if (!$this->dirty && empty($this->variations)) $this->init_variations();
			foreach ($this->variations as $variation){				
				if ($variation['products_variations_id'] == $id) return $variation;	
			}
		}		
		return false;
	}
    function get_attribute_from_id($id){
      //  print_rr($id,'get_attribute_from_id');
        if($id == 0 || $id == null || $id == '') return null;
        if($this->has_attributes){
            if ($this->interlinked || !$this->building && empty($this->attributes)) $this->init_attributes();
            foreach ($this->attributes as $option){
                foreach ( $option['values'] as $value) {
                    if ( $value['products_attributes_id'] == $id) {
                      //  print_rr($value,'get_attribute_from_id');
                        return $value;
                    }
                }
            }
        }
        return null;
    }

    function get_attribute_from_values($options_id, $values_id) {
       // print_rr([$options_id, $values_id],'get_attribute_from_values');
        if($this->has_attributes){
            if ($this->interlinked || !$this->building && empty($this->attributes)) $this->init_attributes();
            foreach ($this->attributes as $option_key => $option){
                if ($option_key == $options_id) {
                    foreach ($option['values'] as $value_key => $value) {
                        if ($value_key == $values_id) {
                           // print_rr([$options_id, $values_id],'returning: ' . $value);
                            return $value;
                        }
                    }
                }
            }
        }
        return null;
    }
	
	
	
	function parse_attribute_string($string){
		if (is_numeric(strpos($string, '{'))){
			$attributes = explode('{', substr($string, strpos($string, '{')+1));			
			$attributes_array = array();
			for ($i=0, $n=sizeof($attributes); $i<$n; $i++) {
				$pair = explode('}', $attributes[$i]);
				$attributes_array[(int)$pair[0]] = (int)$pair[1];
			}
			//print_rr($attributes_array);
			return $attributes_array;
		}else{
			return false;
		}
	}
	
	function generate_product_suffix($attributes = "", $separator = ', '){
		$product_name_suffix = "";
		if($this->has_variations){
            if (!$this->building && empty($this->variations)) $this->init_variations();
			if (!is_array($attributes)){
				$attributes = $this->parse_attribute_string($attributes);
				if(!$attributes) return false;
			}
			
			foreach($attributes as $attrib){
				$product_name_suffix .= $separator . $this->values_name($attrib);
			}		
		}
		return $product_name_suffix;
	}
	
	function options_name($options_id) {
		if (isset($options_id)){
			return $this->attributes[$options_id]['products_options_name'];
		}
		return false;
	}

	function values_name($values_id) {
		if (isset($values_id)) return $this->options_values[$values_id]['products_options_values_name'];
		return false;
	}
	function compare_attributes($a, $b) {
		if (!is_array($a)) {
			$a_array = $this->parse_attribute_string($a);
		} else {
			$a_array = $a;
		}
		if (!is_array($b)) {
			$b_array = $this->parse_attribute_string($b);
		} else {
			$b_array = $b;
		}
		if (!is_array($a_array) || !is_array($b_array)) {
			return false;
		}
		
          // Normalize the arrays to have consistent data types (strings in this case)
          $a_array = array_map('strval', $a_array);
          $b_array = array_map('strval', $b_array);
	
          if (serialize($a_array) == serialize($b_array)) {
			return true;
		} else {
			return false;
		}
	}

	function attributes_customSort($a, $b) {
    // Ensure the arrays are valid and have at least two elements
    if (!is_array($a) || !is_array($b) || count($a) < 2 || count($b) < 2) {
        return 0; // Consider them equal if they are not proper arrays or don't have enough elements
    }
		$cmp = $a[0] - $b[0];
    // If the first values are equal, compare the second values
    if ($cmp === 0) {
			$cmp = $a[1] - $b[1];
    }
    
    return $cmp;
}

    /**
     * Check for attribute dependency issues and variations problems
     *
     * Checks for:
     * - Missing dependencies in attributes
     * - Sort order violations in attributes
     * - Circular dependencies in attributes
     * - Missing variations for attributes
     * - Default attribute conflicts
     * - Self option group dependencies
     * - Missing attributes referenced in variations
     * - Duplicate model numbers in variations
     * - Missing or invalid Autodesk links in variations
     * - Selection issues (enabled variations with disabled attributes)
     *
     * @return array Array of issues found, indexed by attribute_id or variation_id. Each entry contains name, issues array, and optionally is_variation flag
     */
    public function check_dependency_issues() {
        if ($this->interlinked || empty($this->attributes)) $this->init(true);
        $attributes = $this->get_attributes(false);
        $issues_by_attribute = [];
       // print_rr($attributes,'attributesERZ');
        // Build attribute map for quick lookup
        $attribute_map = $this->build_attribute_map($attributes);

        // Collect all issues from different checks
        $all_issues = [];

        // Check for missing dependencies
        $all_issues = array_merge($all_issues, $this->check_missing_dependencies($attribute_map));

        // Check for sort order violations
        $all_issues = array_merge($all_issues, $this->check_sort_order_violations($attribute_map));

        // Check for circular dependencies
        $all_issues = array_merge($all_issues, $this->check_circular_dependencies($attribute_map));

        // Check for missing variations
        $all_issues = array_merge($all_issues, $this->check_missing_variations($attribute_map));

        // Check for default attribute conflicts
        $all_issues = array_merge($all_issues, $this->check_default_conflicts($attribute_map));

        // Check for self option group dependencies
        $all_issues = array_merge($all_issues, $this->check_self_option_group_dependencies($attribute_map));

        // Check variations-specific issues
        $all_issues = array_merge($all_issues, $this->check_variations_missing_attributes($attribute_map));
        $all_issues = array_merge($all_issues, $this->check_variations_duplicate_models());
        $all_issues = array_merge($all_issues, $this->check_variations_autodesk_links());
        $all_issues = array_merge($all_issues, $this->check_variations_selection_issues($attribute_map));

        // Group issues by attribute_id or variation_id
        foreach ($all_issues as $issue) {
            $attr_id = $issue['attribute_id'];
            $issue_data = [
                'type' => $issue['type'],
                'message' => $issue['message']
            ];
            if (!isset($issues_by_attribute[$attr_id])) {
                $issues_by_attribute[$attr_id]['name'] = $issue['name'];
                $issues_by_attribute[$attr_id]['issues'][] = $issue_data;
                // Mark variation issues differently
                if (strpos($attr_id, 'variation_') === 0) {
                    $issues_by_attribute[$attr_id]['is_variation'] = true;
                }
            } else {
                $issues_by_attribute[$attr_id]['issues'][] = $issue_data;
            }
        }
        return $issues_by_attribute;
    }

    /**
     * Build attribute map for dependency checking
     *
     * @param array $attributes The attributes array
     * @return array Mapped attributes with dependency information
     */
    private function build_attribute_map($attributes) {
        $attribute_map = [];
        foreach ($attributes as $key_a => $attribute) {
            foreach ($attribute['values'] as $key_v => $attribute_value) {
                $attribute_map[$attribute_value['products_attributes_id']] = [
                    'options_id' => $key_a,
                    'values_id' => $key_v,
                    'options_name' => $attribute['products_options_name'],
                    'values_name' => $attribute_value['products_options_values_name'],
                    'dependson_options_id' => $attribute_value['dependson_options_id'],
                    'dependson_options_values_id' => $attribute_value['dependson_options_values_id'],
                    'sort_order' => $attribute_value['products_attributes_sort_order']
                ];
            }
        }
        return $attribute_map;
    }

    /**
     * Check for missing dependencies
     *
     * @param array $attribute_map The attribute map
     * @return array Array of missing dependency issues
     */
    private function check_missing_dependencies($attribute_map) {
        $issues = [];
        foreach ($attribute_map as $attr_id => $attr) {
            if (tep_not_null($attr['dependson_options_id']) && tep_not_null($attr['dependson_options_values_id'])) {
                $found = false;
                foreach ($attribute_map as $check_attr) {
                    if ($check_attr['options_id'] == $attr['dependson_options_id'] &&
                        (strpos($attr['dependson_options_values_id'], $check_attr['values_id']) !== false)) {
                        $found = true;
                        break;
                    }
                }

                if (!$found) {
                    $issues[] = [
                        'type' => 'missing_dependency',
                        'name' => "{$attr['options_name']}: {$attr['values_name']}",
                        'attribute_id' => $attr_id,
                        'message' => "Attribute '{$attr['options_name']}: {$attr['values_name']}' depends on a missing attribute"
                    ];
                }
            }
        }
        return $issues;
    }

    /**
     * Check for sort order violations
     *
     * @param array $attribute_map The attribute map
     * @return array Array of sort order violation issues
     */
    private function check_sort_order_violations($attribute_map) {
        $issues = [];
        foreach ($attribute_map as $attr_id => $attr) {
            if (tep_not_null($attr['dependson_options_id']) && tep_not_null($attr['dependson_options_values_id'])) {
                foreach ($attribute_map as $check_attr) {
                    if ($check_attr['options_id'] == $attr['dependson_options_id'] &&
                        (strpos($attr['dependson_options_values_id'], $check_attr['values_id']) !== false)) {
                        if ($check_attr['sort_order'] > $attr['sort_order']) {
                            $issues[] = [
                                'type' => 'sort_order_violation',
                                'name' => "{$attr['options_name']}: {$attr['values_name']}",
                                'attribute_id' => $attr_id,
                                'message' => "Attribute '{$attr['options_name']}: {$attr['values_name']}' depends on '{$check_attr['options_name']}: {$check_attr['values_name']}' which has a higher sort order"
                            ];
                        }
                    }
                }
            }
        }
        return $issues;
    }

    /**
     * Check for circular dependencies
     *
     * @param array $attribute_map The attribute map
     * @return array Array of circular dependency issues
     */
    private function check_circular_dependencies($attribute_map) {
        $issues = [];

        // Build dependency graph
        $dependency_graph = [];
        foreach ($attribute_map as $attr_id => $attr) {
            if (tep_not_null($attr['dependson_options_id']) && tep_not_null($attr['dependson_options_values_id'])) {
                $dependency_graph[$attr_id] = [];
                foreach ($attribute_map as $dep_id => $dep_attr) {
                    if ($dep_attr['options_id'] == $attr['dependson_options_id'] &&
                        (strpos($attr['dependson_options_values_id'], $dep_attr['values_id']) !== false)) {
                        $dependency_graph[$attr_id][] = $dep_id;
                    }
                }
            }
        }

        // Detect cycles in the dependency graph
        foreach ($dependency_graph as $start_id => $deps) {
            $visited = [];
            $path = [$start_id];
            if ($this->has_cycle($dependency_graph, $start_id, $visited, $path)) {
                $cycle_path = [];
                foreach ($path as $attr_id) {
                    if (isset($attribute_map[$attr_id])) {
                        $cycle_path[] = "{$attribute_map[$attr_id]['options_name']}: {$attribute_map[$attr_id]['values_name']}";
                    }
                }
                $issues[] = [
                    'type' => 'circular_dependency',
                    'name' =>  $cycle_path[0],
                    'attribute_id' => $start_id,
                    'message' => "Circular dependency detected: " . implode(" > ", $cycle_path)
                ];
            }
        }

        return $issues;
    }

    /**
     * Helper method to detect cycles in a dependency graph
     *
     * @param array $graph The dependency graph
     * @param mixed $current Current node being checked
     * @param array $visited Reference to visited nodes array
     * @param array $path Reference to current path array
     * @return bool True if cycle is detected
     */
    private function has_cycle($graph, $current, &$visited, &$path) {
        $visited[$current] = true;

        if (isset($graph[$current])) {
            foreach ($graph[$current] as $neighbor) {
                if (!isset($visited[$neighbor])) {
                    $path[] = $neighbor;
                    if ($this->has_cycle($graph, $neighbor, $visited, $path)) {
                        return true;
                    }
                    array_pop($path);
                } else if (in_array($neighbor, $path)) {
                    $path[] = $neighbor;
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Check for attributes that don't have corresponding variations
     *
     * @param array $attribute_map The attribute map
     * @return array Array of missing variation issues
     */
    private function check_missing_variations($attribute_map) {
        $issues = [];

        // Only check if product has variations
        if (!$this->has_variations) {
            return $issues;
        }

        // Get all variations for this product
        $variations = $this->get_variations();

        // Build a set of all attribute combinations that have variations
        $variation_combinations = [];
        foreach ($variations as $variation) {
            if (!empty($variation['attributes'])) {
                $variation_combinations[] = $variation['attributes'];
            }
        }

        // Check each attribute to see if it appears in at least one variation
        foreach ($attribute_map as $attr_id => $attr) {
            $found_in_variation = false;

            foreach ($variation_combinations as $combination) {
                if (isset($combination[$attr['options_id']]) &&
                    $combination[$attr['options_id']] == $attr['values_id']) {
                    $found_in_variation = true;
                    break;
                }
            }

            if (!$found_in_variation) {
                $issues[] = [
                    'type' => 'missing_variation',
                    'name' => "{$attr['options_name']}: {$attr['values_name']}",
                    'attribute_id' => $attr_id,
                    'message' => "Attribute '{$attr['options_name']}: {$attr['values_name']}' has no corresponding product variation"
                ];
            }
        }

        return $issues;
    }

    /**
     * Check whether the default attributes can all be displayed at once
     * (i.e., if selecting one default will hide another due to dependencies)
     *
     * @param array $attribute_map The attribute map
     * @return array Array of default conflict issues
     */
    private function check_default_conflicts($attribute_map) {
        $issues = [];

        // Get all default attributes
        $default_attributes = $this->default_attributes;

        if (empty($default_attributes) || count($default_attributes) < 2) {
            return $issues; // No conflicts possible with 0 or 1 defaults
        }

        // Build a map of default attribute IDs for quick lookup
        $default_attr_ids = [];
        foreach ($attribute_map as $attr_id => $attr) {
            if (isset($default_attributes[$attr['options_id']]) &&
                $default_attributes[$attr['options_id']] == $attr['values_id']) {
                $default_attr_ids[$attr_id] = $attr;
            }
        }

        // Check each default attribute for dependencies that conflict with other defaults
        foreach ($default_attr_ids as $attr_id => $attr) {
            if (tep_not_null($attr['dependson_options_id']) && tep_not_null($attr['dependson_options_values_id'])) {
                // This default attribute has a dependency
                $dependency_satisfied = false;

                // Check if the dependency is satisfied by another default attribute
                foreach ($default_attr_ids as $check_attr_id => $check_attr) {
                    if ($check_attr['options_id'] == $attr['dependson_options_id'] &&
                        (strpos($attr['dependson_options_values_id'], $check_attr['values_id']) !== false)) {
                        $dependency_satisfied = true;
                        break;
                    }
                }

                if (!$dependency_satisfied) {
                    // Find what the dependency actually requires
                    $required_values = explode(',', $attr['dependson_options_values_id']);
                    $required_names = [];

                    foreach ($required_values as $required_value_id) {
                        $required_value_id = trim($required_value_id);
                        foreach ($attribute_map as $map_attr) {
                            if ($map_attr['options_id'] == $attr['dependson_options_id'] &&
                                $map_attr['values_id'] == $required_value_id) {
                                $required_names[] = $map_attr['values_name'];
                                break;
                            }
                        }
                    }

                    $required_options_name = '';
                    foreach ($attribute_map as $map_attr) {
                        if ($map_attr['options_id'] == $attr['dependson_options_id']) {
                            $required_options_name = $map_attr['options_name'];
                            break;
                        }
                    }

                    $issues[] = [
                        'type' => 'default_conflict',
                        'name' => "{$attr['options_name']}: {$attr['values_name']}",
                        'attribute_id' => $attr_id,
                        'message' => "Default attribute '{$attr['options_name']}: {$attr['values_name']}' depends on '{$required_options_name}: " . implode(' or ', $required_names) . "' but the current default for that option conflicts with this dependency"
                    ];
                }
            }
        }

        return $issues;
    }

    /**
     * Check for attributes that depend on items from their own option group
     *
     * @param array $attribute_map The attribute map
     * @return array Array of self option group dependency issues
     */
    private function check_self_option_group_dependencies($attribute_map) {
        $issues = [];

        foreach ($attribute_map as $attr_id => $attr) {
            // Check if this attribute has a dependency
            if (tep_not_null($attr['dependson_options_id']) && tep_not_null($attr['dependson_options_values_id'])) {
                // Check if the dependency is on the same option group as the attribute itself
                if ($attr['dependson_options_id'] == $attr['options_id']) {
                    $issues[] = [
                        'type' => 'self_option_group_dependency',
                        'name' => "{$attr['options_name']}: {$attr['values_name']}",
                        'attribute_id' => $attr_id,
                        'message' => "Attribute '{$attr['options_name']}: {$attr['values_name']}' depends on another item from its own option group '{$attr['options_name']}'. Items cannot depend on other items from the same option group."
                    ];
                }
            }
        }

        return $issues;
    }

    /**
     * Check for variations that reference missing attributes
     *
     * @param array $attribute_map The attribute map
     * @return array Array of missing attribute issues in variations
     */
    private function check_variations_missing_attributes($attribute_map) {
        $issues = [];

        // Only check if product has variations
        if (!$this->has_variations) {
            return $issues;
        }

        // Get all variations for this product
        $variations = $this->get_variations();

        foreach ($variations as $variation) {
            if (!empty($variation['attributes'])) {
                foreach ($variation['attributes'] as $option_id => $value_id) {
                    // Check if this attribute combination exists in the attributes table
                    $found = false;
                    foreach ($attribute_map as $attr_id => $attr) {
                        if ($attr['options_id'] == $option_id && $attr['values_id'] == $value_id) {
                            $found = true;
                            break;
                        }
                    }

                    if (!$found) {
                        // Try to get option and value names from database
                        $option_name = $this->options_name($option_id) ?: "Option ID: $option_id";
                        $value_name = $this->values_name($value_id) ?: "Value ID: $value_id";

                        $issues[] = [
                            'type' => 'variation_missing_attribute',
                            'name' => "Variation: {$variation['model']}",
                            'attribute_id' => 'variation_' . $variation['products_variations_id'],
                            'message' => "Variation '{$variation['model']}' references missing attribute '{$option_name}: {$value_name}'"
                        ];
                    }
                }
            }
        }

        return $issues;
    }

    /**
     * Check for duplicate model numbers in variations
     *
     * @return array Array of duplicate model issues
     */
    private function check_variations_duplicate_models() {
        $issues = [];

        // Only check if product has variations
        if (!$this->has_variations) {
            return $issues;
        }

        // Get all variations for this product
        $variations = $this->get_variations();
        $model_counts = [];
        $model_variations = [];

        // Count occurrences of each model
        foreach ($variations as $variation) {
            $model = trim($variation['model']);
            if (!empty($model)) {
                if (!isset($model_counts[$model])) {
                    $model_counts[$model] = 0;
                    $model_variations[$model] = [];
                }
                $model_counts[$model]++;
                $model_variations[$model][] = $variation;
            }
        }

        // Find duplicates
        foreach ($model_counts as $model => $count) {
            if ($count > 1) {
                foreach ($model_variations[$model] as $variation) {
                    $issues[] = [
                        'type' => 'variation_duplicate_model',
                        'name' => "Variation: {$variation['model']}",
                        'attribute_id' => 'variation_' . $variation['products_variations_id'],
                        'message' => "Variation '{$variation['model']}' has a duplicate model number (appears {$count} times)"
                    ];
                }
            }
        }

        return $issues;
    }

    /**
     * Check for Autodesk link issues in variations
     *
     * @return array Array of Autodesk link issues
     */
    private function check_variations_autodesk_links() {
        $issues = [];

        // Only check if product has variations
        if (!$this->has_variations) {
            return $issues;
        }

        // Get all variations for this product
        $variations = $this->get_variations();

        foreach ($variations as $variation) {
            // Check if this is an Autodesk product (manufacturers_id = 15)
            if ($variation['manufacturers_id'] == 15) {
                $autodesk_hash = trim($variation['autodesk_catalog_unique_hash']);

                if (empty($autodesk_hash) || $autodesk_hash === 'NULL') {
                    $issues[] = [
                        'type' => 'variation_missing_autodesk_link',
                        'name' => "Variation: {$variation['model']}",
                        'attribute_id' => 'variation_' . $variation['products_variations_id'],
                        'message' => "Autodesk variation '{$variation['model']}' is missing Autodesk catalog link"
                    ];
                } else {
                    // Check if the Autodesk link is valid (exists in catalog)
                    $catalog_check = tep_db_query("SELECT id FROM products_autodesk_catalog WHERE unique_hash = '" . tep_db_input($autodesk_hash) . "' LIMIT 1");
                    if (tep_db_num_rows($catalog_check) == 0) {
                        $issues[] = [
                            'type' => 'variation_invalid_autodesk_link',
                            'name' => "Variation: {$variation['model']}",
                            'attribute_id' => 'variation_' . $variation['products_variations_id'],
                            'message' => "Autodesk variation '{$variation['model']}' has invalid Autodesk catalog link: {$autodesk_hash}"
                        ];
                    }
                }
            }
        }

        return $issues;
    }

    /**
     * Check for variations selection issues (enabled variations with disabled attributes)
     *
     * @param array $attribute_map The attribute map
     * @return array Array of variation selection issues
     */
    private function check_variations_selection_issues($attribute_map) {
        $issues = [];

        // Only check if product has variations
        if (!$this->has_variations) {
            return $issues;
        }

        // Get all variations for this product
        $variations = $this->get_variations();

        foreach ($variations as $variation) {
            // Check if variation is enabled but has disabled attributes
            if ($variation['enabled'] == 1 && !empty($variation['attributes'])) {
                $disabled_attributes = [];

                foreach ($variation['attributes'] as $option_id => $value_id) {
                    // Check if this attribute is enabled in the attributes table
                    $attribute_enabled = false;
                    foreach ($attribute_map as $attr_id => $attr) {
                        if ($attr['options_id'] == $option_id && $attr['values_id'] == $value_id) {
                            // Check if attribute is enabled (you may need to adjust this based on your enabled field logic)
                            $attribute_enabled = true;
                            break;
                        }
                    }

                    if (!$attribute_enabled) {
                        $option_name = $this->options_name($option_id) ?: "Option ID: $option_id";
                        $value_name = $this->values_name($value_id) ?: "Value ID: $value_id";
                        $disabled_attributes[] = "{$option_name}: {$value_name}";
                    }
                }

                if (!empty($disabled_attributes)) {
                    $issues[] = [
                        'type' => 'variation_selection_issue',
                        'name' => "Variation: {$variation['model']}",
                        'attribute_id' => 'variation_' . $variation['products_variations_id'],
                        'message' => "Enabled variation '{$variation['model']}' contains disabled attributes: " . implode(', ', $disabled_attributes)
                    ];
                }
            }
        }

        return $issues;
    }

    /**
     * Insert or update a product attribute
     *
     * @param int $attributes_options_id The options ID
     * @param int $attributes_values_id The values ID
     * @param int $attributes_attribute_default Whether this is the default attribute (0 or 1)
     * @param int $attributes_dependson_options_id The dependency options ID
     * @param int $attributes_dependson_values_id The dependency values ID
     * @param int|null $attributes_id The existing attribute ID for updates, null for inserts
     * @return array The inserted/updated attribute data
     */
    public function insert_or_update_attribute($attributes_options_id, $attributes_values_id, $attributes_attribute_default, $attributes_dependson_options_id, $attributes_dependson_values_id, $attributes_id = null) {
        // Sanitize input values - convert empty strings and null to appropriate database values

        print_rr(
            [
                $attributes_options_id,
                $attributes_values_id,
                $attributes_attribute_default,
                $attributes_dependson_options_id,
                $attributes_dependson_values_id,
                $attributes_id
            ],'insert_or_update_attribute');

        if ( ($attributes_options_id == '' || $attributes_options_id == null || $attributes_values_id == '' || $attributes_values_id == null) ) {
            print_rr([
                'attributes_options_id' => $attributes_options_id,
                'attributes_values_id' => $attributes_values_id,
                'attributes_attribute_default' => $attributes_attribute_default,
                'attributes_dependson_options_id' => $attributes_dependson_options_id,
                'attributes_dependson_values_id' => $attributes_dependson_values_id,
                'attributes_id' => $attributes_id
            ],
                'insert_or_update_attribute empty error'
            );
            return [];
        }

        // Check if this is an update operation
        if ($attributes_id && is_numeric($attributes_id)) {
            print_rr(['operation' => 'UPDATE', 'attributes_id' => $attributes_id], 'operation_type');
        } else {
            print_rr(['operation' => 'INSERT', 'attributes_id' => $attributes_id], 'operation_type');
        }

        // Properly format values for SQL - no quotes around NULL or integers
        $attributes_options_id_sql = (int)$attributes_options_id;
        $attributes_values_id_sql = (int)$attributes_values_id;
        $attributes_id_sql = ($attributes_id != '' && $attributes_id != null) ? (int)$attributes_id : 'NULL';
        $attributes_attribute_default_sql = (int)$attributes_attribute_default;
        $attributes_dependson_options_id_sql = ($attributes_dependson_options_id != '' && $attributes_dependson_options_id != null) ? (int)$attributes_dependson_options_id : 'NULL';
        $attributes_dependson_values_id_sql = ($attributes_dependson_values_id != '' && $attributes_dependson_values_id != null) ? "'" . tep_db_input($attributes_dependson_values_id) . "'" : 'NULL';

        print_rr([
            'attributes_options_id_sql' => $attributes_options_id_sql,
            'attributes_values_id_sql' => $attributes_values_id_sql,
            'attributes_id_sql' => $attributes_id_sql,
            'attributes_attribute_default_sql' => $attributes_attribute_default_sql,
            'attributes_dependson_options_id_sql' => $attributes_dependson_options_id_sql,
            'attributes_dependson_values_id_sql' => $attributes_dependson_values_id_sql
        ], 'sql_variables');

        // Build the INSERT ... ON DUPLICATE KEY UPDATE query
        $query_sql = "INSERT INTO `products_attributes` (
                `products_attributes_id`,
                `products_id`,
                `options_id`,
                `options_values_id`,
                `attribute_default`,
                `dependson_options_id`,
                `dependson_options_values_id`
        ) VALUES (
                {$attributes_id_sql},
                {$this->products_id},
                {$attributes_options_id_sql},
                {$attributes_values_id_sql},
                {$attributes_attribute_default_sql},
                {$attributes_dependson_options_id_sql},
                {$attributes_dependson_values_id_sql}
        ) ON DUPLICATE KEY UPDATE
                `products_id` = VALUES(`products_id`),
                `options_id` = VALUES(`options_id`),
                `options_values_id` = VALUES(`options_values_id`),
                `attribute_default` = VALUES(`attribute_default`),
                `dependson_options_id` = VALUES(`dependson_options_id`),
                `dependson_options_values_id` = VALUES(`dependson_options_values_id`)
        ";
        print_rr($query_sql, 'finalq');
        // Execute the query
        $clean_query = str_replace(["\n","\r","\t"], "", $query_sql);
        $result = tep_db_query($clean_query);
        $rows = tep_db_affected_rows($result);

        print_rr(['affected_rows' => $rows, 'attributes_id_before' => $attributes_id, 'attributes_id_sql' => $attributes_id_sql], 'query_result');

        // Get the attribute ID if it was an insert
        if ($attributes_id_sql === 'NULL' || !is_numeric($attributes_id)) {
            $attributes_id = tep_db_insert_id();
            print_rr(['new_insert_id' => $attributes_id], 'insert_id');
        }

        // Fetch the attribute directly from database to ensure we have all required fields
        global $languages_id;
        if (!isset($languages_id)) {
            $languages_id = 1;
        }

        $attribute_query = tep_db_query("SELECT pa.*, po.products_options_name, pov.products_options_values_name
                                        FROM products_attributes pa
                                        JOIN products_options po ON pa.options_id = po.products_options_id
                                        JOIN products_options_values pov ON pa.options_values_id = pov.products_options_values_id
                                        WHERE pa.products_attributes_id = " . (int)$attributes_id . "
                                        AND po.language_id = " . (int)$languages_id . "
                                        AND pov.language_id = " . (int)$languages_id);

        if (tep_db_num_rows($attribute_query) > 0) {
            $db_attribute = tep_db_fetch_array($attribute_query);
            // Create the attribute array with all required fields
            $final_attribute = [
                'products_attributes_id' => $db_attribute['products_attributes_id'],
                'products_options_id' => $db_attribute['options_id'],
                'products_options_values_id' => $db_attribute['options_values_id'],
                'products_options_name' => $db_attribute['products_options_name'],
                'products_options_values_name' => $db_attribute['products_options_values_name'],
                'attribute_default' => $db_attribute['attribute_default'],
                'dependson_options_id' => $db_attribute['dependson_options_id'],
                'dependson_options_values_id' => $db_attribute['dependson_options_values_id'],
                'products_attributes_sort_order' => $db_attribute['products_attributes_sort_order'] ?? 0,
                'enabled' => 1
            ];
            print_rr(['final_attribute' => $final_attribute, 'attributes_id' => $attributes_id], 'final_result');
            return $final_attribute;
        } else {
            print_rr(['error' => 'Could not find inserted attribute', 'attributes_id' => $attributes_id], 'final_error');
            return null;
        }
    }
}