module_login_title = Log In with PayPal
module_login_short_title = Log In

module_login_legacy_admin_app_button = Manage App

module_login_introduction = <ul>
  <li>Accept credit cards and PayPal on your online store.</li>
  <li>Simplified PCI compliance standards.</li>
  <li>Accept multiple currencies worldwide.</li>
  <li>Optimized mobile checkout experience.</li>
</ul>

<p>PayPal handles the payment acceptance experience and returns the customer to your store after payment has been made.</p>
<p>For new buyers, signing up for a PayPal account is optional meaning customers can complete their payments first and then decide to save their information in a PayPal account for future purchases.</p>

module_login_error_curl = This module requires cURL to be enabled in PHP and will not load until it has been enabled on this webserver.
module_login_error_credentials = This module will not load until the Client ID and Secret parameters have been configured. Please edit and configure the settings of the PayPal App.

module_login_notice_paypal_app_return_url = The following url must be configured as your Return URL in the PayPal REST App settings that was created for Log In with PayPal:<br /><br />:return_url

module_login_template_title = Log In with PayPal
module_login_template_content = Have a PayPal account? Securely log in with PayPal to shop even faster!
module_login_template_sandbox_alert = Test Mode: The Sandbox server is currently selected.
module_login_language_locale = en-us
