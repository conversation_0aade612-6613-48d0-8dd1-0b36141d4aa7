/* user.css ----------------------------------------------------------------- */

@keyframes cart_pulse {
    0% {
     transform: scale(1, 1);
    }

    50% {
     transform: scale(1.3, 1.3);
    }

    100% {
    transform: scale(1, 1);
    }
}

.cart_pulse {
    animation: cart_pulse 2s ease-in-out infinite;
}


@keyframes checkout_bounce {
    0%,
    20%,
    50%,
    80%,
    100% {
        transform: translateX(0);
    }

    40% {
        transform: translateX(30px);
    }

    60% {
        transform: translateX(15px);
    }
}

.checkout_bounce {
    animation: checkout_bounce 2s ease-in-out infinite;
}





body{	
	padding-top: 60px !important;
    background: #ddd !important;
	font-family: Arial,sans-serif;
}

.panel-default > .panel-heading {
    background-color: #27a9cb;
   /* border-color: #ddd;*/
    color: #fff;
}
.panel-default > .panel-heading a {
    background-color: #27a9cb;
  /*  border-color: #ddd;*/
    color: #fff;
}
.piGal{width:100% !important; padding:10px;}
.piGal img {max-width:100% !important;}
/*
.piGal{max-width:100% !important;width: auto !important;}
.piGal a, .piGal img {
	width: auto !important;
	max-width: 100% !important;
}
*/
.sf-menu a {
    padding: 0.25em 0.5em;
}

.sf-with-ul {
    color: #880808;
    font-weight: bold;
}

.sf-menu, .sf-menu * {
    font-size: 1em;
    list-style: none outside none;
    margin: 0;
    padding: 0;
	color: #BB1111;
}

.sf-menu li, .sf-menu ul li {
	background: #FFF;  
  min-width: 148px; /* Make the top-level categories fit in the column width */
}

.videoWrapper {
	width:100%;
	/*padding:5%;*/
	margin:0 auto;
}

.vcenter {
    display: inline-block;
    vertical-align: middle;
  /*  float: none;*/
}

.header-breadcrumb {
      background-color: #27a9cb;
    border-radius: 4px;
    color: #fff;
    list-style: outside none none;
    margin: 8px 0;
    padding: 8px 0;
}
.header-breadcrumb a {
      background-color: #27a9cb;
    border-radius: 4px;
    color: #fff;
    list-style: outside none none;
    margin: 8px 0;
    padding: 8px 0;
	
}
.breadcrumb a{
	color: #777;
	background-color:transparent;
}
.spacer{padding-left:15px;}

.breadcrumb {
    background-color: #27a9cb;
    border-radius: 4px;
    color: #fff;
    list-style: outside none none;


    background-color: #27a9cb;
    border-radius: 4px;
    color: #fff;
    margin-bottom: 0;
    padding: 0;
    
}
.header-breadcrumb .breadcrumb  a{
	color: #fff;
}
#bodyWrapper {
    padding-top: 10px;
    background: #fff none repeat scroll 0 0 !important;
    box-shadow: 0 0 20px grey  !important;
    border-radius: 2px;
    margin-bottom: 10px;
	overflow-x: visible;
    }
.row-offcanvas-left {
    margin-right: 0px;
    margin-left: 0px;
}

.headerBanner, .storeLogo {
    #min-height:50px;display:flex;align-items:center;
}

.sidebarBannerWrapper {
      margin-bottom: 20px;  
}
    
/*
.navbar-default .navbar-nav > li > a {
    color: #777;
}
.navbar-nav > . {
    padding-bottom: 15px;
    padding-top: 15px;
}
.navbar-nav > li > a {
    line-height: 20px;
    padding-bottom: 10px;
    padding-top: 10px;
}
.nav > li > a {
    display: block;
    padding: 10px 15px;
    position: relative;
}*/

.productsPrice{padding-bottom:10px}

#productInfoPrice {
    font-size: 26px;
    font-weight: bold;
    padding: 10px;
}
.productSpecialPrice {
    color: red;
}
.page-header {
    border-bottom: 1px solid #eee;
    margin: 0px 0 0px;
    padding-bottom: 9px;
	min-height:65px;
}

.h1, h1{ font-size:24px;}
.h2, h2{ font-size:20px;}
.h3, h3{ font-size:18px;}
.h4, h4{ font-size:16px;}
.h5, h5{ font-size:14px;}
.h6, h6{ font-size:12px;}

.sf-vertical.sf-arrows > li > .sf-with-ul::after {
    margin-top: -4px;
    margin-right: -12px;
    border-color: transparent;
    border-left-color: #000;
    border-left-color: rgba(103,103,103,.5);
}
.sf-arrows ul .sf-with-ul::after {
    margin-top: -5px;
    margin-right: -3px;
    border-color: transparent;
    border-left-color: #dFeEFF;
    border-left-color: rgba(103,103,103,.5);
}

.sf-menu ul ul li {
    background: #fff;
}


/*no collapse*/

.navbar-collapse.collapse.off {
    display: block!important;
}
.navbar-collapse.collapse.off ul {
    margin: 0;
    padding: 0;
}

.navbar-nav.no-collapse>li,
.navbar-nav.no-collapse {
    float: left !important;
}

.navbar-right.no-collapse {
    float: right!important;
}
.header-breadcrumb a {background-color:transparent;}

#bodyWrapper {

   /* min-height: 1650px;*/
   padding-top: 10px;
background: #fff none repeat scroll 0 0 !important;
box-shadow: 0 0 20px grey !important;
border-radius: 2px;
margin-bottom: 10px;
overflow-x: visible;

}
.social-navbar-links{padding:10px;}

.navbar-toggle {
    position: relative;
    float: right;
    padding: 9px 10px;
    margin-top: 8px;
    margin-right: 15px;
    margin-bottom: 0px;
    background-color: transparent;
    background-image: none;
    border: none;
    border-top-color: transparent;
    border-right-color: transparent;
    border-bottom-color: transparent;
    border-left-color: transparent;
    border-radius: 4px;
}
@media screen and (min-width: 768px) {
	.navbar-toggle {
		display: block;
	}
	.subcat{
		display:none;
	}
	.sf-arrows .sf-with-ul:after{
		content:none;
	}
	columnLeft {
		max-width: 225px;
	}
	
}
@media screen and (min-width: 992px) {
	.navbar-toggle {
		display: none;}
}
.subcat {display:block;}

#attributes_values_id_btns_container {
  display: flex;
  border-collapse: collapse;
  width: 100%;
  gap: 5px;
  border-radius: unset;
  flex-direction: row;
  flex-wrap: wrap;
}

.product_attribute_btns {
	flex-grow: 4;
}

.variationsBottomWrapper {
	display:none;
}
.btn_debuttoned {
background: none;
  border: none;
  margin-top: 7px;
  cursor: pointer;
}

.products_subtotals{

    min-width: 65px;
    display:inline-block;
}
@media (max-width: 767px) {

    .text-left-not-xs,
    .text-center-not-xs,
    .text-right-not-xs,
    .text-justify-not-xs {
        text-align: inherit;
    }

    .text-left-xs {
        text-align: left;
    }

    .text-center-xs {
        text-align: center;
    }

    .text-right-xs {
        text-align: right;
    }

    .text-justify-xs {
        text-align: justify;
    }
}

@media (min-width: 768px) and (max-width: 991px) {

    .text-left-not-sm,
    .text-center-not-sm,
    .text-right-not-sm,
    .text-justify-not-sm {
        text-align: inherit;
    }

    .text-left-sm {
        text-align: left;
    }

    .text-center-sm {
        text-align: center;
    }

    .text-right-sm {
        text-align: right;
    }

    .text-justify-sm {
        text-align: justify;
    }
}

@media (min-width: 992px) and (max-width: 1199px) {

    .text-left-not-md,
    .text-center-not-md,
    .text-right-not-md,
    .text-justify-not-md {
        text-align: inherit;
    }

    .text-left-md {
        text-align: left;
    }

    .text-center-md {
        text-align: center;
    }

    .text-right-md {
        text-align: right;
    }

    .text-justify-md {
        text-align: justify;
    }
}

@media (min-width: 1200px) {

    .text-left-not-lg,
    .text-center-not-lg,
    .text-right-not-lg,
    .text-justify-not-lg {
        text-align: inherit;
    }

    .text-left-lg {
        text-align: left;
    }

    .text-center-lg {
        text-align: center;
    }

    .text-right-lg {
        text-align: right;
    }

    .text-justify-lg {
        text-align: justify;
    }
}

.footer {
    background: #eee;
    border-top: 2px solid #ddd;
}

.footer .footerbox {
    padding: 10px 10px 0px 10px;
}

.footer-extra {
    background: #111;
    color: silver;
    line-height: 3;
}

.footer-extra A {
    color: silver;
}

.footer h2 {
    padding: 0;
    margin: 0;
    font-size: 1em;
    letter-spacing: 0.1em;
    color: rgb(142, 11, 0);
    text-shadow: 1px 1px 1px rgba(255, 255, 255, 0.6);
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
    margin-bottom: 10px;
    text-transform: uppercase;
}

.contact {
    border-left: 1px solid #ddd;
}

.timeline {
    list-style: none;
    padding: 20px 0 20px;
    position: relative;
}

.timeline:before {
    top: 0;
    bottom: 0;
    position: absolute;
    content: " ";
    width: 3px;
    background-color: #eeeeee;
    right: 25px;
    margin-left: -1.5px;
}

.timeline>li {
    margin-bottom: 20px;
    position: relative;
}

.timeline>li:before,
.timeline>li:after {
    content: " ";
    display: table;
}

.timeline>li:after {
    clear: both;
}

.timeline>li:before,
.timeline>li:after {
    content: " ";
    display: table;
}

.timeline>li:after {
    clear: both;
}

.timeline>li>.timeline-panel {
    width: calc(100% - 75px);
    float: left;
    border: 1px solid #d4d4d4;
    border-radius: 2px;
    padding: 20px;
    position: relative;
    -webkit-box-shadow: 0 1px 6px rgba(0, 0, 0, 0.175);
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.175);
}

.timeline>li>.timeline-panel:before {
    position: absolute;
    top: 26px;
    right: -15px;
    display: inline-block;
    border-top: 15px solid transparent;
    border-left: 15px solid #ccc;
    border-right: 0 solid #ccc;
    border-bottom: 15px solid transparent;
    content: " ";
}

.timeline>li>.timeline-panel:after {
    position: absolute;
    top: 27px;
    right: -14px;
    display: inline-block;
    border-top: 14px solid transparent;
    border-left: 14px solid #fff;
    border-right: 0 solid #fff;
    border-bottom: 14px solid transparent;
    content: " ";
}

.timeline>li>.timeline-badge {
    color: #fff;
    width: 50px;
    height: 50px;
    line-height: 50px;
    font-size: 1.4em;
    text-align: center;
    position: absolute;
    top: 16px;
    right: 0px;
    margin-left: -25px;
    background-color: #999999;
    z-index: 100;
    border-top-right-radius: 50%;
    border-top-left-radius: 50%;
    border-bottom-right-radius: 50%;
    border-bottom-left-radius: 50%;
}

.timeline>li.timeline-inverted>.timeline-panel {
    float: right;
}

.timeline>li.timeline-inverted>.timeline-panel:before {
    border-left-width: 0;
    border-right-width: 15px;
    left: -15px;
    right: auto;
}

.timeline>li.timeline-inverted>.timeline-panel:after {
    border-left-width: 0;
    border-right-width: 14px;
    left: -14px;
    right: auto;
}

.timeline-badge.primary {
    background-color: #2e6da4 !important;
}

.timeline-badge.success {
    background-color: #3f903f !important;
}

.timeline-badge.warning {
    background-color: #f0ad4e !important;
}

.timeline-badge.danger {
    background-color: #d9534f !important;
}

.timeline-badge.info {
    background-color: #5bc0de !important;
}

.timeline-title {
    margin-top: 0;
    color: inherit;
}

.timeline-body>p .timeline-body>ul {
    margin-bottom: 0;
}

.timeline-body>p+p {
    margin-top: 5px;
}

.timeline-body blockquote {
    font-size: 1em;
}

.navbar-no-corners {
    border-radius: 0 !important;
    -moz-border-radius: 0 !important;
    border-left: none;
    border-right: none;
}

.navbar-no-margin {
    margin-bottom: 0 !important;
}

.searchbox-margin {
    margin-top: 10px;
    margin-bottom: 10px;
}

.form-control-feedback {
    padding-right: 2%;
}



@media (max-width: 767px) {
    .dropdown.shopping_cart_dropdown.open {
        display: grid;
        max-width: 100%;
        overflow: clip;
        text-overflow: ellipsis;
    }
   
      #shopping_cart_dropdown {    
        height: 0;
        transition: height 2s;
      }
      
       .open > #shopping_cart_dropdown {
        height: auto;
        transition: height 2s;
      }
    .dropdown.shopping_cart_dropdown.open > li > a{
        max-width: 100%;
        overflow: clip;
        text-wrap: wrap;
        padding-left: 2em;
        text-indent: -1em;
    }
    .navbar-text {
        padding-left: 15px !important;
    }
}