<?php
/*
  $Id$
  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com
  Copyright (c) 2014 osCommerce
  Released under the GNU General Public License
*/
  $oscTemplate->buildBlocks();
  if (!$oscTemplate->hasBlocks('boxes_column_left')) {
    $oscTemplate->setGridContentWidth($oscTemplate->getGridContentWidth() + $oscTemplate->getGridColumnWidth());
 }
  if (!$oscTemplate->hasBlocks('boxes_column_right')) {
    $oscTemplate->setGridContentWidth($oscTemplate->getGridContentWidth() + $oscTemplate->getGridColumnWidth());
 }
?>
<!DOCTYPE html>
<html <?php echo HTML_PARAMS;?>>
<head>
<meta charset="<?php echo CHARSET;?>">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<title><?php echo tep_output_string_protected($oscTemplate->getTitle());?></title>
<base href="<?php echo (($request_type == 'SSL') ? HTTPS_SERVER : HTTP_SERVER) . DIR_WS_CATALOG;?>">
 <style>#bodyContent{opacity: 0;}</style> 

<link href="/min/g=css.css&hash=10145497" rel="stylesheet">

<?php
/*
<link href="ext/bootstrap/css/bootstrap.min.css" rel="stylesheet"> 
?>

<script defer src="https://use.fontawesome.com/releases/v5.0.6/js/all.js"></script>
<link href="custom.css" rel="stylesheet">
<link href="user.css" rel="stylesheet">
<!--[if lt IE 9]>
   <script src="ext/js/html5shiv.js"></script>
   <script src="ext/js/respond.min.js"></script>
   <script src="ext/js/excanvas.min.js"></script>
<![endif]-->
 
<script src="ext/jquery/jquery-3.1.1.min.js"></script>
*/
?>

<?php echo $oscTemplate->getBlocks('header_tags');?>
</head>
<body>
  <?php echo $oscTemplate->getContent('navigation');?>
  
  <div id="bodyWrapper" class="<?php echo BOOTSTRAP_CONTAINER;?>">
    <div class="row">

      <?php require('includes/header.php');?>

      <div class="row row-offcanvas row-offcanvas-left">
	 <div id="bodyContent" class="col-md-<?php echo $oscTemplate->getGridContentWidth(); ?> <?php echo ($oscTemplate->hasBlocks('boxes_column_left') ? 'col-md-push-' . $oscTemplate->getGridColumnWidth() : ''); ?>"> 
 <?php /*<div class="alert alert-danger"><p>TCS will close for the Christmas and New year holidays at 12pm on Friday 20th December 2024 and will re-open on Thursday the 2nd January 2025.  All orders placed after 11am on the 20th of December 2024 may not be processed until the 2nd January 2025.</p><p>If you require assistance during this period please consult the relevent support links on our <a href="https://www.cadservices.co.uk/support.html">support page</a>.</div> */ ?>