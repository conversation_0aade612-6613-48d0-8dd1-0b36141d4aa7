@echo off
echo ========================================
echo Simple SSL Certificate Generation
echo ========================================
echo.

set XAMPP_PATH=E:\tools\xampp
set OPENSSL_PATH=%XAMPP_PATH%\apache\bin\openssl.exe
set CERT_DIR=%XAMPP_PATH%\apache\conf\ssl.crt
set KEY_DIR=%XAMPP_PATH%\apache\conf\ssl.key

echo Creating certificate directories...
if not exist "%CERT_DIR%" mkdir "%CERT_DIR%"
if not exist "%KEY_DIR%" mkdir "%KEY_DIR%"

echo.
echo Generating SSL certificate...
echo.

REM Change to XAMPP Apache bin directory
cd /d "%XAMPP_PATH%\apache\bin"

REM Create a temporary OpenSSL config file
echo [req] > temp_openssl.cnf
echo distinguished_name = req_distinguished_name >> temp_openssl.cnf
echo req_extensions = v3_req >> temp_openssl.cnf
echo prompt = no >> temp_openssl.cnf
echo. >> temp_openssl.cnf
echo [req_distinguished_name] >> temp_openssl.cnf
echo C = GB >> temp_openssl.cnf
echo ST = Local >> temp_openssl.cnf
echo L = Local >> temp_openssl.cnf
echo O = Local Development >> temp_openssl.cnf
echo OU = IT Department >> temp_openssl.cnf
echo CN = localhost.cadservices >> temp_openssl.cnf
echo. >> temp_openssl.cnf
echo [v3_req] >> temp_openssl.cnf
echo keyUsage = keyEncipherment, dataEncipherment >> temp_openssl.cnf
echo extendedKeyUsage = serverAuth >> temp_openssl.cnf
echo subjectAltName = @alt_names >> temp_openssl.cnf
echo. >> temp_openssl.cnf
echo [alt_names] >> temp_openssl.cnf
echo DNS.1 = localhost.cadservices >> temp_openssl.cnf
echo DNS.2 = localhost >> temp_openssl.cnf
echo IP.1 = 127.0.0.1 >> temp_openssl.cnf

REM Generate the certificate using the temporary config
openssl req -x509 -nodes -days 365 -newkey rsa:2048 ^
    -keyout "%KEY_DIR%\localhost.key" ^
    -out "%CERT_DIR%\localhost.crt" ^
    -config temp_openssl.cnf

REM Clean up temporary config file
del temp_openssl.cnf

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo SSL Certificate generated successfully!
    echo ========================================
    echo Certificate: %CERT_DIR%\localhost.crt
    echo Private Key: %KEY_DIR%\localhost.key
    echo.
    echo The certificate includes:
    echo - Common Name: localhost.cadservices
    echo - Subject Alternative Names: localhost.cadservices, localhost, 127.0.0.1
    echo - Valid for: 365 days
    echo.
    echo Next steps:
    echo 1. Restart Apache in XAMPP Control Panel
    echo 2. Visit https://localhost.cadservices
    echo 3. Accept the security warning in your browser
    echo.
) else (
    echo.
    echo ========================================
    echo ERROR: Failed to generate SSL certificate
    echo ========================================
    echo.
    echo Troubleshooting:
    echo 1. Make sure XAMPP is installed at: %XAMPP_PATH%
    echo 2. Check if OpenSSL exists at: %OPENSSL_PATH%
    echo 3. Try running XAMPP Control Panel as Administrator
    echo.
)

echo Press any key to continue...
pause >nul
