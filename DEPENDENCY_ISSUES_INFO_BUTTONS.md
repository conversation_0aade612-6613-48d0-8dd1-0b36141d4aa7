# Dependency Issues with Info Buttons

This enhancement adds detailed explanations and resolution steps to the dependency issue checking system. Each issue now includes an info button (ℹ️) that provides comprehensive help for resolving the problem.

## Features Added

### 1. Info Buttons
- **Visual indicator**: Blue info button (ℹ️) next to each issue
- **Hover tooltip**: Shows "Click for detailed explanation and resolution steps"
- **Click action**: Opens a modal with detailed information

### 2. Detailed Explanations
Each issue type now includes:
- **What this means**: Clear explanation of the problem
- **How to resolve**: Step-by-step resolution instructions
- **Visual indicators**: Emoji icons for different issue types

### 3. Interactive Modals
- **Alpine.js powered**: Smooth transitions and animations
- **Responsive design**: Works on desktop and mobile
- **Keyboard support**: ESC key to close, focus management
- **Click outside to close**: User-friendly interaction

## Issue Types Supported

### Attribute Issues
- 🔗 **Missing Dependencies**: Attribute depends on non-existent attribute
- 🔄 **Circular Dependencies**: Attributes depend on each other in a loop
- 📊 **Sort Order Violations**: Dependencies appear after dependent attributes
- 🎯 **Missing Variations**: Attributes without corresponding variations
- ⚠️ **Default Conflicts**: Default attributes with conflicting dependencies
- 🚫 **Self Option Group Dependencies**: Attributes depending on same option group

### Variation Issues
- ❌ **Missing Attributes**: Variations referencing non-existent attributes
- 📋 **Duplicate Model Numbers**: Multiple variations with same model
- 🔗 **Missing Autodesk Links**: Autodesk products without catalog links
- ❌ **Invalid Autodesk Links**: Autodesk links that don't exist in catalog
- ⚡ **Selection Issues**: Enabled variations with disabled attributes

## Files Modified

### 1. `includes/functions/tcs_attributes_components.php`
- Added `get_dependency_issue_explanation()` function
- Enhanced `tcs_draw_attributes_dependency_warnings()` function
- Added `tcs_draw_issue_info_modal()` function

### 2. `includes/css/dependency-issues.css` (New)
- Styling for info buttons and modals
- Responsive design rules
- Animation and transition effects
- Issue type indicators

### 3. `test_dependency_issues_with_info.php` (New)
- Test page to demonstrate the functionality
- Shows all issue types and their explanations
- Includes debugging information

## Usage

### In Product Edit Interface
The info buttons automatically appear when dependency issues are detected:

```php
// This is automatically called when displaying attributes
$products_attributes = new tcs_product_attributes($product_id);
echo tcs_draw_attributes_dependency_warnings($products_attributes);
```

### Standalone Usage
You can also use the functions independently:

```php
// Get explanation for a specific issue type
$explanation = get_dependency_issue_explanation('missing_dependency');

// Create a modal for an issue
$modal_html = tcs_draw_issue_info_modal('modal_id', $explanation);
```

## Testing

1. **Test Page**: Visit `test_dependency_issues_with_info.php?product_id=1`
2. **Product Edit**: Go to any product edit page with dependency issues
3. **Different Products**: Test with various product IDs to see different issue types

## Browser Requirements

- **Alpine.js**: Basic version (no plugins required)
- **Font Awesome**: For icons (info, close, etc.)
- **Modern Browser**: CSS Grid and Flexbox support
- **JavaScript Enabled**: Required for modal functionality

## Fixed Issues

### Alpine.js Focus Plugin Warning
- **Issue**: `x-trap` directive required the Focus plugin
- **Fix**: Replaced with `x-init` and basic focus management
- **Result**: Works with standard Alpine.js CDN without additional plugins

## Customization

### Adding New Issue Types
1. Add explanation to `get_dependency_issue_explanation()` function
2. Add CSS class for visual indicator in `dependency-issues.css`
3. Update documentation

### Styling Changes
- Modify `includes/css/dependency-issues.css`
- Update modal structure in `tcs_draw_issue_info_modal()`
- Adjust button styling in `tcs_draw_attributes_dependency_warnings()`

## Benefits

1. **Better User Experience**: Clear explanations instead of cryptic error messages
2. **Faster Problem Resolution**: Step-by-step instructions for each issue
3. **Reduced Support Requests**: Users can self-resolve common issues
4. **Visual Clarity**: Icons and styling make issues easier to identify
5. **Responsive Design**: Works on all devices and screen sizes

## Future Enhancements

- **Video Tutorials**: Links to video explanations for complex issues
- **Auto-Fix Suggestions**: Buttons to automatically resolve simple issues
- **Issue Severity Levels**: Color-coding for critical vs. warning issues
- **Bulk Actions**: Resolve multiple issues at once
- **Issue History**: Track resolved issues over time
